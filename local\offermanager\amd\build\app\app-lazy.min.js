define("local_offermanager/app/app-lazy",["core/config","tool_lfxp/ajax","core/notification"],function(Vg,Rg,Fg){"use strict";function Ug(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const s in e)if(s!=="default"){const i=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,i.get?i:{enumerable:!0,get:()=>e[s]})}}return t.default=e,Object.freeze(t)}const Lg=Ug(Vg);/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function or(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const et={}.NODE_ENV!=="production"?Object.freeze({}):{},Dn={}.NODE_ENV!=="production"?Object.freeze([]):[],Dt=()=>{},Bg=()=>!1,oo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ci=e=>e.startsWith("onUpdate:"),ft=Object.assign,Ja=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},qg=Object.prototype.hasOwnProperty,Ke=(e,t)=>qg.call(e,t),he=Array.isArray,Zr=e=>io(e)==="[object Map]",In=e=>io(e)==="[object Set]",Vc=e=>io(e)==="[object Date]",Se=e=>typeof e=="function",at=e=>typeof e=="string",Is=e=>typeof e=="symbol",Ze=e=>e!==null&&typeof e=="object",Ya=e=>(Ze(e)||Se(e))&&Se(e.then)&&Se(e.catch),Rc=Object.prototype.toString,io=e=>Rc.call(e),Xa=e=>io(e).slice(8,-1),Fc=e=>io(e)==="[object Object]",el=e=>at(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ao=or(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Hg=or("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),di=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},$g=/-(\w)/g,jt=di(e=>e.replace($g,(t,s)=>s?s.toUpperCase():"")),Wg=/\B([A-Z])/g,Dr=di(e=>e.replace(Wg,"-$1").toLowerCase()),Jr=di(e=>e.charAt(0).toUpperCase()+e.slice(1)),Yr=di(e=>e?`on${Jr(e)}`:""),Ir=(e,t)=>!Object.is(e,t),Nn=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},lo=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},fi=e=>{const t=parseFloat(e);return isNaN(t)?e:t},jg=e=>{const t=at(e)?Number(e):NaN;return isNaN(t)?e:t};let Uc;const uo=()=>Uc||(Uc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function as(e){if(he(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=at(i)?Qg(i):as(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(at(e)||Ze(e))return e}const zg=/;(?![^(]*\))/g,Gg=/:([^]+)/,Kg=/\/\*[^]*?\*\//g;function Qg(e){const t={};return e.replace(Kg,"").split(zg).forEach(s=>{if(s){const i=s.split(Gg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function ce(e){let t="";if(at(e))t=e;else if(he(e))for(let s=0;s<e.length;s++){const i=ce(e[s]);i&&(t+=i+" ")}else if(Ze(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Zg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Jg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Yg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Xg=or(Zg),e_=or(Jg),t_=or(Yg),s_=or("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Lc(e){return!!e||e===""}function r_(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=co(e[i],t[i]);return s}function co(e,t){if(e===t)return!0;let s=Vc(e),i=Vc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Is(e),i=Is(t),s||i)return e===t;if(s=he(e),i=he(t),s||i)return s&&i?r_(e,t):!1;if(s=Ze(e),i=Ze(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!co(e[u],t[u]))return!1}}return String(e)===String(t)}function tl(e,t){return e.findIndex(s=>co(s,t))}const Bc=e=>!!(e&&e.__v_isRef===!0),q=e=>at(e)?e:e==null?"":he(e)||Ze(e)&&(e.toString===Rc||!Se(e.toString))?Bc(e)?q(e.value):JSON.stringify(e,qc,2):String(e),qc=(e,t)=>Bc(t)?qc(e,t.value):Zr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[sl(i,a)+" =>"]=n,s),{})}:In(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>sl(s))}:Is(t)?sl(t):Ze(t)&&!he(t)&&!Fc(t)?String(t):t,sl=(e,t="")=>{var s;return Is(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let es;class Hc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=es,!t&&es&&(this.index=(es.scopes||(es.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=es;try{return es=this,t()}finally{es=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){++this._on===1&&(this.prevScope=es,es=this)}off(){this._on>0&&--this._on===0&&(es=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function n_(e){return new Hc(e)}function o_(){return es}let tt;const rl=new WeakSet;class $c{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,es&&es.active&&es.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,rl.has(this)&&(rl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||jc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Zc(this),zc(this);const t=tt,s=Ns;tt=this,Ns=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&tt!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),Gc(this),tt=t,Ns=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)al(t);this.deps=this.depsTail=void 0,Zc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?rl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){il(this)&&this.run()}get dirty(){return il(this)}}let Wc=0,fo,ho;function jc(e,t=!1){if(e.flags|=8,t){e.next=ho,ho=e;return}e.next=fo,fo=e}function nl(){Wc++}function ol(){if(--Wc>0)return;if(ho){let t=ho;for(ho=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;fo;){let t=fo;for(fo=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function zc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Gc(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),al(i),i_(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function il(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Kc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Kc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===po)||(e.globalVersion=po,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!il(e))))return;e.flags|=2;const t=e.dep,s=tt,i=Ns;tt=e,Ns=!0;try{zc(e);const n=e.fn(e._value);(t.version===0||Ir(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{tt=s,Ns=i,Gc(e),e.flags&=-3}}function al(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)al(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function i_(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ns=!0;const Qc=[];function Ts(){Qc.push(Ns),Ns=!1}function As(){const e=Qc.pop();Ns=e===void 0?!0:e}function Zc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=tt;tt=void 0;try{t()}finally{tt=s}}}let po=0;class a_{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ll{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!tt||!Ns||tt===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==tt)s=this.activeLink=new a_(tt,this),tt.deps?(s.prevDep=tt.depsTail,tt.depsTail.nextDep=s,tt.depsTail=s):tt.deps=tt.depsTail=s,Jc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=tt.depsTail,s.nextDep=void 0,tt.depsTail.nextDep=s,tt.depsTail=s,tt.deps===s&&(tt.deps=i)}return{}.NODE_ENV!=="production"&&tt.onTrack&&tt.onTrack(ft({effect:tt},t)),s}trigger(t){this.version++,po++,this.notify(t)}notify(t){nl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(ft({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{ol()}}}function Jc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Jc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ul=new WeakMap,Xr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),cl=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),mo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function It(e,t,s){if(Ns&&tt){let i=ul.get(e);i||ul.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new ll),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function $s(e,t,s,i,n,a){const u=ul.get(e);if(!u){po++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):h.trigger())};if(nl(),t==="clear")u.forEach(c);else{const h=he(e),_=h&&el(s);if(h&&s==="length"){const p=Number(i);u.forEach((g,v)=>{(v==="length"||v===mo||!Is(v)&&v>=p)&&c(g)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),_&&c(u.get(mo)),t){case"add":h?_&&c(u.get("length")):(c(u.get(Xr)),Zr(e)&&c(u.get(cl)));break;case"delete":h||(c(u.get(Xr)),Zr(e)&&c(u.get(cl)));break;case"set":Zr(e)&&c(u.get(Xr));break}}ol()}function Tn(e){const t=Te(e);return t===e?t:(It(t,"iterate",mo),zt(e)?t:t.map(Ut))}function hi(e){return It(e=Te(e),"iterate",mo),e}const l_={__proto__:null,[Symbol.iterator](){return dl(this,Symbol.iterator,Ut)},concat(...e){return Tn(this).concat(...e.map(t=>he(t)?Tn(t):t))},entries(){return dl(this,"entries",e=>(e[1]=Ut(e[1]),e))},every(e,t){return ir(this,"every",e,t,void 0,arguments)},filter(e,t){return ir(this,"filter",e,t,s=>s.map(Ut),arguments)},find(e,t){return ir(this,"find",e,t,Ut,arguments)},findIndex(e,t){return ir(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ir(this,"findLast",e,t,Ut,arguments)},findLastIndex(e,t){return ir(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ir(this,"forEach",e,t,void 0,arguments)},includes(...e){return fl(this,"includes",e)},indexOf(...e){return fl(this,"indexOf",e)},join(e){return Tn(this).join(e)},lastIndexOf(...e){return fl(this,"lastIndexOf",e)},map(e,t){return ir(this,"map",e,t,void 0,arguments)},pop(){return go(this,"pop")},push(...e){return go(this,"push",e)},reduce(e,...t){return Yc(this,"reduce",e,t)},reduceRight(e,...t){return Yc(this,"reduceRight",e,t)},shift(){return go(this,"shift")},some(e,t){return ir(this,"some",e,t,void 0,arguments)},splice(...e){return go(this,"splice",e)},toReversed(){return Tn(this).toReversed()},toSorted(e){return Tn(this).toSorted(e)},toSpliced(...e){return Tn(this).toSpliced(...e)},unshift(...e){return go(this,"unshift",e)},values(){return dl(this,"values",Ut)}};function dl(e,t,s){const i=hi(e),n=i[t]();return i!==e&&!zt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const u_=Array.prototype;function ir(e,t,s,i,n,a){const u=hi(e),c=u!==e&&!zt(e),h=u[t];if(h!==u_[t]){const g=h.apply(e,a);return c?Ut(g):g}let _=s;u!==e&&(c?_=function(g,v){return s.call(this,Ut(g),v,e)}:s.length>2&&(_=function(g,v){return s.call(this,g,v,e)}));const p=h.call(u,_,i);return c&&n?n(p):p}function Yc(e,t,s,i){const n=hi(e);let a=s;return n!==e&&(zt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ut(c),h,e)}),n[t](a,...i)}function fl(e,t,s){const i=Te(e);It(i,"iterate",mo);const n=i[t](...s);return(n===-1||n===!1)&&yi(s[0])?(s[0]=Te(s[0]),i[t](...s)):n}function go(e,t,s=[]){Ts(),nl();const i=Te(e)[t].apply(e,s);return ol(),As(),i}const c_=or("__proto__,__v_isRef,__isVue"),Xc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Is));function d_(e){Is(e)||(e=String(e));const t=Te(this);return It(t,"has",e),t.hasOwnProperty(e)}class ed{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?ad:id:a?od:nd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=he(t);if(!n){let h;if(u&&(h=l_[s]))return h;if(s==="hasOwnProperty")return d_}const c=Reflect.get(t,s,xt(t)?t:i);return(Is(s)?Xc.has(s):c_(s))||(n||It(t,"get",s),a)?c:xt(c)?u&&el(s)?c:c.value:Ze(c)?n?ud(c):_i(c):c}}class td extends ed{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const h=js(a);if(!zt(i)&&!js(i)&&(a=Te(a),i=Te(i)),!he(t)&&xt(a)&&!xt(i))return h?!1:(a.value=i,!0)}const u=he(t)&&el(s)?Number(s)<t.length:Ke(t,s),c=Reflect.set(t,s,i,xt(t)?t:n);return t===Te(n)&&(u?Ir(i,a)&&$s(t,"set",s,i,a):$s(t,"add",s,i)),c}deleteProperty(t,s){const i=Ke(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&$s(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!Is(s)||!Xc.has(s))&&It(t,"has",s),i}ownKeys(t){return It(t,"iterate",he(t)?"length":Xr),Reflect.ownKeys(t)}}class sd extends ed{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const f_=new td,h_=new sd,p_=new td(!0),m_=new sd(!0),hl=e=>e,pi=e=>Reflect.getPrototypeOf(e);function g_(e,t,s){return function(...i){const n=this.__v_raw,a=Te(n),u=Zr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,_=n[e](...i),p=s?hl:t?bi:Ut;return!t&&It(a,"iterate",h?cl:Xr),{next(){const{value:g,done:v}=_.next();return v?{value:g,done:v}:{value:c?[p(g[0]),p(g[1])]:p(g),done:v}},[Symbol.iterator](){return this}}}}function mi(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Jr(e)} operation ${s}failed: target is readonly.`,Te(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function __(e,t){const s={get(n){const a=this.__v_raw,u=Te(a),c=Te(n);e||(Ir(n,c)&&It(u,"get",n),It(u,"get",c));const{has:h}=pi(u),_=t?hl:e?bi:Ut;if(h.call(u,n))return _(a.get(n));if(h.call(u,c))return _(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&It(Te(n),"iterate",Xr),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Te(a),c=Te(n);return e||(Ir(n,c)&&It(u,"has",n),It(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,h=Te(c),_=t?hl:e?bi:Ut;return!e&&It(h,"iterate",Xr),c.forEach((p,g)=>n.call(a,_(p),_(g),u))}};return ft(s,e?{add:mi("add"),set:mi("set"),delete:mi("delete"),clear:mi("clear")}:{add(n){!t&&!zt(n)&&!js(n)&&(n=Te(n));const a=Te(this);return pi(a).has.call(a,n)||(a.add(n),$s(a,"add",n,n)),this},set(n,a){!t&&!zt(a)&&!js(a)&&(a=Te(a));const u=Te(this),{has:c,get:h}=pi(u);let _=c.call(u,n);_?{}.NODE_ENV!=="production"&&rd(u,c,n):(n=Te(n),_=c.call(u,n));const p=h.call(u,n);return u.set(n,a),_?Ir(a,p)&&$s(u,"set",n,a,p):$s(u,"add",n,a),this},delete(n){const a=Te(this),{has:u,get:c}=pi(a);let h=u.call(a,n);h?{}.NODE_ENV!=="production"&&rd(a,u,n):(n=Te(n),h=u.call(a,n));const _=c?c.call(a,n):void 0,p=a.delete(n);return h&&$s(a,"delete",n,void 0,_),p},clear(){const n=Te(this),a=n.size!==0,u={}.NODE_ENV!=="production"?Zr(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&$s(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=g_(n,e,t)}),s}function gi(e,t){const s=__(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(Ke(s,n)&&n in i?s:i,n,a)}const v_={get:gi(!1,!1)},y_={get:gi(!1,!0)},b_={get:gi(!0,!1)},C_={get:gi(!0,!0)};function rd(e,t,s){const i=Te(s);if(i!==s&&t.call(e,i)){const n=Xa(e);Hs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const nd=new WeakMap,od=new WeakMap,id=new WeakMap,ad=new WeakMap;function w_(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function E_(e){return e.__v_skip||!Object.isExtensible(e)?0:w_(Xa(e))}function _i(e){return js(e)?e:vi(e,!1,f_,v_,nd)}function ld(e){return vi(e,!1,p_,y_,od)}function ud(e){return vi(e,!0,h_,b_,id)}function Ws(e){return vi(e,!0,m_,C_,ad)}function vi(e,t,s,i,n){if(!Ze(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=E_(e);if(a===0)return e;const u=n.get(e);if(u)return u;const c=new Proxy(e,a===2?i:s);return n.set(e,c),c}function en(e){return js(e)?en(e.__v_raw):!!(e&&e.__v_isReactive)}function js(e){return!!(e&&e.__v_isReadonly)}function zt(e){return!!(e&&e.__v_isShallow)}function yi(e){return e?!!e.__v_raw:!1}function Te(e){const t=e&&e.__v_raw;return t?Te(t):e}function pl(e){return!Ke(e,"__v_skip")&&Object.isExtensible(e)&&lo(e,"__v_skip",!0),e}const Ut=e=>Ze(e)?_i(e):e,bi=e=>Ze(e)?ud(e):e;function xt(e){return e?e.__v_isRef===!0:!1}function cd(e){return dd(e,!1)}function O_(e){return dd(e,!0)}function dd(e,t){return xt(e)?e:new x_(e,t)}class x_{constructor(t,s){this.dep=new ll,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Te(t),this._value=s?t:Ut(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||zt(t)||js(t);t=i?t:Te(t),Ir(t,s)&&(this._rawValue=t,this._value=i?t:Ut(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Nr(e){return xt(e)?e.value:e}const S_={get:(e,t,s)=>t==="__v_raw"?e:Nr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return xt(n)&&!xt(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function fd(e){return en(e)?e:new Proxy(e,S_)}class D_{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new ll(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=po-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&tt!==this)return jc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return Kc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function I_(e,t,s=!1){let i,n;Se(e)?i=e:(i=e.get,n=e.set);const a=new D_(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const Ci={},wi=new WeakMap;let tn;function N_(e,t=!1,s=tn){if(s){let i=wi.get(s);i||wi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function T_(e,t,s=et){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:h}=s,_=X=>{(s.onWarn||Hs)("Invalid watch source: ",X,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=X=>n?X:zt(X)||n===!1||n===0?ar(X,1):ar(X);let g,v,O,k,V=!1,re=!1;if(xt(e)?(v=()=>e.value,V=zt(e)):en(e)?(v=()=>p(e),V=!0):he(e)?(re=!0,V=e.some(X=>en(X)||zt(X)),v=()=>e.map(X=>{if(xt(X))return X.value;if(en(X))return p(X);if(Se(X))return h?h(X,2):X();({}).NODE_ENV!=="production"&&_(X)})):Se(e)?t?v=h?()=>h(e,2):e:v=()=>{if(O){Ts();try{O()}finally{As()}}const X=tn;tn=g;try{return h?h(e,3,[k]):e(k)}finally{tn=X}}:(v=Dt,{}.NODE_ENV!=="production"&&_(e)),t&&n){const X=v,fe=n===!0?1/0:n;v=()=>ar(X(),fe)}const J=o_(),oe=()=>{g.stop(),J&&J.active&&Ja(J.effects,g)};if(a&&t){const X=t;t=(...fe)=>{X(...fe),oe()}}let Y=re?new Array(e.length).fill(Ci):Ci;const Ee=X=>{if(!(!(g.flags&1)||!g.dirty&&!X))if(t){const fe=g.run();if(n||V||(re?fe.some((_e,Ne)=>Ir(_e,Y[Ne])):Ir(fe,Y))){O&&O();const _e=tn;tn=g;try{const Ne=[fe,Y===Ci?void 0:re&&Y[0]===Ci?[]:Y,k];Y=fe,h?h(t,3,Ne):t(...Ne)}finally{tn=_e}}}else g.run()};return c&&c(Ee),g=new $c(v),g.scheduler=u?()=>u(Ee,!1):Ee,k=X=>N_(X,!1,g),O=g.onStop=()=>{const X=wi.get(g);if(X){if(h)h(X,4);else for(const fe of X)fe();wi.delete(g)}},{}.NODE_ENV!=="production"&&(g.onTrack=s.onTrack,g.onTrigger=s.onTrigger),t?i?Ee(!0):Y=g.run():u?u(Ee.bind(null,!0),!0):g.run(),oe.pause=g.pause.bind(g),oe.resume=g.resume.bind(g),oe.stop=oe,oe}function ar(e,t=1/0,s){if(t<=0||!Ze(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,xt(e))ar(e.value,t,s);else if(he(e))for(let i=0;i<e.length;i++)ar(e[i],t,s);else if(In(e)||Zr(e))e.forEach(i=>{ar(i,t,s)});else if(Fc(e)){for(const i in e)ar(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&ar(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const sn=[];function Ei(e){sn.push(e)}function Oi(){sn.pop()}let ml=!1;function Z(e,...t){if(ml)return;ml=!0,Ts();const s=sn.length?sn[sn.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=A_();if(i)An(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${$i(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...M_(n)),console.warn(...a)}As(),ml=!1}function A_(){let e=sn[sn.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function M_(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...P_(s))}),t}function P_({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${$i(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...k_(e.props),a]:[n+a]}function k_(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...hd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function hd(e,t,s){return at(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:xt(t)?(t=hd(e,Te(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):Se(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Te(t),s?t:[`${e}=`,t])}function V_(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Z(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Z(`${t} is NaN - the duration expression might be incorrect.`))}const gl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function An(e,t,s,i){try{return i?e(...i):e()}catch(n){_o(n,t,s)}}function Ms(e,t,s,i){if(Se(e)){const n=An(e,t,s,i);return n&&Ya(n)&&n.catch(a=>{_o(a,t,s)}),n}if(he(e)){const n=[];for(let a=0;a<e.length;a++)n.push(Ms(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&Z(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function _o(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||et;if(t){let c=t.parent;const h=t.proxy,_={}.NODE_ENV!=="production"?gl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let g=0;g<p.length;g++)if(p[g](e,h,_)===!1)return}c=c.parent}if(a){Ts(),An(a,null,10,[e,h,_]),As();return}}R_(e,s,n,i,u)}function R_(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=gl[t];if(s&&Ei(s),Z(`Unhandled error${a?` during execution of ${a}`:""}`),s&&Oi(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const Gt=[];let zs=-1;const Mn=[];let Tr=null,Pn=0;const pd=Promise.resolve();let xi=null;const F_=100;function _l(e){const t=xi||pd;return e?t.then(this?e.bind(this):e):t}function U_(e){let t=zs+1,s=Gt.length;for(;t<s;){const i=t+s>>>1,n=Gt[i],a=vo(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function Si(e){if(!(e.flags&1)){const t=vo(e),s=Gt[Gt.length-1];!s||!(e.flags&2)&&t>=vo(s)?Gt.push(e):Gt.splice(U_(t),0,e),e.flags|=1,md()}}function md(){xi||(xi=pd.then(yd))}function gd(e){he(e)?Mn.push(...e):Tr&&e.id===-1?Tr.splice(Pn+1,0,e):e.flags&1||(Mn.push(e),e.flags|=1),md()}function _d(e,t,s=zs+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Gt.length;s++){const i=Gt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&vl(t,i))continue;Gt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function vd(e){if(Mn.length){const t=[...new Set(Mn)].sort((s,i)=>vo(s)-vo(i));if(Mn.length=0,Tr){Tr.push(...t);return}for(Tr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),Pn=0;Pn<Tr.length;Pn++){const s=Tr[Pn];({}).NODE_ENV!=="production"&&vl(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Tr=null,Pn=0}}const vo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>vl(e,s):Dt;try{for(zs=0;zs<Gt.length;zs++){const s=Gt[zs];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),An(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;zs<Gt.length;zs++){const s=Gt[zs];s&&(s.flags&=-2)}zs=-1,Gt.length=0,vd(e),xi=null,(Gt.length||Mn.length)&&yd(e)}}function vl(e,t){const s=e.get(t)||0;if(s>F_){const i=t.i,n=i&&jl(i.type);return _o(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let Ps=!1;const Di=new Map;({}).NODE_ENV!=="production"&&(uo().__VUE_HMR_RUNTIME__={createRecord:yl(bd),rerender:yl(q_),reload:yl(H_)});const rn=new Map;function L_(e){const t=e.type.__hmrId;let s=rn.get(t);s||(bd(t,e.type),s=rn.get(t)),s.instances.add(e)}function B_(e){rn.get(e.type.__hmrId).instances.delete(e)}function bd(e,t){return rn.has(e)?!1:(rn.set(e,{initialDef:Ii(t),instances:new Set}),!0)}function Ii(e){return Af(e)?e.__vccOpts:e}function q_(e,t){const s=rn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ii(i.type).render=t),i.renderCache=[],Ps=!0,i.update(),Ps=!1}))}function H_(e,t){const s=rn.get(e);if(!s)return;t=Ii(t),Cd(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=Ii(a.type);let c=Di.get(u);c||(u!==s.initialDef&&Cd(u,t),Di.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?Si(()=>{Ps=!0,a.parent.update(),Ps=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}gd(()=>{Di.clear()})}function Cd(e,t){ft(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function yl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,yo=[],bl=!1;function bo(e,...t){Gs?Gs.emit(e,...t):bl||yo.push({event:e,args:t})}function wd(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,yo.forEach(({event:n,args:a})=>Gs.emit(n,...a)),yo=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{wd(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,bl=!0,yo=[])},3e3)):(bl=!0,yo=[])}function $_(e,t){bo("app:init",e,t,{Fragment:Ae,Text:Do,Comment:Ct,Static:Io})}function W_(e){bo("app:unmount",e)}const j_=Cl("component:added"),Ed=Cl("component:updated"),z_=Cl("component:removed"),G_=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&z_(e)};/*! #__NO_SIDE_EFFECTS__ */function Cl(e){return t=>{bo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const K_=Od("perf:start"),Q_=Od("perf:end");function Od(e){return(t,s,i)=>{bo(e,t.appContext.app,t.uid,t,s,i)}}function Z_(e,t,s){bo("component:emit",e.appContext.app,e,t,s)}let bt=null,xd=null;function Ni(e){const t=bt;return bt=e,xd=e&&e.type.__scopeId||null,t}function Ce(e,t=bt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&Cf(-1);const a=Ni(t);let u;try{u=e(...n)}finally{Ni(a),i._d&&Cf(1)}return{}.NODE_ENV!=="production"&&Ed(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Sd(e){Hg(e)&&Z("Do not use built-in directive ids as custom directive id: "+e)}function gt(e,t){if(bt===null)return{}.NODE_ENV!=="production"&&Z("withDirectives can only be used inside render functions."),e;const s=Hi(bt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,h=et]=t[n];a&&(Se(a)&&(a={mounted:a,updated:a}),a.deep&&ar(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function nn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(Ts(),Ms(h,s,8,[e.el,c,e,t]),As())}}const Dd=Symbol("_vte"),Id=e=>e.__isTeleport,on=e=>e&&(e.disabled||e.disabled===""),Nd=e=>e&&(e.defer||e.defer===""),Td=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ad=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,wl=(e,t)=>{const s=e&&e.to;if(at(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!on(e)&&Z(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Z("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!on(e)&&Z(`Invalid Teleport target: ${s}`),s},Md={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,h,_){const{mc:p,pc:g,pbc:v,o:{insert:O,querySelector:k,createText:V,createComment:re}}=_,J=on(t.props);let{shapeFlag:oe,children:Y,dynamicChildren:Ee}=t;if({}.NODE_ENV!=="production"&&Ps&&(h=!1,Ee=null),e==null){const X=t.el={}.NODE_ENV!=="production"?re("teleport start"):V(""),fe=t.anchor={}.NODE_ENV!=="production"?re("teleport end"):V("");O(X,s,i),O(fe,s,i);const _e=(ae,A)=>{oe&16&&(n&&n.isCE&&(n.ce._teleportTarget=ae),p(Y,ae,A,n,a,u,c,h))},Ne=()=>{const ae=t.target=wl(t.props,k),A=Pd(ae,t,V,O);ae?(u!=="svg"&&Td(ae)?u="svg":u!=="mathml"&&Ad(ae)&&(u="mathml"),J||(_e(ae,A),Ai(t,!1))):{}.NODE_ENV!=="production"&&!J&&Z("Invalid Teleport target on mount:",ae,`(${typeof ae})`)};J&&(_e(s,fe),Ai(t,!0)),Nd(t.props)?(t.el.__isMounted=!1,Qt(()=>{Ne(),delete t.el.__isMounted},a)):Ne()}else{if(Nd(t.props)&&e.el.__isMounted===!1){Qt(()=>{Md.process(e,t,s,i,n,a,u,c,h,_)},a);return}t.el=e.el,t.targetStart=e.targetStart;const X=t.anchor=e.anchor,fe=t.target=e.target,_e=t.targetAnchor=e.targetAnchor,Ne=on(e.props),ae=Ne?s:fe,A=Ne?X:_e;if(u==="svg"||Td(fe)?u="svg":(u==="mathml"||Ad(fe))&&(u="mathml"),Ee?(v(e.dynamicChildren,Ee,ae,n,a,u,c),So(e,t,{}.NODE_ENV==="production")):h||g(e,t,ae,A,n,a,u,c,!1),J)Ne?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Ti(t,s,X,_,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const we=t.target=wl(t.props,k);we?Ti(t,we,null,_,0):{}.NODE_ENV!=="production"&&Z("Invalid Teleport target on update:",fe,`(${typeof fe})`)}else Ne&&Ti(t,fe,_e,_,1);Ai(t,J)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:_,targetAnchor:p,target:g,props:v}=e;if(g&&(n(_),n(p)),a&&n(h),u&16){const O=a||!on(v);for(let k=0;k<c.length;k++){const V=c[k];i(V,t,s,O,!!V.dynamicChildren)}}},move:Ti,hydrate:J_};function Ti(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:_,props:p}=e,g=a===2;if(g&&i(u,t,s),(!g||on(p))&&h&16)for(let v=0;v<_.length;v++)n(_[v],t,s,2);g&&i(c,t,s)}function J_(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:_,createText:p}},g){const v=t.target=wl(t.props,h);if(v){const O=on(t.props),k=v._lpa||v.firstChild;if(t.shapeFlag&16)if(O)t.anchor=g(u(e),t,c(e),s,i,n,a),t.targetStart=k,t.targetAnchor=k&&u(k);else{t.anchor=u(e);let V=k;for(;V;){if(V&&V.nodeType===8){if(V.data==="teleport start anchor")t.targetStart=V;else if(V.data==="teleport anchor"){t.targetAnchor=V,v._lpa=t.targetAnchor&&u(t.targetAnchor);break}}V=u(V)}t.targetAnchor||Pd(v,t,p,_),g(k&&u(k),t,v,s,i,n,a)}Ai(t,O)}return t.anchor&&u(t.anchor)}const Y_=Md;function Ai(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Pd(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[Dd]=a,e&&(i(n,e),i(a,e)),a}const Ar=Symbol("_leaveCb"),Mi=Symbol("_enterCb");function X_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return $d(()=>{e.isMounted=!0}),Wd(()=>{e.isUnmounting=!0}),e}const _s=[Function,Array],kd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:_s,onEnter:_s,onAfterEnter:_s,onEnterCancelled:_s,onBeforeLeave:_s,onLeave:_s,onAfterLeave:_s,onLeaveCancelled:_s,onBeforeAppear:_s,onAppear:_s,onAfterAppear:_s,onAppearCancelled:_s},Vd=e=>{const t=e.subTree;return t.component?Vd(t.component):t},ev={name:"BaseTransition",props:kd,setup(e,{slots:t}){const s=Bi(),i=X_();return()=>{const n=t.default&&Ld(t.default(),!0);if(!n||!n.length)return;const a=Rd(n),u=Te(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Z(`invalid <transition> mode: ${c}`),i.isLeaving)return Ol(a);const h=Ud(a);if(!h)return Ol(a);let _=El(h,u,i,s,g=>_=g);h.type!==Ct&&Co(h,_);let p=s.subTree&&Ud(s.subTree);if(p&&p.type!==Ct&&!cn(h,p)&&Vd(s).type!==Ct){let g=El(p,u,i,s);if(Co(p,g),c==="out-in"&&h.type!==Ct)return i.isLeaving=!0,g.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete g.afterLeave,p=void 0},Ol(a);c==="in-out"&&h.type!==Ct?g.delayLeave=(v,O,k)=>{const V=Fd(i,p);V[String(p.key)]=p,v[Ar]=()=>{O(),v[Ar]=void 0,delete _.delayedLeave,p=void 0},_.delayedLeave=()=>{k(),delete _.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Rd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==Ct){if({}.NODE_ENV!=="production"&&s){Z("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const tv=ev;function Fd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function El(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:_,onAfterEnter:p,onEnterCancelled:g,onBeforeLeave:v,onLeave:O,onAfterLeave:k,onLeaveCancelled:V,onBeforeAppear:re,onAppear:J,onAfterAppear:oe,onAppearCancelled:Y}=t,Ee=String(e.key),X=Fd(s,e),fe=(ae,A)=>{ae&&Ms(ae,i,9,A)},_e=(ae,A)=>{const we=A[1];fe(ae,A),he(ae)?ae.every(ue=>ue.length<=1)&&we():ae.length<=1&&we()},Ne={mode:u,persisted:c,beforeEnter(ae){let A=h;if(!s.isMounted)if(a)A=re||h;else return;ae[Ar]&&ae[Ar](!0);const we=X[Ee];we&&cn(e,we)&&we.el[Ar]&&we.el[Ar](),fe(A,[ae])},enter(ae){let A=_,we=p,ue=g;if(!s.isMounted)if(a)A=J||_,we=oe||p,ue=Y||g;else return;let ze=!1;const _t=ae[Mi]=pt=>{ze||(ze=!0,pt?fe(ue,[ae]):fe(we,[ae]),Ne.delayedLeave&&Ne.delayedLeave(),ae[Mi]=void 0)};A?_e(A,[ae,_t]):_t()},leave(ae,A){const we=String(e.key);if(ae[Mi]&&ae[Mi](!0),s.isUnmounting)return A();fe(v,[ae]);let ue=!1;const ze=ae[Ar]=_t=>{ue||(ue=!0,A(),_t?fe(V,[ae]):fe(k,[ae]),ae[Ar]=void 0,X[we]===e&&delete X[we])};X[we]=e,O?_e(O,[ae,ze]):ze()},clone(ae){const A=El(ae,t,s,i,n);return n&&n(A),A}};return Ne}function Ol(e){if(Eo(e))return e=Qs(e),e.children=null,e}function Ud(e){if(!Eo(e))return Id(e.type)&&e.children?Rd(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&Se(s.default))return s.default()}}function Co(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Co(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ld(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Ae?(u.patchFlag&128&&n++,i=i.concat(Ld(u.children,t,c))):(t||u.type!==Ct)&&i.push(c!=null?Qs(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Bd(e,t){return Se(e)?(()=>ft({name:e.name},t,{setup:e}))():e}function qd(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const sv=new WeakSet;function wo(e,t,s,i,n=!1){if(he(e)){e.forEach((k,V)=>wo(k,t&&(he(t)?t[V]:t),s,i,n));return}if(kn(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&wo(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Hi(i.component):i.el,u=n?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){Z("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const _=t&&t.r,p=c.refs===et?c.refs={}:c.refs,g=c.setupState,v=Te(g),O=g===et?()=>!1:k=>({}).NODE_ENV!=="production"&&(Ke(v,k)&&!xt(v[k])&&Z(`Template ref "${k}" used on a non-ref value. It will not work in the production build.`),sv.has(v[k]))?!1:Ke(v,k);if(_!=null&&_!==h&&(at(_)?(p[_]=null,O(_)&&(g[_]=null)):xt(_)&&(_.value=null)),Se(h))An(h,c,12,[u,p]);else{const k=at(h),V=xt(h);if(k||V){const re=()=>{if(e.f){const J=k?O(h)?g[h]:p[h]:h.value;n?he(J)&&Ja(J,a):he(J)?J.includes(a)||J.push(a):k?(p[h]=[a],O(h)&&(g[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else k?(p[h]=u,O(h)&&(g[h]=u)):V?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Z("Invalid template ref type:",h,`(${typeof h})`)};u?(re.id=-1,Qt(re,s)):re()}else({}).NODE_ENV!=="production"&&Z("Invalid template ref type:",h,`(${typeof h})`)}}uo().requestIdleCallback,uo().cancelIdleCallback;const kn=e=>!!e.type.__asyncLoader,Eo=e=>e.type.__isKeepAlive;function rv(e,t){Hd(e,"a",t)}function nv(e,t){Hd(e,"da",t)}function Hd(e,t,s=Nt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Pi(t,i,s),s){let n=s.parent;for(;n&&n.parent;)Eo(n.parent.vnode)&&ov(i,t,s,n),n=n.parent}}function ov(e,t,s,i){const n=Pi(t,e,i,!0);jd(()=>{Ja(i[t],n)},s)}function Pi(e,t,s=Nt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{Ts();const c=Ao(s),h=Ms(t,s,e,u);return c(),As(),h});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=Yr(gl[e].replace(/ hook$/,""));Z(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const lr=e=>(t,s=Nt)=>{(!Mo||e==="sp")&&Pi(e,(...i)=>t(...i),s)},iv=lr("bm"),$d=lr("m"),av=lr("bu"),lv=lr("u"),Wd=lr("bum"),jd=lr("um"),uv=lr("sp"),cv=lr("rtg"),dv=lr("rtc");function fv(e,t=Nt){Pi("ec",e,t)}const xl="components";function $(e,t){return pv(xl,e,!0,t)||e}const hv=Symbol.for("v-ndc");function pv(e,t,s=!0,i=!1){const n=bt||Nt;if(n){const a=n.type;if(e===xl){const c=jl(a,!1);if(c&&(c===t||c===jt(t)||c===Jr(jt(t))))return a}const u=zd(n[e]||a[e],t)||zd(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===xl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Z(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Z(`resolve${Jr(e.slice(0,-1))} can only be used in render() or setup().`)}function zd(e,t){return e&&(e[t]||e[jt(t)]||e[Jr(jt(t))])}function lt(e,t,s,i){let n;const a=s&&s[i],u=he(e);if(u||at(e)){const c=u&&en(e);let h=!1,_=!1;c&&(h=!zt(e),_=js(e),e=hi(e)),n=new Array(e.length);for(let p=0,g=e.length;p<g;p++)n[p]=t(h?_?bi(Ut(e[p])):Ut(e[p]):e[p],p,void 0,a&&a[p])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Z(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(Ze(e))if(e[Symbol.iterator])n=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);n=new Array(c.length);for(let h=0,_=c.length;h<_;h++){const p=c[h];n[h]=t(e[p],p,h,a&&a[h])}}else n=[];return s&&(s[i]=n),n}function Pt(e,t,s={},i,n){if(bt.ce||bt.parent&&kn(bt.parent)&&bt.parent.ce)return t!=="default"&&(s.name=t),x(),ht(Ae,null,[T("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Z("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),x();const u=a&&Gd(a(s)),c=s.key||u&&u.key,h=ht(Ae,{key:(c&&!Is(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function Gd(e){return e.some(t=>un(t)?!(t.type===Ct||t.type===Ae&&!Gd(t.children)):!0)?e:null}const Sl=e=>e?Df(e)?Hi(e):Sl(e.parent):null,an=ft(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?Ws(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?Ws(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?Ws(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?Ws(e.refs):e.refs,$parent:e=>Sl(e.parent),$root:e=>Sl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Tl(e),$forceUpdate:e=>e.f||(e.f=()=>{Si(e.update)}),$nextTick:e=>e.n||(e.n=_l.bind(e.proxy)),$watch:e=>Gv.bind(e)}),Dl=e=>e==="_"||e==="$",Il=(e,t)=>e!==et&&!e.__isScriptSetup&&Ke(e,t),Kd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let _;if(t[0]!=="$"){const O=u[t];if(O!==void 0)switch(O){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(Il(i,t))return u[t]=1,i[t];if(n!==et&&Ke(n,t))return u[t]=2,n[t];if((_=e.propsOptions[0])&&Ke(_,t))return u[t]=3,a[t];if(s!==et&&Ke(s,t))return u[t]=4,s[t];Nl&&(u[t]=0)}}const p=an[t];let g,v;if(p)return t==="$attrs"?(It(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Ui()):{}.NODE_ENV!=="production"&&t==="$slots"&&It(e,"get",t),p(e);if((g=c.__cssModules)&&(g=g[t]))return g;if(s!==et&&Ke(s,t))return u[t]=4,s[t];if(v=h.config.globalProperties,Ke(v,t))return v[t];({}).NODE_ENV!=="production"&&bt&&(!at(t)||t.indexOf("__v")!==0)&&(n!==et&&Dl(t[0])&&Ke(n,t)?Z(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===bt&&Z(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return Il(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&Ke(n,t)?(Z(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==et&&Ke(i,t)?(i[t]=s,!0):Ke(e.props,t)?({}.NODE_ENV!=="production"&&Z(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Z(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==et&&Ke(e,u)||Il(t,u)||(c=a[0])&&Ke(c,u)||Ke(i,u)||Ke(an,u)||Ke(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ke(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Kd.ownKeys=e=>(Z("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function mv(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(an).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>an[s](e),set:Dt})}),t}function gv(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Dt})})}function _v(e){const{ctx:t,setupState:s}=e;Object.keys(Te(s)).forEach(i=>{if(!s.__isScriptSetup){if(Dl(i[0])){Z(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Dt})}})}function Qd(e){return he(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function vv(){const e=Object.create(null);return(t,s)=>{e[s]?Z(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Nl=!0;function yv(e){const t=Tl(e),s=e.proxy,i=e.ctx;Nl=!1,t.beforeCreate&&Zd(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:h,inject:_,created:p,beforeMount:g,mounted:v,beforeUpdate:O,updated:k,activated:V,deactivated:re,beforeDestroy:J,beforeUnmount:oe,destroyed:Y,unmounted:Ee,render:X,renderTracked:fe,renderTriggered:_e,errorCaptured:Ne,serverPrefetch:ae,expose:A,inheritAttrs:we,components:ue,directives:ze,filters:_t}=t,pt={}.NODE_ENV!=="production"?vv():null;if({}.NODE_ENV!=="production"){const[K]=e.propsOptions;if(K)for(const ye in K)pt("Props",ye)}if(_&&bv(_,i,pt),u)for(const K in u){const ye=u[K];Se(ye)?({}.NODE_ENV!=="production"?Object.defineProperty(i,K,{value:ye.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[K]=ye.bind(s),{}.NODE_ENV!=="production"&&pt("Methods",K)):{}.NODE_ENV!=="production"&&Z(`Method "${K}" has type "${typeof ye}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!Se(n)&&Z("The data option must be a function. Plain object usage is no longer supported.");const K=n.call(s,s);if({}.NODE_ENV!=="production"&&Ya(K)&&Z("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Ze(K))({}).NODE_ENV!=="production"&&Z("data() should return an object.");else if(e.data=_i(K),{}.NODE_ENV!=="production")for(const ye in K)pt("Data",ye),Dl(ye[0])||Object.defineProperty(i,ye,{configurable:!0,enumerable:!0,get:()=>K[ye],set:Dt})}if(Nl=!0,a)for(const K in a){const ye=a[K],be=Se(ye)?ye.bind(s,s):Se(ye.get)?ye.get.bind(s,s):Dt;({}).NODE_ENV!=="production"&&be===Dt&&Z(`Computed property "${K}" has no getter.`);const wt=!Se(ye)&&Se(ye.set)?ye.set.bind(s):{}.NODE_ENV!=="production"?()=>{Z(`Write operation failed: computed property "${K}" is readonly.`)}:Dt,ct=Vs({get:be,set:wt});Object.defineProperty(i,K,{enumerable:!0,configurable:!0,get:()=>ct.value,set:vs=>ct.value=vs}),{}.NODE_ENV!=="production"&&pt("Computed",K)}if(c)for(const K in c)Jd(c[K],i,s,K);if(h){const K=Se(h)?h.call(s):h;Reflect.ownKeys(K).forEach(ye=>{Vi(ye,K[ye])})}p&&Zd(p,e,"c");function ut(K,ye){he(ye)?ye.forEach(be=>K(be.bind(s))):ye&&K(ye.bind(s))}if(ut(iv,g),ut($d,v),ut(av,O),ut(lv,k),ut(rv,V),ut(nv,re),ut(fv,Ne),ut(dv,fe),ut(cv,_e),ut(Wd,oe),ut(jd,Ee),ut(uv,ae),he(A))if(A.length){const K=e.exposed||(e.exposed={});A.forEach(ye=>{Object.defineProperty(K,ye,{get:()=>s[ye],set:be=>s[ye]=be})})}else e.exposed||(e.exposed={});X&&e.render===Dt&&(e.render=X),we!=null&&(e.inheritAttrs=we),ue&&(e.components=ue),ze&&(e.directives=ze),ae&&qd(e)}function bv(e,t,s=Dt){he(e)&&(e=Al(e));for(const i in e){const n=e[i];let a;Ze(n)?"default"in n?a=Ks(n.from||i,n.default,!0):a=Ks(n.from||i):a=Ks(n),xt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Zd(e,t,s){Ms(he(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Jd(e,t,s,i){let n=i.includes(".")?mf(s,i):()=>s[i];if(at(e)){const a=t[e];Se(a)?Rn(n,a):{}.NODE_ENV!=="production"&&Z(`Invalid watch handler specified by key "${e}"`,a)}else if(Se(e))Rn(n,e.bind(s));else if(Ze(e))if(he(e))e.forEach(a=>Jd(a,t,s,i));else{const a=Se(e.handler)?e.handler.bind(s):t[e.handler];Se(a)?Rn(n,a,e):{}.NODE_ENV!=="production"&&Z(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Z(`Invalid watch option: "${i}"`,e)}function Tl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!n.length&&!s&&!i?h=t:(h={},n.length&&n.forEach(_=>ki(h,_,u,!0)),ki(h,t,u)),Ze(t)&&a.set(t,h),h}function ki(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&ki(e,a,s,!0),n&&n.forEach(u=>ki(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Z('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=Cv[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const Cv={data:Yd,props:Xd,emits:Xd,methods:Oo,computed:Oo,beforeCreate:Kt,created:Kt,beforeMount:Kt,mounted:Kt,beforeUpdate:Kt,updated:Kt,beforeDestroy:Kt,beforeUnmount:Kt,destroyed:Kt,unmounted:Kt,activated:Kt,deactivated:Kt,errorCaptured:Kt,serverPrefetch:Kt,components:Oo,directives:Oo,watch:Ev,provide:Yd,inject:wv};function Yd(e,t){return t?e?function(){return ft(Se(e)?e.call(this,this):e,Se(t)?t.call(this,this):t)}:t:e}function wv(e,t){return Oo(Al(e),Al(t))}function Al(e){if(he(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Kt(e,t){return e?[...new Set([].concat(e,t))]:t}function Oo(e,t){return e?ft(Object.create(null),e,t):t}function Xd(e,t){return e?he(e)&&he(t)?[...new Set([...e,...t])]:ft(Object.create(null),Qd(e),Qd(t??{})):t}function Ev(e,t){if(!e)return t;if(!t)return e;const s=ft(Object.create(null),e);for(const i in t)s[i]=Kt(e[i],t[i]);return s}function ef(){return{app:null,config:{isNativeTag:Bg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ov=0;function xv(e,t){return function(i,n=null){Se(i)||(i=ft({},i)),n!=null&&!Ze(n)&&({}.NODE_ENV!=="production"&&Z("root props passed to app.mount() must be an object."),n=null);const a=ef(),u=new WeakSet,c=[];let h=!1;const _=a.app={_uid:Ov++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:Mf,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Z("app.config cannot be replaced. Modify individual options instead.")},use(p,...g){return u.has(p)?{}.NODE_ENV!=="production"&&Z("Plugin has already been applied to target app."):p&&Se(p.install)?(u.add(p),p.install(_,...g)):Se(p)?(u.add(p),p(_,...g)):{}.NODE_ENV!=="production"&&Z('A plugin must either be a function or an object with an "install" function.'),_},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Z("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),_},component(p,g){return{}.NODE_ENV!=="production"&&$l(p,a.config),g?({}.NODE_ENV!=="production"&&a.components[p]&&Z(`Component "${p}" has already been registered in target app.`),a.components[p]=g,_):a.components[p]},directive(p,g){return{}.NODE_ENV!=="production"&&Sd(p),g?({}.NODE_ENV!=="production"&&a.directives[p]&&Z(`Directive "${p}" has already been registered in target app.`),a.directives[p]=g,_):a.directives[p]},mount(p,g,v){if(h)({}).NODE_ENV!=="production"&&Z("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Z("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const O=_._ceVNode||T(i,n);return O.appContext=a,v===!0?v="svg":v===!1&&(v=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{const k=Qs(O);k.el=null,e(k,p,v)}),g&&t?t(O,p):e(O,p,v),h=!0,_._container=p,p.__vue_app__=_,{}.NODE_ENV!=="production"&&(_._instance=O.component,$_(_,Mf)),Hi(O.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Z(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ms(c,_._instance,16),e(null,_._container),{}.NODE_ENV!=="production"&&(_._instance=null,W_(_)),delete _._container.__vue_app__):{}.NODE_ENV!=="production"&&Z("Cannot unmount an app that is not mounted.")},provide(p,g){return{}.NODE_ENV!=="production"&&p in a.provides&&(Ke(a.provides,p)?Z(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`):Z(`App already provides property with key "${String(p)}" inherited from its parent element. It will be overwritten with the new value.`)),a.provides[p]=g,_},runWithContext(p){const g=Vn;Vn=_;try{return p()}finally{Vn=g}}};return _}}let Vn=null;function Vi(e,t){if(!Nt)({}).NODE_ENV!=="production"&&Z("provide() can only be used inside setup().");else{let s=Nt.provides;const i=Nt.parent&&Nt.parent.provides;i===s&&(s=Nt.provides=Object.create(i)),s[e]=t}}function Ks(e,t,s=!1){const i=Nt||bt;if(i||Vn){let n=Vn?Vn._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&Se(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Z(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Z("inject() can only be used inside setup() or functional components.")}const tf={},sf=()=>Object.create(tf),rf=e=>Object.getPrototypeOf(e)===tf;function Sv(e,t,s,i=!1){const n={},a=sf();e.propsDefaults=Object.create(null),nf(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&lf(t||{},n,e),s?e.props=i?n:ld(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function Dv(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function Iv(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Te(n),[h]=e.propsOptions;let _=!1;if(!({}.NODE_ENV!=="production"&&Dv(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let g=0;g<p.length;g++){let v=p[g];if(Fi(e.emitsOptions,v))continue;const O=t[v];if(h)if(Ke(a,v))O!==a[v]&&(a[v]=O,_=!0);else{const k=jt(v);n[k]=Ml(h,c,k,O,e,!1)}else O!==a[v]&&(a[v]=O,_=!0)}}}else{nf(e,t,n,a)&&(_=!0);let p;for(const g in c)(!t||!Ke(t,g)&&((p=Dr(g))===g||!Ke(t,p)))&&(h?s&&(s[g]!==void 0||s[p]!==void 0)&&(n[g]=Ml(h,c,g,void 0,e,!0)):delete n[g]);if(a!==c)for(const g in a)(!t||!Ke(t,g))&&(delete a[g],_=!0)}_&&$s(e.attrs,"set",""),{}.NODE_ENV!=="production"&&lf(t||{},n,e)}function nf(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(ao(h))continue;const _=t[h];let p;n&&Ke(n,p=jt(h))?!a||!a.includes(p)?s[p]=_:(c||(c={}))[p]=_:Fi(e.emitsOptions,h)||(!(h in i)||_!==i[h])&&(i[h]=_,u=!0)}if(a){const h=Te(s),_=c||et;for(let p=0;p<a.length;p++){const g=a[p];s[g]=Ml(n,h,g,_[g],e,!Ke(_,g))}}return u}function Ml(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=Ke(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&Se(h)){const{propsDefaults:_}=n;if(s in _)i=_[s];else{const p=Ao(n);i=_[s]=h.call(null,t),p()}}else i=h;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Dr(s))&&(i=!0))}return i}const Nv=new WeakMap;function of(e,t,s=!1){const i=s?Nv:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let h=!1;if(!Se(e)){const p=g=>{h=!0;const[v,O]=of(g,t,!0);ft(u,v),O&&c.push(...O)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Ze(e)&&i.set(e,Dn),Dn;if(he(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!at(a[p])&&Z("props must be strings when using array syntax.",a[p]);const g=jt(a[p]);af(g)&&(u[g]=et)}else if(a){({}).NODE_ENV!=="production"&&!Ze(a)&&Z("invalid props options",a);for(const p in a){const g=jt(p);if(af(g)){const v=a[p],O=u[g]=he(v)||Se(v)?{type:v}:ft({},v),k=O.type;let V=!1,re=!0;if(he(k))for(let J=0;J<k.length;++J){const oe=k[J],Y=Se(oe)&&oe.name;if(Y==="Boolean"){V=!0;break}else Y==="String"&&(re=!1)}else V=Se(k)&&k.name==="Boolean";O[0]=V,O[1]=re,(V||Ke(O,"default"))&&c.push(g)}}}const _=[u,c];return Ze(e)&&i.set(e,_),_}function af(e){return e[0]!=="$"&&!ao(e)?!0:({}.NODE_ENV!=="production"&&Z(`Invalid prop name: "${e}" is a reserved property.`),!1)}function Tv(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function lf(e,t,s){const i=Te(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>jt(u));for(const u in n){let c=n[u];c!=null&&Av(u,i[u],c,{}.NODE_ENV!=="production"?Ws(i):i,!a.includes(u))}}function Av(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&n){Z('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let _=!1;const p=he(a)?a:[a],g=[];for(let v=0;v<p.length&&!_;v++){const{valid:O,expectedType:k}=Pv(t,p[v]);g.push(k||""),_=O}if(!_){Z(kv(e,t,g));return}}c&&!c(t,i)&&Z('Invalid prop: custom validator check failed for prop "'+e+'".')}}const Mv=or("String,Number,Boolean,Function,Symbol,BigInt");function Pv(e,t){let s;const i=Tv(t);if(i==="null")s=e===null;else if(Mv(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=Ze(e):i==="Array"?s=he(e):s=e instanceof t;return{valid:s,expectedType:i}}function kv(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Jr).join(" | ")}`;const n=s[0],a=Xa(t),u=uf(t,n),c=uf(t,a);return s.length===1&&cf(n)&&!Vv(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,cf(a)&&(i+=`with value ${c}.`),i}function uf(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function cf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function Vv(...e){return e.some(t=>t.toLowerCase()==="boolean")}const Pl=e=>e[0]==="_"||e==="$stable",kl=e=>he(e)?e.map(ks):[ks(e)],Rv=(e,t,s)=>{if(t._n)return t;const i=Ce((...n)=>({}.NODE_ENV!=="production"&&Nt&&!(s===null&&bt)&&!(s&&s.root!==Nt.root)&&Z(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),kl(t(...n))),s);return i._c=!1,i},df=(e,t,s)=>{const i=e._ctx;for(const n in e){if(Pl(n))continue;const a=e[n];if(Se(a))t[n]=Rv(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Z(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=kl(a);t[n]=()=>u}}},ff=(e,t)=>{({}).NODE_ENV!=="production"&&!Eo(e.vnode)&&Z("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=kl(t);e.slots.default=()=>s},Vl=(e,t,s)=>{for(const i in t)(s||!Pl(i))&&(e[i]=t[i])},Fv=(e,t,s)=>{const i=e.slots=sf();if(e.vnode.shapeFlag&32){const n=t.__;n&&lo(i,"__",n,!0);const a=t._;a?(Vl(i,t,s),s&&lo(i,"_",a,!0)):df(t,i)}else t&&ff(e,t)},Uv=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=et;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&Ps?(Vl(n,t,s),$s(e,"set","$slots")):s&&c===1?a=!1:Vl(n,t,s):(a=!t.$stable,df(t,n)),u=t}else t&&(ff(e,t),u={default:1});if(a)for(const c in n)!Pl(c)&&u[c]==null&&delete n[c]};let xo,Mr;function ur(e,t){e.appContext.config.performance&&Ri()&&Mr.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&K_(e,t,Ri()?Mr.now():Date.now())}function cr(e,t){if(e.appContext.config.performance&&Ri()){const s=`vue-${t}-${e.uid}`,i=s+":end";Mr.mark(i),Mr.measure(`<${$i(e,e.type)}> ${t}`,s,i),Mr.clearMarks(s),Mr.clearMarks(i)}({}).NODE_ENV!=="production"&&Q_(e,t,Ri()?Mr.now():Date.now())}function Ri(){return xo!==void 0||(typeof window<"u"&&window.performance?(xo=!0,Mr=window.performance):xo=!1),xo}function Lv(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Qt=e1;function Bv(e){return qv(e)}function qv(e,t){Lv();const s=uo();s.__VUE__=!0,{}.NODE_ENV!=="production"&&wd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:h,setText:_,setElementText:p,parentNode:g,nextSibling:v,setScopeId:O=Dt,insertStaticContent:k}=e,V=(C,E,P,U=null,H=null,W=null,te=void 0,Q=null,ee={}.NODE_ENV!=="production"&&Ps?!1:!!E.dynamicChildren)=>{if(C===E)return;C&&!cn(C,E)&&(U=ie(C),ss(C,H,W,!0),C=null),E.patchFlag===-2&&(ee=!1,E.dynamicChildren=null);const{type:z,ref:ve,shapeFlag:se}=E;switch(z){case Do:re(C,E,P,U);break;case Ct:J(C,E,P,U);break;case Io:C==null?oe(E,P,U,te):{}.NODE_ENV!=="production"&&Y(C,E,P,te);break;case Ae:ze(C,E,P,U,H,W,te,Q,ee);break;default:se&1?fe(C,E,P,U,H,W,te,Q,ee):se&6?_t(C,E,P,U,H,W,te,Q,ee):se&64||se&128?z.process(C,E,P,U,H,W,te,Q,ee,ke):{}.NODE_ENV!=="production"&&Z("Invalid VNode type:",z,`(${typeof z})`)}ve!=null&&H?wo(ve,C&&C.ref,W,E||C,!E):ve==null&&C&&C.ref!=null&&wo(C.ref,null,W,C,!0)},re=(C,E,P,U)=>{if(C==null)i(E.el=c(E.children),P,U);else{const H=E.el=C.el;E.children!==C.children&&_(H,E.children)}},J=(C,E,P,U)=>{C==null?i(E.el=h(E.children||""),P,U):E.el=C.el},oe=(C,E,P,U)=>{[C.el,C.anchor]=k(C.children,E,P,U,C.el,C.anchor)},Y=(C,E,P,U)=>{if(E.children!==C.children){const H=v(C.anchor);X(C),[E.el,E.anchor]=k(E.children,P,H,U)}else E.el=C.el,E.anchor=C.anchor},Ee=({el:C,anchor:E},P,U)=>{let H;for(;C&&C!==E;)H=v(C),i(C,P,U),C=H;i(E,P,U)},X=({el:C,anchor:E})=>{let P;for(;C&&C!==E;)P=v(C),n(C),C=P;n(E)},fe=(C,E,P,U,H,W,te,Q,ee)=>{E.type==="svg"?te="svg":E.type==="math"&&(te="mathml"),C==null?_e(E,P,U,H,W,te,Q,ee):A(C,E,H,W,te,Q,ee)},_e=(C,E,P,U,H,W,te,Q)=>{let ee,z;const{props:ve,shapeFlag:se,transition:me,dirs:Oe}=C;if(ee=C.el=u(C.type,W,ve&&ve.is,ve),se&8?p(ee,C.children):se&16&&ae(C.children,ee,null,U,H,Rl(C,W),te,Q),Oe&&nn(C,null,U,"created"),Ne(ee,C,C.scopeId,te,U),ve){for(const Ye in ve)Ye!=="value"&&!ao(Ye)&&a(ee,Ye,null,ve[Ye],W,U);"value"in ve&&a(ee,"value",null,ve.value,W),(z=ve.onVnodeBeforeMount)&&Zs(z,U,C)}({}).NODE_ENV!=="production"&&(lo(ee,"__vnode",C,!0),lo(ee,"__vueParentComponent",U,!0)),Oe&&nn(C,null,U,"beforeMount");const Fe=Hv(H,me);Fe&&me.beforeEnter(ee),i(ee,E,P),((z=ve&&ve.onVnodeMounted)||Fe||Oe)&&Qt(()=>{z&&Zs(z,U,C),Fe&&me.enter(ee),Oe&&nn(C,null,U,"mounted")},H)},Ne=(C,E,P,U,H)=>{if(P&&O(C,P),U)for(let W=0;W<U.length;W++)O(C,U[W]);if(H){let W=H.subTree;if({}.NODE_ENV!=="production"&&W.patchFlag>0&&W.patchFlag&2048&&(W=Bl(W.children)||W),E===W||bf(W.type)&&(W.ssContent===E||W.ssFallback===E)){const te=H.vnode;Ne(C,te,te.scopeId,te.slotScopeIds,H.parent)}}},ae=(C,E,P,U,H,W,te,Q,ee=0)=>{for(let z=ee;z<C.length;z++){const ve=C[z]=Q?Pr(C[z]):ks(C[z]);V(null,ve,E,P,U,H,W,te,Q)}},A=(C,E,P,U,H,W,te)=>{const Q=E.el=C.el;({}).NODE_ENV!=="production"&&(Q.__vnode=E);let{patchFlag:ee,dynamicChildren:z,dirs:ve}=E;ee|=C.patchFlag&16;const se=C.props||et,me=E.props||et;let Oe;if(P&&ln(P,!1),(Oe=me.onVnodeBeforeUpdate)&&Zs(Oe,P,E,C),ve&&nn(E,C,P,"beforeUpdate"),P&&ln(P,!0),{}.NODE_ENV!=="production"&&Ps&&(ee=0,te=!1,z=null),(se.innerHTML&&me.innerHTML==null||se.textContent&&me.textContent==null)&&p(Q,""),z?(we(C.dynamicChildren,z,Q,P,U,Rl(E,H),W),{}.NODE_ENV!=="production"&&So(C,E)):te||be(C,E,Q,null,P,U,Rl(E,H),W,!1),ee>0){if(ee&16)ue(Q,se,me,P,H);else if(ee&2&&se.class!==me.class&&a(Q,"class",null,me.class,H),ee&4&&a(Q,"style",se.style,me.style,H),ee&8){const Fe=E.dynamicProps;for(let Ye=0;Ye<Fe.length;Ye++){const Qe=Fe[Ye],Rt=se[Qe],St=me[Qe];(St!==Rt||Qe==="value")&&a(Q,Qe,Rt,St,H,P)}}ee&1&&C.children!==E.children&&p(Q,E.children)}else!te&&z==null&&ue(Q,se,me,P,H);((Oe=me.onVnodeUpdated)||ve)&&Qt(()=>{Oe&&Zs(Oe,P,E,C),ve&&nn(E,C,P,"updated")},U)},we=(C,E,P,U,H,W,te)=>{for(let Q=0;Q<E.length;Q++){const ee=C[Q],z=E[Q],ve=ee.el&&(ee.type===Ae||!cn(ee,z)||ee.shapeFlag&198)?g(ee.el):P;V(ee,z,ve,null,U,H,W,te,!0)}},ue=(C,E,P,U,H)=>{if(E!==P){if(E!==et)for(const W in E)!ao(W)&&!(W in P)&&a(C,W,E[W],null,H,U);for(const W in P){if(ao(W))continue;const te=P[W],Q=E[W];te!==Q&&W!=="value"&&a(C,W,Q,te,H,U)}"value"in P&&a(C,"value",E.value,P.value,H)}},ze=(C,E,P,U,H,W,te,Q,ee)=>{const z=E.el=C?C.el:c(""),ve=E.anchor=C?C.anchor:c("");let{patchFlag:se,dynamicChildren:me,slotScopeIds:Oe}=E;({}).NODE_ENV!=="production"&&(Ps||se&2048)&&(se=0,ee=!1,me=null),Oe&&(Q=Q?Q.concat(Oe):Oe),C==null?(i(z,P,U),i(ve,P,U),ae(E.children||[],P,ve,H,W,te,Q,ee)):se>0&&se&64&&me&&C.dynamicChildren?(we(C.dynamicChildren,me,P,H,W,te,Q),{}.NODE_ENV!=="production"?So(C,E):(E.key!=null||H&&E===H.subTree)&&So(C,E,!0)):be(C,E,P,ve,H,W,te,Q,ee)},_t=(C,E,P,U,H,W,te,Q,ee)=>{E.slotScopeIds=Q,C==null?E.shapeFlag&512?H.ctx.activate(E,P,U,te,ee):pt(E,P,U,H,W,te,ee):ut(C,E,ee)},pt=(C,E,P,U,H,W,te)=>{const Q=C.component=l1(C,U,H);if({}.NODE_ENV!=="production"&&Q.type.__hmrId&&L_(Q),{}.NODE_ENV!=="production"&&(Ei(C),ur(Q,"mount")),Eo(C)&&(Q.ctx.renderer=ke),{}.NODE_ENV!=="production"&&ur(Q,"init"),c1(Q,!1,te),{}.NODE_ENV!=="production"&&cr(Q,"init"),{}.NODE_ENV!=="production"&&Ps&&(C.el=null),Q.asyncDep){if(H&&H.registerDep(Q,K,te),!C.el){const ee=Q.subTree=T(Ct);J(null,ee,E,P)}}else K(Q,C,E,P,H,W,te);({}).NODE_ENV!=="production"&&(Oi(),cr(Q,"mount"))},ut=(C,E,P)=>{const U=E.component=C.component;if(Yv(C,E,P))if(U.asyncDep&&!U.asyncResolved){({}).NODE_ENV!=="production"&&Ei(E),ye(U,E,P),{}.NODE_ENV!=="production"&&Oi();return}else U.next=E,U.update();else E.el=C.el,U.vnode=E},K=(C,E,P,U,H,W,te)=>{const Q=()=>{if(C.isMounted){let{next:se,bu:me,u:Oe,parent:Fe,vnode:Ye}=C;{const Lt=hf(C);if(Lt){se&&(se.el=Ye.el,ye(C,se,te)),Lt.asyncDep.then(()=>{C.isUnmounted||Q()});return}}let Qe=se,Rt;({}).NODE_ENV!=="production"&&Ei(se||C.vnode),ln(C,!1),se?(se.el=Ye.el,ye(C,se,te)):se=Ye,me&&Nn(me),(Rt=se.props&&se.props.onVnodeBeforeUpdate)&&Zs(Rt,Fe,se,Ye),ln(C,!0),{}.NODE_ENV!=="production"&&ur(C,"render");const St=Ll(C);({}).NODE_ENV!=="production"&&cr(C,"render");const Zt=C.subTree;C.subTree=St,{}.NODE_ENV!=="production"&&ur(C,"patch"),V(Zt,St,g(Zt.el),ie(Zt),C,H,W),{}.NODE_ENV!=="production"&&cr(C,"patch"),se.el=St.el,Qe===null&&Xv(C,St.el),Oe&&Qt(Oe,H),(Rt=se.props&&se.props.onVnodeUpdated)&&Qt(()=>Zs(Rt,Fe,se,Ye),H),{}.NODE_ENV!=="production"&&Ed(C),{}.NODE_ENV!=="production"&&Oi()}else{let se;const{el:me,props:Oe}=E,{bm:Fe,m:Ye,parent:Qe,root:Rt,type:St}=C,Zt=kn(E);if(ln(C,!1),Fe&&Nn(Fe),!Zt&&(se=Oe&&Oe.onVnodeBeforeMount)&&Zs(se,Qe,E),ln(C,!0),me&&Ve){const Lt=()=>{({}).NODE_ENV!=="production"&&ur(C,"render"),C.subTree=Ll(C),{}.NODE_ENV!=="production"&&cr(C,"render"),{}.NODE_ENV!=="production"&&ur(C,"hydrate"),Ve(me,C.subTree,C,H,null),{}.NODE_ENV!=="production"&&cr(C,"hydrate")};Zt&&St.__asyncHydrate?St.__asyncHydrate(me,C,Lt):Lt()}else{Rt.ce&&Rt.ce._def.shadowRoot!==!1&&Rt.ce._injectChildStyle(St),{}.NODE_ENV!=="production"&&ur(C,"render");const Lt=C.subTree=Ll(C);({}).NODE_ENV!=="production"&&cr(C,"render"),{}.NODE_ENV!=="production"&&ur(C,"patch"),V(null,Lt,P,U,C,H,W),{}.NODE_ENV!=="production"&&cr(C,"patch"),E.el=Lt.el}if(Ye&&Qt(Ye,H),!Zt&&(se=Oe&&Oe.onVnodeMounted)){const Lt=E;Qt(()=>Zs(se,Qe,Lt),H)}(E.shapeFlag&256||Qe&&kn(Qe.vnode)&&Qe.vnode.shapeFlag&256)&&C.a&&Qt(C.a,H),C.isMounted=!0,{}.NODE_ENV!=="production"&&j_(C),E=P=U=null}};C.scope.on();const ee=C.effect=new $c(Q);C.scope.off();const z=C.update=ee.run.bind(ee),ve=C.job=ee.runIfDirty.bind(ee);ve.i=C,ve.id=C.uid,ee.scheduler=()=>Si(ve),ln(C,!0),{}.NODE_ENV!=="production"&&(ee.onTrack=C.rtc?se=>Nn(C.rtc,se):void 0,ee.onTrigger=C.rtg?se=>Nn(C.rtg,se):void 0),z()},ye=(C,E,P)=>{E.component=C;const U=C.vnode.props;C.vnode=E,C.next=null,Iv(C,E.props,U,P),Uv(C,E.children,P),Ts(),_d(C),As()},be=(C,E,P,U,H,W,te,Q,ee=!1)=>{const z=C&&C.children,ve=C?C.shapeFlag:0,se=E.children,{patchFlag:me,shapeFlag:Oe}=E;if(me>0){if(me&128){ct(z,se,P,U,H,W,te,Q,ee);return}else if(me&256){wt(z,se,P,U,H,W,te,Q,ee);return}}Oe&8?(ve&16&&R(z,H,W),se!==z&&p(P,se)):ve&16?Oe&16?ct(z,se,P,U,H,W,te,Q,ee):R(z,H,W,!0):(ve&8&&p(P,""),Oe&16&&ae(se,P,U,H,W,te,Q,ee))},wt=(C,E,P,U,H,W,te,Q,ee)=>{C=C||Dn,E=E||Dn;const z=C.length,ve=E.length,se=Math.min(z,ve);let me;for(me=0;me<se;me++){const Oe=E[me]=ee?Pr(E[me]):ks(E[me]);V(C[me],Oe,P,null,H,W,te,Q,ee)}z>ve?R(C,H,W,!0,!1,se):ae(E,P,U,H,W,te,Q,ee,se)},ct=(C,E,P,U,H,W,te,Q,ee)=>{let z=0;const ve=E.length;let se=C.length-1,me=ve-1;for(;z<=se&&z<=me;){const Oe=C[z],Fe=E[z]=ee?Pr(E[z]):ks(E[z]);if(cn(Oe,Fe))V(Oe,Fe,P,null,H,W,te,Q,ee);else break;z++}for(;z<=se&&z<=me;){const Oe=C[se],Fe=E[me]=ee?Pr(E[me]):ks(E[me]);if(cn(Oe,Fe))V(Oe,Fe,P,null,H,W,te,Q,ee);else break;se--,me--}if(z>se){if(z<=me){const Oe=me+1,Fe=Oe<ve?E[Oe].el:U;for(;z<=me;)V(null,E[z]=ee?Pr(E[z]):ks(E[z]),P,Fe,H,W,te,Q,ee),z++}}else if(z>me)for(;z<=se;)ss(C[z],H,W,!0),z++;else{const Oe=z,Fe=z,Ye=new Map;for(z=Fe;z<=me;z++){const Tt=E[z]=ee?Pr(E[z]):ks(E[z]);Tt.key!=null&&({}.NODE_ENV!=="production"&&Ye.has(Tt.key)&&Z("Duplicate keys found during update:",JSON.stringify(Tt.key),"Make sure keys are unique."),Ye.set(Tt.key,z))}let Qe,Rt=0;const St=me-Fe+1;let Zt=!1,Lt=0;const _r=new Array(St);for(z=0;z<St;z++)_r[z]=0;for(z=Oe;z<=se;z++){const Tt=C[z];if(Rt>=St){ss(Tt,H,W,!0);continue}let ys;if(Tt.key!=null)ys=Ye.get(Tt.key);else for(Qe=Fe;Qe<=me;Qe++)if(_r[Qe-Fe]===0&&cn(Tt,E[Qe])){ys=Qe;break}ys===void 0?ss(Tt,H,W,!0):(_r[ys-Fe]=z+1,ys>=Lt?Lt=ys:Zt=!0,V(Tt,E[ys],P,null,H,W,te,Q,ee),Rt++)}const Wn=Zt?$v(_r):Dn;for(Qe=Wn.length-1,z=St-1;z>=0;z--){const Tt=Fe+z,ys=E[Tt],la=Tt+1<ve?E[Tt+1].el:U;_r[z]===0?V(null,ys,P,la,H,W,te,Q,ee):Zt&&(Qe<0||z!==Wn[Qe]?vs(ys,P,la,2):Qe--)}}},vs=(C,E,P,U,H=null)=>{const{el:W,type:te,transition:Q,children:ee,shapeFlag:z}=C;if(z&6){vs(C.component.subTree,E,P,U);return}if(z&128){C.suspense.move(E,P,U);return}if(z&64){te.move(C,E,P,ke);return}if(te===Ae){i(W,E,P);for(let se=0;se<ee.length;se++)vs(ee[se],E,P,U);i(C.anchor,E,P);return}if(te===Io){Ee(C,E,P);return}if(U!==2&&z&1&&Q)if(U===0)Q.beforeEnter(W),i(W,E,P),Qt(()=>Q.enter(W),H);else{const{leave:se,delayLeave:me,afterLeave:Oe}=Q,Fe=()=>{C.ctx.isUnmounted?n(W):i(W,E,P)},Ye=()=>{se(W,()=>{Fe(),Oe&&Oe()})};me?me(W,Fe,Ye):Ye()}else i(W,E,P)},ss=(C,E,P,U=!1,H=!1)=>{const{type:W,props:te,ref:Q,children:ee,dynamicChildren:z,shapeFlag:ve,patchFlag:se,dirs:me,cacheIndex:Oe}=C;if(se===-2&&(H=!1),Q!=null&&(Ts(),wo(Q,null,P,C,!0),As()),Oe!=null&&(E.renderCache[Oe]=void 0),ve&256){E.ctx.deactivate(C);return}const Fe=ve&1&&me,Ye=!kn(C);let Qe;if(Ye&&(Qe=te&&te.onVnodeBeforeUnmount)&&Zs(Qe,E,C),ve&6)cs(C.component,P,U);else{if(ve&128){C.suspense.unmount(P,U);return}Fe&&nn(C,null,E,"beforeUnmount"),ve&64?C.type.remove(C,E,P,ke,U):z&&!z.hasOnce&&(W!==Ae||se>0&&se&64)?R(z,E,P,!1,!0):(W===Ae&&se&384||!H&&ve&16)&&R(ee,E,P),U&&tr(C)}(Ye&&(Qe=te&&te.onVnodeUnmounted)||Fe)&&Qt(()=>{Qe&&Zs(Qe,E,C),Fe&&nn(C,null,E,"unmounted")},P)},tr=C=>{const{type:E,el:P,anchor:U,transition:H}=C;if(E===Ae){({}).NODE_ENV!=="production"&&C.patchFlag>0&&C.patchFlag&2048&&H&&!H.persisted?C.children.forEach(te=>{te.type===Ct?n(te.el):tr(te)}):Fs(P,U);return}if(E===Io){X(C);return}const W=()=>{n(P),H&&!H.persisted&&H.afterLeave&&H.afterLeave()};if(C.shapeFlag&1&&H&&!H.persisted){const{leave:te,delayLeave:Q}=H,ee=()=>te(P,W);Q?Q(C.el,W,ee):ee()}else W()},Fs=(C,E)=>{let P;for(;C!==E;)P=v(C),n(C),C=P;n(E)},cs=(C,E,P)=>{({}).NODE_ENV!=="production"&&C.type.__hmrId&&B_(C);const{bum:U,scope:H,job:W,subTree:te,um:Q,m:ee,a:z,parent:ve,slots:{__:se}}=C;pf(ee),pf(z),U&&Nn(U),ve&&he(se)&&se.forEach(me=>{ve.renderCache[me]=void 0}),H.stop(),W&&(W.flags|=8,ss(te,C,E,P)),Q&&Qt(Q,E),Qt(()=>{C.isUnmounted=!0},E),E&&E.pendingBranch&&!E.isUnmounted&&C.asyncDep&&!C.asyncResolved&&C.suspenseId===E.pendingId&&(E.deps--,E.deps===0&&E.resolve()),{}.NODE_ENV!=="production"&&G_(C)},R=(C,E,P,U=!1,H=!1,W=0)=>{for(let te=W;te<C.length;te++)ss(C[te],E,P,U,H)},ie=C=>{if(C.shapeFlag&6)return ie(C.component.subTree);if(C.shapeFlag&128)return C.suspense.next();const E=v(C.anchor||C.el),P=E&&E[Dd];return P?v(P):E};let ne=!1;const pe=(C,E,P)=>{C==null?E._vnode&&ss(E._vnode,null,null,!0):V(E._vnode||null,C,E,null,null,null,P),E._vnode=C,ne||(ne=!0,_d(),vd(),ne=!1)},ke={p:V,um:ss,m:vs,r:tr,mt:pt,mc:ae,pc:be,pbc:we,n:ie,o:e};let rt,Ve;return t&&([rt,Ve]=t(ke)),{render:pe,hydrate:rt,createApp:xv(pe,rt)}}function Rl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function ln({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Hv(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function So(e,t,s=!1){const i=e.children,n=t.children;if(he(i)&&he(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Pr(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&So(u,c)),c.type===Do&&(c.el=u.el),c.type===Ct&&!c.el&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.el&&(c.el.__vnode=c)}}function $v(e){const t=e.slice(),s=[0];let i,n,a,u,c;const h=e.length;for(i=0;i<h;i++){const _=e[i];if(_!==0){if(n=s[s.length-1],e[n]<_){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<_?a=c+1:u=c;_<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function hf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:hf(t)}function pf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Wv=Symbol.for("v-scx"),jv=()=>{{const e=Ks(Wv);return e||{}.NODE_ENV!=="production"&&Z("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function zv(e,t){return Fl(e,null,t)}function Rn(e,t,s){return{}.NODE_ENV!=="production"&&!Se(t)&&Z("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Fl(e,t,s)}function Fl(e,t,s=et){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Z('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&Z('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Z('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=ft({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Z);const h=t&&i||!t&&a!=="post";let _;if(Mo){if(a==="sync"){const O=jv();_=O.__watcherHandles||(O.__watcherHandles=[])}else if(!h){const O=()=>{};return O.stop=Dt,O.resume=Dt,O.pause=Dt,O}}const p=Nt;c.call=(O,k,V)=>Ms(O,p,k,V);let g=!1;a==="post"?c.scheduler=O=>{Qt(O,p&&p.suspense)}:a!=="sync"&&(g=!0,c.scheduler=(O,k)=>{k?O():Si(O)}),c.augmentJob=O=>{t&&(O.flags|=4),g&&(O.flags|=2,p&&(O.id=p.uid,O.i=p))};const v=T_(e,t,c);return Mo&&(_?_.push(v):h&&v()),v}function Gv(e,t,s){const i=this.proxy,n=at(e)?e.includes(".")?mf(i,e):()=>i[e]:e.bind(i,i);let a;Se(t)?a=t:(a=t.handler,s=t);const u=Ao(this),c=Fl(n,a.bind(i),s);return u(),c}function mf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const Kv=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${jt(t)}Modifiers`]||e[`${Dr(t)}Modifiers`];function Qv(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||et;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[g]}=e;if(p)if(!(t in p))(!g||!(Yr(jt(t))in g))&&Z(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Yr(jt(t))}" prop.`);else{const v=p[t];Se(v)&&(v(...s)||Z(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&Kv(i,t.slice(7));if(u&&(u.trim&&(n=s.map(p=>at(p)?p.trim():p)),u.number&&(n=s.map(fi))),{}.NODE_ENV!=="production"&&Z_(e,t,n),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Yr(p)]&&Z(`Event "${p}" is emitted in component ${$i(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Dr(t)}" instead of "${t}".`)}let c,h=i[c=Yr(t)]||i[c=Yr(jt(t))];!h&&a&&(h=i[c=Yr(Dr(t))]),h&&Ms(h,e,6,n);const _=i[c+"Once"];if(_){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ms(_,e,6,n)}}function gf(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!Se(e)){const h=_=>{const p=gf(_,t,!0);p&&(c=!0,ft(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Ze(e)&&i.set(e,null),null):(he(a)?a.forEach(h=>u[h]=null):ft(u,a),Ze(e)&&i.set(e,u),u)}function Fi(e,t){return!e||!oo(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ke(e,t[0].toLowerCase()+t.slice(1))||Ke(e,Dr(t))||Ke(e,t))}let Ul=!1;function Ui(){Ul=!0}function Ll(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:h,render:_,renderCache:p,props:g,data:v,setupState:O,ctx:k,inheritAttrs:V}=e,re=Ni(e);let J,oe;({}).NODE_ENV!=="production"&&(Ul=!1);try{if(s.shapeFlag&4){const X=n||i,fe={}.NODE_ENV!=="production"&&O.__isScriptSetup?new Proxy(X,{get(_e,Ne,ae){return Z(`Property '${String(Ne)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(_e,Ne,ae)}}):X;J=ks(_.call(fe,X,p,{}.NODE_ENV!=="production"?Ws(g):g,O,v,k)),oe=c}else{const X=t;({}).NODE_ENV!=="production"&&c===g&&Ui(),J=ks(X.length>1?X({}.NODE_ENV!=="production"?Ws(g):g,{}.NODE_ENV!=="production"?{get attrs(){return Ui(),Ws(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):X({}.NODE_ENV!=="production"?Ws(g):g,null)),oe=t.props?c:Zv(c)}}catch(X){No.length=0,_o(X,e,1),J=T(Ct)}let Y=J,Ee;if({}.NODE_ENV!=="production"&&J.patchFlag>0&&J.patchFlag&2048&&([Y,Ee]=_f(J)),oe&&V!==!1){const X=Object.keys(oe),{shapeFlag:fe}=Y;if(X.length){if(fe&7)a&&X.some(ci)&&(oe=Jv(oe,a)),Y=Qs(Y,oe,!1,!0);else if({}.NODE_ENV!=="production"&&!Ul&&Y.type!==Ct){const _e=Object.keys(c),Ne=[],ae=[];for(let A=0,we=_e.length;A<we;A++){const ue=_e[A];oo(ue)?ci(ue)||Ne.push(ue[2].toLowerCase()+ue.slice(3)):ae.push(ue)}ae.length&&Z(`Extraneous non-props attributes (${ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ne.length&&Z(`Extraneous non-emits event listeners (${Ne.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!vf(Y)&&Z("Runtime directive used on component with non-element root node. The directives will not function as intended."),Y=Qs(Y,null,!1,!0),Y.dirs=Y.dirs?Y.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!vf(Y)&&Z("Component inside <Transition> renders non-element root node that cannot be animated."),Co(Y,s.transition)),{}.NODE_ENV!=="production"&&Ee?Ee(Y):J=Y,Ni(re),J}const _f=e=>{const t=e.children,s=e.dynamicChildren,i=Bl(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return _f(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[ks(i),u]};function Bl(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(un(n)){if(n.type!==Ct||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Bl(s.children)}}else return}return s}const Zv=e=>{let t;for(const s in e)(s==="class"||s==="style"||oo(s))&&((t||(t={}))[s]=e[s]);return t},Jv=(e,t)=>{const s={};for(const i in e)(!ci(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},vf=e=>e.shapeFlag&7||e.type===Ct;function Yv(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:h}=t,_=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&Ps||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?yf(i,u,_):!!u;if(h&8){const p=t.dynamicProps;for(let g=0;g<p.length;g++){const v=p[g];if(u[v]!==i[v]&&!Fi(_,v))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?yf(i,u,_):!0:!!u;return!1}function yf(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!Fi(s,a))return!0}return!1}function Xv({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const bf=e=>e.__isSuspense;function e1(e,t){t&&t.pendingBranch?he(e)?t.effects.push(...e):t.effects.push(e):gd(e)}const Ae=Symbol.for("v-fgt"),Do=Symbol.for("v-txt"),Ct=Symbol.for("v-cmt"),Io=Symbol.for("v-stc"),No=[];let ls=null;function x(e=!1){No.push(ls=e?null:[])}function t1(){No.pop(),ls=No[No.length-1]||null}let To=1;function Cf(e,t=!1){To+=e,e<0&&ls&&t&&(ls.hasOnce=!0)}function wf(e){return e.dynamicChildren=To>0?ls||Dn:null,t1(),To>0&&ls&&ls.push(e),e}function D(e,t,s,i,n,a){return wf(f(e,t,s,i,n,a,!0))}function ht(e,t,s,i,n){return wf(T(e,t,s,i,n,!0))}function un(e){return e?e.__v_isVNode===!0:!1}function cn(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=Di.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const s1=(...e)=>Of(...e),Ef=({key:e})=>e??null,Li=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?at(e)||xt(e)||Se(e)?{i:bt,r:e,k:t,f:!!s}:e:null);function f(e,t=null,s=null,i=0,n=null,a=e===Ae?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ef(t),ref:t&&Li(t),scopeId:xd,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:bt};return c?(ql(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=at(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Z("VNode created with invalid key (NaN). VNode type:",h.type),To>0&&!u&&ls&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&ls.push(h),h}const T={}.NODE_ENV!=="production"?s1:Of;function Of(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===hv)&&({}.NODE_ENV!=="production"&&!e&&Z(`Invalid vnode type when creating vnode: ${e}.`),e=Ct),un(e)){const c=Qs(e,t,!0);return s&&ql(c,s),To>0&&!a&&ls&&(c.shapeFlag&6?ls[ls.indexOf(e)]=c:ls.push(c)),c.patchFlag=-2,c}if(Af(e)&&(e=e.__vccOpts),t){t=r1(t);let{class:c,style:h}=t;c&&!at(c)&&(t.class=ce(c)),Ze(h)&&(yi(h)&&!he(h)&&(h=ft({},h)),t.style=as(h))}const u=at(e)?1:bf(e)?128:Id(e)?64:Ze(e)?4:Se(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&yi(e)&&(e=Te(e),Z("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),f(e,t,s,i,n,u,a,!0)}function r1(e){return e?yi(e)||rf(e)?ft({},e):e:null}function Qs(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:h}=e,_=t?o1(n||{},t):n,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:_,key:_&&Ef(_),ref:t&&t.ref?s&&a?he(a)?a.concat(Li(t)):[a,Li(t)]:Li(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&he(c)?c.map(xf):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ae?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qs(e.ssContent),ssFallback:e.ssFallback&&Qs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&Co(p,h.clone(p)),p}function xf(e){const t=Qs(e);return he(e.children)&&(t.children=e.children.map(xf)),t}function We(e=" ",t=0){return T(Do,null,e,t)}function n1(e,t){const s=T(Io,null,e);return s.staticCount=t,s}function G(e="",t=!1){return t?(x(),ht(Ct,null,e)):T(Ct,null,e)}function ks(e){return e==null||typeof e=="boolean"?T(Ct):he(e)?T(Ae,null,e.slice()):un(e)?Pr(e):T(Do,null,String(e))}function Pr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qs(e)}function ql(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(he(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),ql(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!rf(t)?t._ctx=bt:n===3&&bt&&(bt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Se(t)?(t={default:t,_ctx:bt},s=32):(t=String(t),i&64?(s=16,t=[We(t)]):s=8);e.children=t,e.shapeFlag|=s}function o1(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=ce([t.class,i.class]));else if(n==="style")t.style=as([t.style,i.style]);else if(oo(n)){const a=t[n],u=i[n];u&&a!==u&&!(he(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Zs(e,t,s,i=null){Ms(e,t,7,[s,i])}const i1=ef();let a1=0;function l1(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||i1,a={uid:a1++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Hc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:of(i,n),emitsOptions:gf(i,n),emit:null,emitted:null,propsDefaults:et,inheritAttrs:i.inheritAttrs,ctx:et,data:et,props:et,attrs:et,slots:et,refs:et,setupState:et,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=mv(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Qv.bind(null,a),e.ce&&e.ce(a),a}let Nt=null;const Bi=()=>Nt||bt;let qi,Hl;{const e=uo(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};qi=t("__VUE_INSTANCE_SETTERS__",s=>Nt=s),Hl=t("__VUE_SSR_SETTERS__",s=>Mo=s)}const Ao=e=>{const t=Nt;return qi(e),e.scope.on(),()=>{e.scope.off(),qi(t)}},Sf=()=>{Nt&&Nt.scope.off(),qi(null)},u1=or("slot,component");function $l(e,{isNativeTag:t}){(u1(e)||t(e))&&Z("Do not use built-in or reserved HTML elements as component id: "+e)}function Df(e){return e.vnode.shapeFlag&4}let Mo=!1;function c1(e,t=!1,s=!1){t&&Hl(t);const{props:i,children:n}=e.vnode,a=Df(e);Sv(e,i,a,t),Fv(e,n,s||t);const u=a?d1(e,t):void 0;return t&&Hl(!1),u}function d1(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&$l(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)$l(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Sd(a[u])}i.compilerOptions&&f1()&&Z('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Kd),{}.NODE_ENV!=="production"&&gv(e);const{setup:n}=i;if(n){Ts();const a=e.setupContext=n.length>1?p1(e):null,u=Ao(e),c=An(n,e,0,[{}.NODE_ENV!=="production"?Ws(e.props):e.props,a]),h=Ya(c);if(As(),u(),(h||e.sp)&&!kn(e)&&qd(e),h){if(c.then(Sf,Sf),t)return c.then(_=>{If(e,_,t)}).catch(_=>{_o(_,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const _=(s=i.name)!=null?s:"Anonymous";Z(`Component <${_}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else If(e,c,t)}else Nf(e,t)}function If(e,t,s){Se(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ze(t)?({}.NODE_ENV!=="production"&&un(t)&&Z("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=fd(t),{}.NODE_ENV!=="production"&&_v(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Z(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Nf(e,s)}let Wl;const f1=()=>!Wl;function Nf(e,t,s){const i=e.type;if(!e.render){if(!t&&Wl&&!i.render){const n=i.template||Tl(e).template;if(n){({}).NODE_ENV!=="production"&&ur(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,_=ft(ft({isCustomElement:a,delimiters:c},u),h);i.render=Wl(n,_),{}.NODE_ENV!=="production"&&cr(e,"compile")}}e.render=i.render||Dt}{const n=Ao(e);Ts();try{yv(e)}finally{As(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Dt&&!t&&(i.template?Z('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Z("Component is missing template or render function: ",i))}const Tf={}.NODE_ENV!=="production"?{get(e,t){return Ui(),It(e,"get",""),e[t]},set(){return Z("setupContext.attrs is readonly."),!1},deleteProperty(){return Z("setupContext.attrs is readonly."),!1}}:{get(e,t){return It(e,"get",""),e[t]}};function h1(e){return new Proxy(e.slots,{get(t,s){return It(e,"get","$slots"),t[s]}})}function p1(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Z("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(he(s)?i="array":xt(s)&&(i="ref")),i!=="object"&&Z(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Tf))},get slots(){return i||(i=h1(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Tf),slots:e.slots,emit:e.emit,expose:t}}function Hi(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fd(pl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in an)return an[s](e)},has(t,s){return s in t||s in an}})):e.proxy}const m1=/(?:^|[-_])(\w)/g,g1=e=>e.replace(m1,t=>t.toUpperCase()).replace(/[-_]/g,"");function jl(e,t=!0){return Se(e)?e.displayName||e.name:e.name||t&&e.__name}function $i(e,t,s=!1){let i=jl(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?g1(i):s?"App":"Anonymous"}function Af(e){return Se(e)&&"__vccOpts"in e}const Vs=(e,t)=>{const s=I_(e,t,Mo);if({}.NODE_ENV!=="production"){const i=Bi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function zl(e,t,s){const i=arguments.length;return i===2?Ze(t)&&!he(t)?un(t)?T(e,null,[t]):T(e,t):T(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&un(s)&&(s=[s]),T(e,t,s))}function _1(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(g){if(!Ze(g))return null;if(g.__isVue)return["div",e,"VueInstance"];if(xt(g)){Ts();const v=g.value;return As(),["div",{},["span",e,p(g)],"<",c(v),">"]}else{if(en(g))return["div",{},["span",e,zt(g)?"ShallowReactive":"Reactive"],"<",c(g),`>${js(g)?" (readonly)":""}`];if(js(g))return["div",{},["span",e,zt(g)?"ShallowReadonly":"Readonly"],"<",c(g),">"]}return null},hasBody(g){return g&&g.__isVue},body(g){if(g&&g.__isVue)return["div",{},...a(g.$)]}};function a(g){const v=[];g.type.props&&g.props&&v.push(u("props",Te(g.props))),g.setupState!==et&&v.push(u("setup",g.setupState)),g.data!==et&&v.push(u("data",Te(g.data)));const O=h(g,"computed");O&&v.push(u("computed",O));const k=h(g,"inject");return k&&v.push(u("injected",k)),v.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:g}]]),v}function u(g,v){return v=ft({},v),Object.keys(v).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},g],["div",{style:"padding-left:1.25em"},...Object.keys(v).map(O=>["div",{},["span",i,O+": "],c(v[O],!1)])]]:["span",{}]}function c(g,v=!0){return typeof g=="number"?["span",t,g]:typeof g=="string"?["span",s,JSON.stringify(g)]:typeof g=="boolean"?["span",i,g]:Ze(g)?["object",{object:v?Te(g):g}]:["span",s,String(g)]}function h(g,v){const O=g.type;if(Se(O))return;const k={};for(const V in g.ctx)_(O,V,v)&&(k[V]=g.ctx[V]);return k}function _(g,v,O){const k=g[O];if(he(k)&&k.includes(v)||Ze(k)&&v in k||g.extends&&_(g.extends,v,O)||g.mixins&&g.mixins.some(V=>_(V,v,O)))return!0}function p(g){return zt(g)?"ShallowRef":g.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const Mf="3.5.17",Js={}.NODE_ENV!=="production"?Z:Dt;/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Gl;const Pf=typeof window<"u"&&window.trustedTypes;if(Pf)try{Gl=Pf.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Js(`Error creating trusted types policy: ${e}`)}const kf=Gl?e=>Gl.createHTML(e):e=>e,v1="http://www.w3.org/2000/svg",y1="http://www.w3.org/1998/Math/MathML",dr=typeof document<"u"?document:null,Vf=dr&&dr.createElement("template"),b1={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?dr.createElementNS(v1,e):t==="mathml"?dr.createElementNS(y1,e):s?dr.createElement(e,{is:s}):dr.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>dr.createTextNode(e),createComment:e=>dr.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>dr.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{Vf.innerHTML=kf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=Vf.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},kr="transition",Po="animation",ko=Symbol("_vtc"),Rf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},C1=ft({},kd,Rf),Ff=(e=>(e.displayName="Transition",e.props=C1,e))((e,{slots:t})=>zl(tv,w1(e),t)),dn=(e,t=[])=>{he(e)?e.forEach(s=>s(...t)):e&&e(...t)},Uf=e=>e?he(e)?e.some(t=>t.length>1):e.length>1:!1;function w1(e){const t={};for(const ue in e)ue in Rf||(t[ue]=e[ue]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:_=u,appearToClass:p=c,leaveFromClass:g=`${s}-leave-from`,leaveActiveClass:v=`${s}-leave-active`,leaveToClass:O=`${s}-leave-to`}=e,k=E1(n),V=k&&k[0],re=k&&k[1],{onBeforeEnter:J,onEnter:oe,onEnterCancelled:Y,onLeave:Ee,onLeaveCancelled:X,onBeforeAppear:fe=J,onAppear:_e=oe,onAppearCancelled:Ne=Y}=t,ae=(ue,ze,_t,pt)=>{ue._enterCancelled=pt,fn(ue,ze?p:c),fn(ue,ze?_:u),_t&&_t()},A=(ue,ze)=>{ue._isLeaving=!1,fn(ue,g),fn(ue,O),fn(ue,v),ze&&ze()},we=ue=>(ze,_t)=>{const pt=ue?_e:oe,ut=()=>ae(ze,ue,_t);dn(pt,[ze,ut]),Lf(()=>{fn(ze,ue?h:a),fr(ze,ue?p:c),Uf(pt)||Bf(ze,i,V,ut)})};return ft(t,{onBeforeEnter(ue){dn(J,[ue]),fr(ue,a),fr(ue,u)},onBeforeAppear(ue){dn(fe,[ue]),fr(ue,h),fr(ue,_)},onEnter:we(!1),onAppear:we(!0),onLeave(ue,ze){ue._isLeaving=!0;const _t=()=>A(ue,ze);fr(ue,g),ue._enterCancelled?(fr(ue,v),$f()):($f(),fr(ue,v)),Lf(()=>{ue._isLeaving&&(fn(ue,g),fr(ue,O),Uf(Ee)||Bf(ue,i,re,_t))}),dn(Ee,[ue,_t])},onEnterCancelled(ue){ae(ue,!1,void 0,!0),dn(Y,[ue])},onAppearCancelled(ue){ae(ue,!0,void 0,!0),dn(Ne,[ue])},onLeaveCancelled(ue){A(ue),dn(X,[ue])}})}function E1(e){if(e==null)return null;if(Ze(e))return[Kl(e.enter),Kl(e.leave)];{const t=Kl(e);return[t,t]}}function Kl(e){const t=jg(e);return{}.NODE_ENV!=="production"&&V_(t,"<transition> explicit duration"),t}function fr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[ko]||(e[ko]=new Set)).add(t)}function fn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[ko];s&&(s.delete(t),s.size||(e[ko]=void 0))}function Lf(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let O1=0;function Bf(e,t,s,i){const n=e._endId=++O1,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=x1(e,t);if(!u)return i();const _=u+"end";let p=0;const g=()=>{e.removeEventListener(_,v),a()},v=O=>{O.target===e&&++p>=h&&g()};setTimeout(()=>{p<h&&g()},c+1),e.addEventListener(_,v)}function x1(e,t){const s=window.getComputedStyle(e),i=k=>(s[k]||"").split(", "),n=i(`${kr}Delay`),a=i(`${kr}Duration`),u=qf(n,a),c=i(`${Po}Delay`),h=i(`${Po}Duration`),_=qf(c,h);let p=null,g=0,v=0;t===kr?u>0&&(p=kr,g=u,v=a.length):t===Po?_>0&&(p=Po,g=_,v=h.length):(g=Math.max(u,_),p=g>0?u>_?kr:Po:null,v=p?p===kr?a.length:h.length:0);const O=p===kr&&/\b(transform|all)(,|$)/.test(i(`${kr}Property`).toString());return{type:p,timeout:g,propCount:v,hasTransform:O}}function qf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>Hf(s)+Hf(e[i])))}function Hf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function $f(){return document.body.offsetHeight}function S1(e,t,s){const i=e[ko];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Wi=Symbol("_vod"),Wf=Symbol("_vsh"),Ql={beforeMount(e,{value:t},{transition:s}){e[Wi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):Vo(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),Vo(e,!0),i.enter(e)):i.leave(e,()=>{Vo(e,!1)}):Vo(e,t))},beforeUnmount(e,{value:t}){Vo(e,t)}};({}).NODE_ENV!=="production"&&(Ql.name="show");function Vo(e,t){e.style.display=t?e[Wi]:"none",e[Wf]=!t}const D1=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),I1=/(^|;)\s*display\s*:/;function N1(e,t,s){const i=e.style,n=at(s);let a=!1;if(s&&!n){if(t)if(at(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&ji(i,c,"")}else for(const u in t)s[u]==null&&ji(i,u,"");for(const u in s)u==="display"&&(a=!0),ji(i,u,s[u])}else if(n){if(t!==s){const u=i[D1];u&&(s+=";"+u),i.cssText=s,a=I1.test(s)}}else t&&e.removeAttribute("style");Wi in e&&(e[Wi]=a?i.display:"",e[Wf]&&(i.display="none"))}const T1=/[^\\];\s*$/,jf=/\s*!important$/;function ji(e,t,s){if(he(s))s.forEach(i=>ji(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&T1.test(s)&&Js(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=A1(e,t);jf.test(s)?e.setProperty(Dr(i),s.replace(jf,""),"important"):e[i]=s}}const zf=["Webkit","Moz","ms"],Zl={};function A1(e,t){const s=Zl[t];if(s)return s;let i=jt(t);if(i!=="filter"&&i in e)return Zl[t]=i;i=Jr(i);for(let n=0;n<zf.length;n++){const a=zf[n]+i;if(a in e)return Zl[t]=a}return t}const Gf="http://www.w3.org/1999/xlink";function Kf(e,t,s,i,n,a=s_(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Gf,t.slice(6,t.length)):e.setAttributeNS(Gf,t,s):s==null||a&&!Lc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":Is(s)?String(s):s)}function Qf(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?kf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Lc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Js(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function Vr(e,t,s,i){e.addEventListener(t,s,i)}function M1(e,t,s,i){e.removeEventListener(t,s,i)}const Zf=Symbol("_vei");function P1(e,t,s,i,n=null){const a=e[Zf]||(e[Zf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Yf(i,t):i;else{const[c,h]=k1(t);if(i){const _=a[t]=F1({}.NODE_ENV!=="production"?Yf(i,t):i,n);Vr(e,c,_,h)}else u&&(M1(e,c,u,h),a[t]=void 0)}}const Jf=/(?:Once|Passive|Capture)$/;function k1(e){let t;if(Jf.test(e)){t={};let i;for(;i=e.match(Jf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Dr(e.slice(2)),t]}let Jl=0;const V1=Promise.resolve(),R1=()=>Jl||(V1.then(()=>Jl=0),Jl=Date.now());function F1(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ms(U1(i,s.value),t,5,[i])};return s.value=e,s.attached=R1(),s}function Yf(e,t){return Se(e)||he(e)?e:(Js(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Dt)}function U1(e,t){if(he(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const Xf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,L1=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?S1(e,i,u):t==="style"?N1(e,s,i):oo(t)?ci(t)||P1(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):B1(e,t,i,u))?(Qf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Kf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!at(i))?Qf(e,jt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Kf(e,t,i,u))};function B1(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Xf(t)&&Se(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Xf(t)&&at(s)?!1:t in e}const Fn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return he(t)?s=>Nn(t,s):t};function q1(e){e.target.composing=!0}function eh(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const hr=Symbol("_assign"),ts={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[hr]=Fn(n);const a=i||n.props&&n.props.type==="number";Vr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=fi(c)),e[hr](c)}),s&&Vr(e,"change",()=>{e.value=e.value.trim()}),t||(Vr(e,"compositionstart",q1),Vr(e,"compositionend",eh),Vr(e,"change",eh))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[hr]=Fn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?fi(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===h)||(e.value=h))}},zi={deep:!0,created(e,t,s){e[hr]=Fn(s),Vr(e,"change",()=>{const i=e._modelValue,n=Ro(e),a=e.checked,u=e[hr];if(he(i)){const c=tl(i,n),h=c!==-1;if(a&&!h)u(i.concat(n));else if(!a&&h){const _=[...i];_.splice(c,1),u(_)}}else if(In(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(rh(e,a))})},mounted:th,beforeUpdate(e,t,s){e[hr]=Fn(s),th(e,t,s)}};function th(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(he(t))n=tl(t,i.props.value)>-1;else if(In(t))n=t.has(i.props.value);else{if(t===s)return;n=co(t,rh(e,!0))}e.checked!==n&&(e.checked=n)}const Yl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=In(t);Vr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?fi(Ro(u)):Ro(u));e[hr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,_l(()=>{e._assigning=!1})}),e[hr]=Fn(i)},mounted(e,{value:t}){sh(e,t)},beforeUpdate(e,t,s){e[hr]=Fn(s)},updated(e,{value:t}){e._assigning||sh(e,t)}};function sh(e,t){const s=e.multiple,i=he(t);if(s&&!i&&!In(t)){({}).NODE_ENV!=="production"&&Js(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Ro(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(_=>String(_)===String(c)):u.selected=tl(t,c)>-1}else u.selected=t.has(c);else if(co(Ro(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Ro(e){return"_value"in e?e._value:e.value}function rh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const H1=["ctrl","shift","alt","meta"],$1={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>H1.some(s=>e[`${s}Key`]&&!t.includes(s))},kt=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=$1[t[u]];if(c&&c(n,t))return}return e(n,...a)})},W1=ft({patchProp:L1},b1);let nh;function j1(){return nh||(nh=Bv(W1))}const z1=(...e)=>{const t=j1().createApp(...e);({}).NODE_ENV!=="production"&&(K1(t),Q1(t));const{mount:s}=t;return t.mount=i=>{const n=Z1(i);if(!n)return;const a=t._component;!Se(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,G1(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function G1(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function K1(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Xg(t)||e_(t)||t_(t),writable:!1})}function Q1(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Js("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Js(i),s},set(){Js(i)}})}}function Z1(e){if(at(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Js(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Js('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function J1(){_1()}({}).NODE_ENV!=="production"&&J1();var Y1=!1;function X1(){return oh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function oh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const ey=typeof Proxy=="function",ty="devtools-plugin:setup",sy="plugin:settings:set";let Un,Xl;function ry(){var e;return Un!==void 0||(typeof window<"u"&&window.performance?(Un=!0,Xl=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Un=!0,Xl=globalThis.perf_hooks.performance):Un=!1),Un}function ny(){return ry()?Xl.now():Date.now()}class oy{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return ny()}},s&&s.on(sy,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(_=>{this.targetQueue.push({method:c,args:h,resolve:_})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function eu(e,t){const s=e,i=oh(),n=X1(),a=ey&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(ty,e,t);else{const u=a?new oy(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const iy={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var hn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(hn||(hn={}));const tu=typeof window<"u",ih=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function ay(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function su(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){uh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function ah(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function Gi(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const Ki=typeof navigator=="object"?navigator:{userAgent:""},lh=(()=>/Macintosh/.test(Ki.userAgent)&&/AppleWebKit/.test(Ki.userAgent)&&!/Safari/.test(Ki.userAgent))(),uh=tu?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!lh?ly:"msSaveOrOpenBlob"in Ki?uy:cy:()=>{};function ly(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?ah(i.href)?su(e,t,s):(i.target="_blank",Gi(i)):Gi(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){Gi(i)},0))}function uy(e,t="download",s){if(typeof e=="string")if(ah(e))su(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){Gi(i)})}else navigator.msSaveOrOpenBlob(ay(e,s),t)}function cy(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return su(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(ih.HTMLElement))||"safari"in ih,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||lh)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function Vt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function ru(e){return"_a"in e&&"install"in e}function ch(){if(!("clipboard"in navigator))return Vt("Your browser doesn't support the Clipboard API","error"),!0}function dh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(Vt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function dy(e){if(!ch())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),Vt("Global state copied to clipboard.")}catch(t){if(dh(t))return;Vt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function fy(e){if(!ch())try{fh(e,JSON.parse(await navigator.clipboard.readText())),Vt("Global state pasted from clipboard.")}catch(t){if(dh(t))return;Vt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function hy(e){try{uh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){Vt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let pr;function py(){pr||(pr=document.createElement("input"),pr.type="file",pr.accept=".json");function e(){return new Promise((t,s)=>{pr.onchange=async()=>{const i=pr.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},pr.oncancel=()=>t(null),pr.onerror=s,pr.click()})}return e}async function my(e){try{const s=await py()();if(!s)return;const{text:i,file:n}=s;fh(e,JSON.parse(i)),Vt(`Global state imported from "${n.name}".`)}catch(t){Vt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function fh(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Rs(e){return{_custom:{display:e}}}const hh="🍍 Pinia (root)",Qi="_root";function gy(e){return ru(e)?{id:Qi,label:hh}:{id:e.$id,label:e.$id}}function _y(e){if(ru(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function vy(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Rs(e.type),key:Rs(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function yy(e){switch(e){case hn.direct:return"mutation";case hn.patchFunction:return"$patch";case hn.patchObject:return"$patch";default:return"unknown"}}let Ln=!0;const Zi=[],pn="pinia:mutations",Ht="pinia",{assign:by}=Object,Ji=e=>"🍍 "+e;function Cy(e,t){eu({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Zi,app:e},s=>{typeof s.now!="function"&&Vt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:pn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:Ht,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{dy(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await fy(t),s.sendInspectorTree(Ht),s.sendInspectorState(Ht)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{hy(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await my(t),s.sendInspectorTree(Ht),s.sendInspectorState(Ht)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?Vt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),Vt(`Store "${i}" reset.`)):Vt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Ji(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Te(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,_)=>(h[_]=c.$state[_],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Ji(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,_)=>{try{h[_]=c[_]}catch(p){h[_]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===Ht){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):hh.toLowerCase().includes(i.filter.toLowerCase())):n).map(gy)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===Ht){const n=i.nodeId===Qi?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==Qi&&(globalThis.$store=Te(n)),i.state=_y(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===Ht){const a=i.nodeId===Qi?t:t._s.get(i.nodeId);if(!a)return Vt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;ru(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Ln=!1,i.set(a,u,i.state.value),Ln=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return Vt(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return Vt(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Ln=!1,i.set(a,u,i.state.value),Ln=!0}})})}function wy(e,t){Zi.includes(Ji(t.$id))||Zi.push(Ji(t.$id)),eu({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Zi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:_})=>{const p=ph++;s.addTimelineEvent({layerId:pn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Rs(t.$id),action:Rs(h),args:_},groupId:p}}),u(g=>{Rr=void 0,s.addTimelineEvent({layerId:pn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Rs(t.$id),action:Rs(h),args:_,result:g},groupId:p}})}),c(g=>{Rr=void 0,s.addTimelineEvent({layerId:pn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Rs(t.$id),action:Rs(h),args:_,error:g},groupId:p}})})},!0),t._customProperties.forEach(u=>{Rn(()=>Nr(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(Ht),Ln&&s.addTimelineEvent({layerId:pn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Rr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(Ht),!Ln)return;const _={time:i(),title:yy(c),data:by({store:Rs(t.$id)},vy(u)),groupId:Rr};c===hn.patchFunction?_.subtitle="⤵️":c===hn.patchObject?_.subtitle="🧩":u&&!Array.isArray(u)&&(_.subtitle=u.type),u&&(_.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:pn,event:_})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=pl(u=>{n(u),s.addTimelineEvent({layerId:pn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Rs(t.$id),info:Rs("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(Ht),s.sendInspectorState(Ht)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(Ht),s.sendInspectorState(Ht),s.getSettings().logStoreChanges&&Vt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(Ht),s.sendInspectorState(Ht),s.getSettings().logStoreChanges&&Vt(`"${t.$id}" store installed 🆕`)})}let ph=0,Rr;function mh(e,t,s){const i=t.reduce((n,a)=>(n[a]=Te(e)[a],n),{});for(const n in i)e[n]=function(){const a=ph,u=s?new Proxy(e,{get(...h){return Rr=a,Reflect.get(...h)},set(...h){return Rr=a,Reflect.set(...h)}}):e;Rr=a;const c=i[n].apply(u,arguments);return Rr=void 0,c}}function Ey({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){mh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Te(t)._hotUpdate=function(n){i.apply(this,arguments),mh(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}wy(e,t)}}function Oy(){const e=n_(!0),t=e.run(()=>cd({}));let s=[],i=[];const n=pl({install(a){n._a=a,a.provide(iy,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&tu&&Cy(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Y1?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&tu&&typeof Proxy<"u"&&n.use(Ey),n}const qP="",Pe=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},xy={name:"App",mounted(){}},Sy={id:"app"};function Dy(e,t,s,i,n,a){const u=$("router-view");return x(),D("div",Sy,[T(u)])}const Iy=Pe(xy,[["render",Dy]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const mr=typeof document<"u";function gh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ny(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&gh(e.default)}const Je=Object.assign;function nu(e,t){const s={};for(const i in t){const n=t[i];s[i]=us(n)?n.map(e):e(n)}return s}const Fo=()=>{},us=Array.isArray;function qe(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const _h=/#/g,Ty=/&/g,Ay=/\//g,My=/=/g,Py=/\?/g,vh=/\+/g,ky=/%5B/g,Vy=/%5D/g,yh=/%5E/g,Ry=/%60/g,bh=/%7B/g,Fy=/%7C/g,Ch=/%7D/g,Uy=/%20/g;function ou(e){return encodeURI(""+e).replace(Fy,"|").replace(ky,"[").replace(Vy,"]")}function Ly(e){return ou(e).replace(bh,"{").replace(Ch,"}").replace(yh,"^")}function iu(e){return ou(e).replace(vh,"%2B").replace(Uy,"+").replace(_h,"%23").replace(Ty,"%26").replace(Ry,"`").replace(bh,"{").replace(Ch,"}").replace(yh,"^")}function By(e){return iu(e).replace(My,"%3D")}function qy(e){return ou(e).replace(_h,"%23").replace(Py,"%3F")}function Hy(e){return e==null?"":qy(e).replace(Ay,"%2F")}function Bn(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&qe(`Error decoding "${e}". Using original value`)}return""+e}const $y=/\/$/,Wy=e=>e.replace($y,"");function au(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Gy(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:Bn(u)}}function jy(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function wh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Eh(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&Fr(t.matched[i],s.matched[n])&&Oh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Fr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Oh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!zy(e[s],t[s]))return!1;return!0}function zy(e,t){return us(e)?xh(e,t):us(t)?xh(t,e):e===t}function xh(e,t){return us(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Gy(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return qe(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Ur={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Uo;(function(e){e.pop="pop",e.push="push"})(Uo||(Uo={}));var Lo;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Lo||(Lo={}));function Ky(e){if(!e)if(mr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Wy(e)}const Qy=/^[^#]+#/;function Zy(e,t){return e.replace(Qy,"#")+t}function Jy(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Yi=()=>({left:window.scrollX,top:window.scrollY});function Yy(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){qe(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{qe(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&qe(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=Jy(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Sh(e,t){return(history.state?history.state.position-t:-1)+e}const lu=new Map;function Xy(e,t){lu.set(e,t)}function eb(e){const t=lu.get(e);return lu.delete(e),t}let tb=()=>location.protocol+"//"+location.host;function Dh(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,h=n.slice(c);return h[0]!=="/"&&(h="/"+h),wh(h,"")}return wh(s,e)+i+n}function sb(e,t,s,i){let n=[],a=[],u=null;const c=({state:v})=>{const O=Dh(e,location),k=s.value,V=t.value;let re=0;if(v){if(s.value=O,t.value=v,u&&u===k){u=null;return}re=V?v.position-V.position:0}else i(O);n.forEach(J=>{J(s.value,k,{delta:re,type:Uo.pop,direction:re?re>0?Lo.forward:Lo.back:Lo.unknown})})};function h(){u=s.value}function _(v){n.push(v);const O=()=>{const k=n.indexOf(v);k>-1&&n.splice(k,1)};return a.push(O),O}function p(){const{history:v}=window;v.state&&v.replaceState(Je({},v.state,{scroll:Yi()}),"")}function g(){for(const v of a)v();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:_,destroy:g}}function Ih(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?Yi():null}}function rb(e){const{history:t,location:s}=window,i={value:Dh(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,_,p){const g=e.indexOf("#"),v=g>-1?(s.host&&document.querySelector("base")?e:e.slice(g))+h:tb()+e+h;try{t[p?"replaceState":"pushState"](_,"",v),n.value=_}catch(O){({}).NODE_ENV!=="production"?qe("Error with push/replace State",O):console.error(O),s[p?"replace":"assign"](v)}}function u(h,_){const p=Je({},t.state,Ih(n.value.back,h,n.value.forward,!0),_,{position:n.value.position});a(h,p,!0),i.value=h}function c(h,_){const p=Je({},n.value,t.state,{forward:h,scroll:Yi()});({}).NODE_ENV!=="production"&&!t.state&&qe(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const g=Je({},Ih(i.value,h,null),{position:p.position+1},_);a(h,g,!1),i.value=h}return{location:i,state:n,push:c,replace:u}}function nb(e){e=Ky(e);const t=rb(e),s=sb(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=Je({location:"",base:e,go:i,createHref:Zy.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Xi(e){return typeof e=="string"||e&&typeof e=="object"}function Nh(e){return typeof e=="string"||typeof e=="symbol"}const uu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Th;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Th||(Th={}));const ob={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${ab(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function qn(e,t){return{}.NODE_ENV!=="production"?Je(new Error(ob[e](t)),{type:e,[uu]:!0},t):Je(new Error,{type:e,[uu]:!0},t)}function gr(e,t){return e instanceof Error&&uu in e&&(t==null||!!(e.type&t))}const ib=["params","query","hash"];function ab(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of ib)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Ah="[^/]+?",lb={sensitive:!1,strict:!1,start:!0,end:!0},ub=/[.+*?^${}()[\]/\\]/g;function cb(e,t){const s=Je({},lb,t),i=[];let n=s.start?"^":"";const a=[];for(const _ of e){const p=_.length?[]:[90];s.strict&&!_.length&&(n+="/");for(let g=0;g<_.length;g++){const v=_[g];let O=40+(s.sensitive?.25:0);if(v.type===0)g||(n+="/"),n+=v.value.replace(ub,"\\$&"),O+=40;else if(v.type===1){const{value:k,repeatable:V,optional:re,regexp:J}=v;a.push({name:k,repeatable:V,optional:re});const oe=J||Ah;if(oe!==Ah){O+=10;try{new RegExp(`(${oe})`)}catch(Ee){throw new Error(`Invalid custom RegExp for param "${k}" (${oe}): `+Ee.message)}}let Y=V?`((?:${oe})(?:/(?:${oe}))*)`:`(${oe})`;g||(Y=re&&_.length<2?`(?:/${Y})`:"/"+Y),re&&(Y+="?"),n+=Y,O+=20,re&&(O+=-8),V&&(O+=-20),oe===".*"&&(O+=-50)}p.push(O)}i.push(p)}if(s.strict&&s.end){const _=i.length-1;i[_][i[_].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(_){const p=_.match(u),g={};if(!p)return null;for(let v=1;v<p.length;v++){const O=p[v]||"",k=a[v-1];g[k.name]=O&&k.repeatable?O.split("/"):O}return g}function h(_){let p="",g=!1;for(const v of e){(!g||!p.endsWith("/"))&&(p+="/"),g=!1;for(const O of v)if(O.type===0)p+=O.value;else if(O.type===1){const{value:k,repeatable:V,optional:re}=O,J=k in _?_[k]:"";if(us(J)&&!V)throw new Error(`Provided param "${k}" is an array but it is not repeatable (* or + modifiers)`);const oe=us(J)?J.join("/"):J;if(!oe)if(re)v.length<2&&(p.endsWith("/")?p=p.slice(0,-1):g=!0);else throw new Error(`Missing required param "${k}"`);p+=oe}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function db(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Mh(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=db(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(Ph(i))return 1;if(Ph(n))return-1}return n.length-i.length}function Ph(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const fb={type:0,value:""},hb=/[a-zA-Z0-9_]/;function pb(e){if(!e)return[[]];if(e==="/")return[[fb]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(O){throw new Error(`ERR (${s})/"${_}": ${O}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,h,_="",p="";function g(){_&&(s===0?a.push({type:0,value:_}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${_}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:_,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),_="")}function v(){_+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(_&&g(),u()):h===":"?(g(),s=1):v();break;case 4:v(),s=i;break;case 1:h==="("?s=2:hb.test(h)?v():(g(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:g(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${_}"`),g(),u(),n}function mb(e,t,s){const i=cb(pb(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&qe(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=Je(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function gb(e,t){const s=[],i=new Map;t=Fh({strict:!1,end:!0,sensitive:!1},t);function n(g){return i.get(g)}function a(g,v,O){const k=!O,V=Vh(g);({}).NODE_ENV!=="production"&&bb(V,v),V.aliasOf=O&&O.record;const re=Fh(t,g),J=[V];if("alias"in g){const Ee=typeof g.alias=="string"?[g.alias]:g.alias;for(const X of Ee)J.push(Vh(Je({},V,{components:O?O.record.components:V.components,path:X,aliasOf:O?O.record:V})))}let oe,Y;for(const Ee of J){const{path:X}=Ee;if(v&&X[0]!=="/"){const fe=v.record.path,_e=fe[fe.length-1]==="/"?"":"/";Ee.path=v.record.path+(X&&_e+X)}if({}.NODE_ENV!=="production"&&Ee.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(oe=mb(Ee,v,re),{}.NODE_ENV!=="production"&&v&&X[0]==="/"&&wb(oe,v),O?(O.alias.push(oe),{}.NODE_ENV!=="production"&&yb(O,oe)):(Y=Y||oe,Y!==oe&&Y.alias.push(oe),k&&g.name&&!Rh(oe)&&({}.NODE_ENV!=="production"&&Cb(g,v),u(g.name))),Uh(oe)&&h(oe),V.children){const fe=V.children;for(let _e=0;_e<fe.length;_e++)a(fe[_e],oe,O&&O.children[_e])}O=O||oe}return Y?()=>{u(Y)}:Fo}function u(g){if(Nh(g)){const v=i.get(g);v&&(i.delete(g),s.splice(s.indexOf(v),1),v.children.forEach(u),v.alias.forEach(u))}else{const v=s.indexOf(g);v>-1&&(s.splice(v,1),g.record.name&&i.delete(g.record.name),g.children.forEach(u),g.alias.forEach(u))}}function c(){return s}function h(g){const v=Eb(g,s);s.splice(v,0,g),g.record.name&&!Rh(g)&&i.set(g.record.name,g)}function _(g,v){let O,k={},V,re;if("name"in g&&g.name){if(O=i.get(g.name),!O)throw qn(1,{location:g});if({}.NODE_ENV!=="production"){const Y=Object.keys(g.params||{}).filter(Ee=>!O.keys.find(X=>X.name===Ee));Y.length&&qe(`Discarded invalid param(s) "${Y.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}re=O.record.name,k=Je(kh(v.params,O.keys.filter(Y=>!Y.optional).concat(O.parent?O.parent.keys.filter(Y=>Y.optional):[]).map(Y=>Y.name)),g.params&&kh(g.params,O.keys.map(Y=>Y.name))),V=O.stringify(k)}else if(g.path!=null)V=g.path,{}.NODE_ENV!=="production"&&!V.startsWith("/")&&qe(`The Matcher cannot resolve relative paths but received "${V}". Unless you directly called \`matcher.resolve("${V}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),O=s.find(Y=>Y.re.test(V)),O&&(k=O.parse(V),re=O.record.name);else{if(O=v.name?i.get(v.name):s.find(Y=>Y.re.test(v.path)),!O)throw qn(1,{location:g,currentLocation:v});re=O.record.name,k=Je({},v.params,g.params),V=O.stringify(k)}const J=[];let oe=O;for(;oe;)J.unshift(oe.record),oe=oe.parent;return{name:re,path:V,params:k,matched:J,meta:vb(J)}}e.forEach(g=>a(g));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:_,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:n}}function kh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Vh(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_b(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _b(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function Rh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function vb(e){return e.reduce((t,s)=>Je(t,s.meta),{})}function Fh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function cu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function yb(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(cu.bind(null,s)))return qe(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(cu.bind(null,s)))return qe(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function bb(e,t){t&&t.record.name&&!e.name&&!e.path&&qe(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function Cb(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function wb(e,t){for(const s of t.keys)if(!e.keys.find(cu.bind(null,s)))return qe(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function Eb(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Mh(e,t[a])<0?i=a:s=a+1}const n=Ob(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&qe(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function Ob(e){let t=e;for(;t=t.parent;)if(Uh(t)&&Mh(e,t)===0)return t}function Uh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function xb(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(vh," "),u=a.indexOf("="),c=Bn(u<0?a:a.slice(0,u)),h=u<0?null:Bn(a.slice(u+1));if(c in t){let _=t[c];us(_)||(_=t[c]=[_]),_.push(h)}else t[c]=h}return t}function Lh(e){let t="";for(let s in e){const i=e[s];if(s=By(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(us(i)?i.map(a=>a&&iu(a)):[i&&iu(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function Sb(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=us(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const Db=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Bh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),ea=Symbol({}.NODE_ENV!=="production"?"router":""),qh=Symbol({}.NODE_ENV!=="production"?"route location":""),du=Symbol({}.NODE_ENV!=="production"?"router view location":"");function Bo(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Lr(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,h)=>{const _=v=>{v===!1?h(qn(4,{from:s,to:t})):v instanceof Error?h(v):Xi(v)?h(qn(2,{from:t,to:v})):(u&&i.enterCallbacks[n]===u&&typeof v=="function"&&u.push(v),c())},p=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?Ib(_,t,s):_));let g=Promise.resolve(p);if(e.length<3&&(g=g.then(_)),{}.NODE_ENV!=="production"&&e.length>2){const v=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)g=g.then(O=>_._called?O:(qe(v),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!_._called){qe(v),h(new Error("Invalid navigation guard"));return}}g.catch(v=>h(v))})}function Ib(e,t,s){let i=0;return function(){i++===1&&qe(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function fu(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&qe(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw qe(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){qe(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const _=h;h=()=>_}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,qe(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(gh(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Lr(p,s,i,u,c,n))}else{let _=h();({}).NODE_ENV!=="production"&&!("catch"in _)&&(qe(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),_=Promise.resolve(_)),a.push(()=>_.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const g=Ny(p)?p.default:p;u.mods[c]=p,u.components[c]=g;const O=(g.__vccOpts||g)[t];return O&&Lr(O,s,i,u,c,n)()}))}}}return a}function Hh(e){const t=Ks(ea),s=Ks(qh);let i=!1,n=null;const a=Vs(()=>{const p=Nr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==n)&&(Xi(p)||(i?qe(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,n,`
- props:`,e):qe(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),n=p,i=!0),t.resolve(p)}),u=Vs(()=>{const{matched:p}=a.value,{length:g}=p,v=p[g-1],O=s.matched;if(!v||!O.length)return-1;const k=O.findIndex(Fr.bind(null,v));if(k>-1)return k;const V=$h(p[g-2]);return g>1&&$h(v)===V&&O[O.length-1].path!==V?O.findIndex(Fr.bind(null,p[g-2])):k}),c=Vs(()=>u.value>-1&&Mb(s.params,a.value.params)),h=Vs(()=>u.value>-1&&u.value===s.matched.length-1&&Oh(s.params,a.value.params));function _(p={}){if(Ab(p)){const g=t[Nr(e.replace)?"replace":"push"](Nr(e.to)).catch(Fo);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>g),g}return Promise.resolve()}if({}.NODE_ENV!=="production"&&mr){const p=Bi();if(p){const g={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(g),zv(()=>{g.route=a.value,g.isActive=c.value,g.isExactActive=h.value,g.error=Xi(Nr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Vs(()=>a.value.href),isActive:c,isExactActive:h,navigate:_}}function Nb(e){return e.length===1?e[0]:e}const Tb=Bd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Hh,setup(e,{slots:t}){const s=_i(Hh(e)),{options:i}=Ks(ea),n=Vs(()=>({[Wh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[Wh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&Nb(t.default(s));return e.custom?a:zl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function Ab(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Mb(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!us(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function $h(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Wh=(e,t,s)=>e??t??s,Pb=Bd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&Vb();const i=Ks(du),n=Vs(()=>e.route||i.value),a=Ks(Bh,0),u=Vs(()=>{let _=Nr(a);const{matched:p}=n.value;let g;for(;(g=p[_])&&!g.components;)_++;return _}),c=Vs(()=>n.value.matched[u.value]);Vi(Bh,Vs(()=>u.value+1)),Vi(Db,c),Vi(du,n);const h=cd();return Rn(()=>[h.value,c.value,e.name],([_,p,g],[v,O,k])=>{p&&(p.instances[g]=_,O&&O!==p&&_&&_===v&&(p.leaveGuards.size||(p.leaveGuards=O.leaveGuards),p.updateGuards.size||(p.updateGuards=O.updateGuards))),_&&p&&(!O||!Fr(p,O)||!v)&&(p.enterCallbacks[g]||[]).forEach(V=>V(_))},{flush:"post"}),()=>{const _=n.value,p=e.name,g=c.value,v=g&&g.components[p];if(!v)return jh(s.default,{Component:v,route:_});const O=g.props[p],k=O?O===!0?_.params:typeof O=="function"?O(_):O:null,re=zl(v,Je({},k,t,{onVnodeUnmounted:J=>{J.component.isUnmounted&&(g.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&mr&&re.ref){const J={depth:u.value,name:g.name,path:g.path,meta:g.meta};(us(re.ref)?re.ref.map(Y=>Y.i):[re.ref.i]).forEach(Y=>{Y.__vrv_devtools=J})}return jh(s.default,{Component:re,route:_})||re}}});function jh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const kb=Pb;function Vb(){const e=Bi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";qe(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function qo(e,t){const s=Je({},e,{matched:e.matched.map(i=>zb(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function ta(e){return{_custom:{display:e}}}let Rb=0;function Fb(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=Rb++;eu({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((p,g)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:qo(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:p,componentInstance:g})=>{if(g.__vrv_devtools){const v=g.__vrv_devtools;p.tags.push({label:(v.name?`${v.name.toString()}: `:"")+v.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:zh})}us(g.__vrl_devtools)&&(g.__devtoolsApi=n,g.__vrl_devtools.forEach(v=>{let O=v.route.path,k=Qh,V="",re=0;v.error?(O=v.error,k=Hb,re=$b):v.isExactActive?(k=Kh,V="This is exactly active"):v.isActive&&(k=Gh,V="This link is active"),p.tags.push({label:O,textColor:re,tooltip:V,backgroundColor:k})}))}),Rn(t.currentRoute,()=>{h(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,g)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:g.fullPath,logType:"error",time:n.now(),data:{error:p},groupId:g.meta.__navigationId}})});let u=0;t.beforeEach((p,g)=>{const v={guard:ta("beforeEach"),from:qo(g,"Current Location during this navigation"),to:qo(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:p.fullPath,data:v,groupId:p.meta.__navigationId}})}),t.afterEach((p,g,v)=>{const O={guard:ta("afterEach")};v?(O.failure={_custom:{type:Error,readOnly:!0,display:v?v.message:"",tooltip:"Navigation Failure",value:v}},O.status=ta("❌")):O.status=ta("✅"),O.from=qo(g,"Current Location during this navigation"),O.to=qo(p,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:n.now(),data:O,logType:v?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!_)return;const p=_;let g=s.getRoutes().filter(v=>!v.parent||!v.parent.record.components);g.forEach(Yh),p.filter&&(g=g.filter(v=>hu(v,p.filter.toLowerCase()))),g.forEach(v=>Jh(v,t.currentRoute.value)),p.rootNodes=g.map(Zh)}let _;n.on.getInspectorTree(p=>{_=p,p.app===e&&p.inspectorId===c&&h()}),n.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const v=s.getRoutes().find(O=>O.record.__vd_id===p.nodeId);v&&(p.state={options:Lb(v)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function Ub(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function Lb(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${Ub(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const zh=15485081,Gh=2450411,Kh=8702998,Bb=2282478,Qh=16486972,qb=6710886,Hb=16704226,$b=12131356;function Zh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:Bb}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Qh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:zh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Kh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Gh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:qb});let i=s.__vd_id;return i==null&&(i=String(Wb++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Zh)}}let Wb=0;const jb=/^\/(.*)\/([a-z]*)$/;function Jh(e,t){const s=t.matched.length&&Fr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Fr(i,e.record))),e.children.forEach(i=>Jh(i,t))}function Yh(e){e.__vd_match=!1,e.children.forEach(Yh)}function hu(e,t){const s=String(e.re).match(jb);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>hu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=Bn(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>hu(u,t))}function zb(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Gb(e){const t=gb(e.routes,e),s=e.parseQuery||xb,i=e.stringifyQuery||Lh,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=Bo(),u=Bo(),c=Bo(),h=O_(Ur);let _=Ur;mr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=nu.bind(null,R=>""+R),g=nu.bind(null,Hy),v=nu.bind(null,Bn);function O(R,ie){let ne,pe;return Nh(R)?(ne=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!ne&&qe(`Parent route "${String(R)}" not found when adding child route`,ie),pe=ie):pe=R,t.addRoute(pe,ne)}function k(R){const ie=t.getRecordMatcher(R);ie?t.removeRoute(ie):{}.NODE_ENV!=="production"&&qe(`Cannot remove non-existent route "${String(R)}"`)}function V(){return t.getRoutes().map(R=>R.record)}function re(R){return!!t.getRecordMatcher(R)}function J(R,ie){if(ie=Je({},ie||h.value),typeof R=="string"){const C=au(s,R,ie.path),E=t.resolve({path:C.path},ie),P=n.createHref(C.fullPath);return{}.NODE_ENV!=="production"&&(P.startsWith("//")?qe(`Location "${R}" resolved to "${P}". A resolved location cannot start with multiple slashes.`):E.matched.length||qe(`No match found for location with path "${R}"`)),Je(C,E,{params:v(E.params),hash:Bn(C.hash),redirectedFrom:void 0,href:P})}if({}.NODE_ENV!=="production"&&!Xi(R))return qe(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),J({});let ne;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&qe(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),ne=Je({},R,{path:au(s,R.path,ie.path).path});else{const C=Je({},R.params);for(const E in C)C[E]==null&&delete C[E];ne=Je({},R,{params:g(C)}),ie.params=g(ie.params)}const pe=t.resolve(ne,ie),ke=R.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&qe(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),pe.params=p(v(pe.params));const rt=jy(i,Je({},R,{hash:Ly(ke),path:pe.path})),Ve=n.createHref(rt);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?qe(`Location "${R}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):pe.matched.length||qe(`No match found for location with path "${R.path!=null?R.path:R}"`)),Je({fullPath:rt,hash:ke,query:i===Lh?Sb(R.query):R.query||{}},pe,{redirectedFrom:void 0,href:Ve})}function oe(R){return typeof R=="string"?au(s,R,h.value.path):Je({},R)}function Y(R,ie){if(_!==R)return qn(8,{from:ie,to:R})}function Ee(R){return _e(R)}function X(R){return Ee(Je(oe(R),{replace:!0}))}function fe(R){const ie=R.matched[R.matched.length-1];if(ie&&ie.redirect){const{redirect:ne}=ie;let pe=typeof ne=="function"?ne(R):ne;if(typeof pe=="string"&&(pe=pe.includes("?")||pe.includes("#")?pe=oe(pe):{path:pe},pe.params={}),{}.NODE_ENV!=="production"&&pe.path==null&&!("name"in pe))throw qe(`Invalid redirect found:
${JSON.stringify(pe,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Je({query:R.query,hash:R.hash,params:pe.path!=null?{}:R.params},pe)}}function _e(R,ie){const ne=_=J(R),pe=h.value,ke=R.state,rt=R.force,Ve=R.replace===!0,C=fe(ne);if(C)return _e(Je(oe(C),{state:typeof C=="object"?Je({},ke,C.state):ke,force:rt,replace:Ve}),ie||ne);const E=ne;E.redirectedFrom=ie;let P;return!rt&&Eh(i,pe,ne)&&(P=qn(16,{to:E,from:pe}),ct(pe,pe,!0,!1)),(P?Promise.resolve(P):A(E,pe)).catch(U=>gr(U)?gr(U,2)?U:wt(U):ye(U,E,pe)).then(U=>{if(U){if(gr(U,2))return{}.NODE_ENV!=="production"&&Eh(i,J(U.to),E)&&ie&&(ie._count=ie._count?ie._count+1:1)>30?(qe(`Detected a possibly infinite redirection in a navigation guard when going from "${pe.fullPath}" to "${E.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):_e(Je({replace:Ve},oe(U.to),{state:typeof U.to=="object"?Je({},ke,U.to.state):ke,force:rt}),ie||E)}else U=ue(E,pe,!0,Ve,ke);return we(E,pe,U),U})}function Ne(R,ie){const ne=Y(R,ie);return ne?Promise.reject(ne):Promise.resolve()}function ae(R){const ie=tr.values().next().value;return ie&&typeof ie.runWithContext=="function"?ie.runWithContext(R):R()}function A(R,ie){let ne;const[pe,ke,rt]=Kb(R,ie);ne=fu(pe.reverse(),"beforeRouteLeave",R,ie);for(const C of pe)C.leaveGuards.forEach(E=>{ne.push(Lr(E,R,ie))});const Ve=Ne.bind(null,R,ie);return ne.push(Ve),cs(ne).then(()=>{ne=[];for(const C of a.list())ne.push(Lr(C,R,ie));return ne.push(Ve),cs(ne)}).then(()=>{ne=fu(ke,"beforeRouteUpdate",R,ie);for(const C of ke)C.updateGuards.forEach(E=>{ne.push(Lr(E,R,ie))});return ne.push(Ve),cs(ne)}).then(()=>{ne=[];for(const C of rt)if(C.beforeEnter)if(us(C.beforeEnter))for(const E of C.beforeEnter)ne.push(Lr(E,R,ie));else ne.push(Lr(C.beforeEnter,R,ie));return ne.push(Ve),cs(ne)}).then(()=>(R.matched.forEach(C=>C.enterCallbacks={}),ne=fu(rt,"beforeRouteEnter",R,ie,ae),ne.push(Ve),cs(ne))).then(()=>{ne=[];for(const C of u.list())ne.push(Lr(C,R,ie));return ne.push(Ve),cs(ne)}).catch(C=>gr(C,8)?C:Promise.reject(C))}function we(R,ie,ne){c.list().forEach(pe=>ae(()=>pe(R,ie,ne)))}function ue(R,ie,ne,pe,ke){const rt=Y(R,ie);if(rt)return rt;const Ve=ie===Ur,C=mr?history.state:{};ne&&(pe||Ve?n.replace(R.fullPath,Je({scroll:Ve&&C&&C.scroll},ke)):n.push(R.fullPath,ke)),h.value=R,ct(R,ie,ne,Ve),wt()}let ze;function _t(){ze||(ze=n.listen((R,ie,ne)=>{if(!Fs.listening)return;const pe=J(R),ke=fe(pe);if(ke){_e(Je(ke,{replace:!0,force:!0}),pe).catch(Fo);return}_=pe;const rt=h.value;mr&&Xy(Sh(rt.fullPath,ne.delta),Yi()),A(pe,rt).catch(Ve=>gr(Ve,12)?Ve:gr(Ve,2)?(_e(Je(oe(Ve.to),{force:!0}),pe).then(C=>{gr(C,20)&&!ne.delta&&ne.type===Uo.pop&&n.go(-1,!1)}).catch(Fo),Promise.reject()):(ne.delta&&n.go(-ne.delta,!1),ye(Ve,pe,rt))).then(Ve=>{Ve=Ve||ue(pe,rt,!1),Ve&&(ne.delta&&!gr(Ve,8)?n.go(-ne.delta,!1):ne.type===Uo.pop&&gr(Ve,20)&&n.go(-1,!1)),we(pe,rt,Ve)}).catch(Fo)}))}let pt=Bo(),ut=Bo(),K;function ye(R,ie,ne){wt(R);const pe=ut.list();return pe.length?pe.forEach(ke=>ke(R,ie,ne)):({}.NODE_ENV!=="production"&&qe("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function be(){return K&&h.value!==Ur?Promise.resolve():new Promise((R,ie)=>{pt.add([R,ie])})}function wt(R){return K||(K=!R,_t(),pt.list().forEach(([ie,ne])=>R?ne(R):ie()),pt.reset()),R}function ct(R,ie,ne,pe){const{scrollBehavior:ke}=e;if(!mr||!ke)return Promise.resolve();const rt=!ne&&eb(Sh(R.fullPath,0))||(pe||!ne)&&history.state&&history.state.scroll||null;return _l().then(()=>ke(R,ie,rt)).then(Ve=>Ve&&Yy(Ve)).catch(Ve=>ye(Ve,R,ie))}const vs=R=>n.go(R);let ss;const tr=new Set,Fs={currentRoute:h,listening:!0,addRoute:O,removeRoute:k,clearRoutes:t.clearRoutes,hasRoute:re,getRoutes:V,resolve:J,options:e,push:Ee,replace:X,go:vs,back:()=>vs(-1),forward:()=>vs(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ut.add,isReady:be,install(R){const ie=this;R.component("RouterLink",Tb),R.component("RouterView",kb),R.config.globalProperties.$router=ie,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Nr(h)}),mr&&!ss&&h.value===Ur&&(ss=!0,Ee(n.location).catch(ke=>{({}).NODE_ENV!=="production"&&qe("Unexpected error when starting the router:",ke)}));const ne={};for(const ke in Ur)Object.defineProperty(ne,ke,{get:()=>h.value[ke],enumerable:!0});R.provide(ea,ie),R.provide(qh,ld(ne)),R.provide(du,h);const pe=R.unmount;tr.add(R),R.unmount=function(){tr.delete(R),tr.size<1&&(_=Ur,ze&&ze(),ze=null,h.value=Ur,ss=!1,K=!1),pe()},{}.NODE_ENV!=="production"&&mr&&Fb(R,ie,t)}};function cs(R){return R.reduce((ie,ne)=>ie.then(()=>ae(ne)),Promise.resolve())}return Fs}function Kb(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(_=>Fr(_,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(_=>Fr(_,h))||n.push(h))}return[s,i,n]}function Qb(){return Ks(ea)}var Ho=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},sa={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */sa.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",_=500,p="__lodash_placeholder__",g=1,v=2,O=4,k=1,V=2,re=1,J=2,oe=4,Y=8,Ee=16,X=32,fe=64,_e=128,Ne=256,ae=512,A=30,we="...",ue=800,ze=16,_t=1,pt=2,ut=3,K=1/0,ye=9007199254740991,be=17976931348623157e292,wt=0/0,ct=**********,vs=ct-1,ss=ct>>>1,tr=[["ary",_e],["bind",re],["bindKey",J],["curry",Y],["curryRight",Ee],["flip",ae],["partial",X],["partialRight",fe],["rearg",Ne]],Fs="[object Arguments]",cs="[object Array]",R="[object AsyncFunction]",ie="[object Boolean]",ne="[object Date]",pe="[object DOMException]",ke="[object Error]",rt="[object Function]",Ve="[object GeneratorFunction]",C="[object Map]",E="[object Number]",P="[object Null]",U="[object Object]",H="[object Promise]",W="[object Proxy]",te="[object RegExp]",Q="[object Set]",ee="[object String]",z="[object Symbol]",ve="[object Undefined]",se="[object WeakMap]",me="[object WeakSet]",Oe="[object ArrayBuffer]",Fe="[object DataView]",Ye="[object Float32Array]",Qe="[object Float64Array]",Rt="[object Int8Array]",St="[object Int16Array]",Zt="[object Int32Array]",Lt="[object Uint8Array]",_r="[object Uint8ClampedArray]",Wn="[object Uint16Array]",Tt="[object Uint32Array]",ys=/\b__p \+= '';/g,la=/\b(__p \+=) '' \+/g,lN=/(__e\(.*?\)|\b__t\)) \+\n'';/g,ip=/&(?:amp|lt|gt|quot|#39);/g,ap=/[&<>"']/g,uN=RegExp(ip.source),cN=RegExp(ap.source),dN=/<%-([\s\S]+?)%>/g,fN=/<%([\s\S]+?)%>/g,lp=/<%=([\s\S]+?)%>/g,hN=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,pN=/^\w*$/,mN=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Eu=/[\\^$.*+?()[\]{}|]/g,gN=RegExp(Eu.source),Ou=/^\s+/,_N=/\s/,vN=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,yN=/\{\n\/\* \[wrapped with (.+)\] \*/,bN=/,? & /,CN=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,wN=/[()=,{}\[\]\/\s]/,EN=/\\(\\)?/g,ON=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,up=/\w*$/,xN=/^[-+]0x[0-9a-f]+$/i,SN=/^0b[01]+$/i,DN=/^\[object .+?Constructor\]$/,IN=/^0o[0-7]+$/i,NN=/^(?:0|[1-9]\d*)$/,TN=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ua=/($^)/,AN=/['\n\r\u2028\u2029\\]/g,ca="\\ud800-\\udfff",MN="\\u0300-\\u036f",PN="\\ufe20-\\ufe2f",kN="\\u20d0-\\u20ff",cp=MN+PN+kN,dp="\\u2700-\\u27bf",fp="a-z\\xdf-\\xf6\\xf8-\\xff",VN="\\xac\\xb1\\xd7\\xf7",RN="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",FN="\\u2000-\\u206f",UN=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",hp="A-Z\\xc0-\\xd6\\xd8-\\xde",pp="\\ufe0e\\ufe0f",mp=VN+RN+FN+UN,xu="['’]",LN="["+ca+"]",gp="["+mp+"]",da="["+cp+"]",_p="\\d+",BN="["+dp+"]",vp="["+fp+"]",yp="[^"+ca+mp+_p+dp+fp+hp+"]",Su="\\ud83c[\\udffb-\\udfff]",qN="(?:"+da+"|"+Su+")",bp="[^"+ca+"]",Du="(?:\\ud83c[\\udde6-\\uddff]){2}",Iu="[\\ud800-\\udbff][\\udc00-\\udfff]",jn="["+hp+"]",Cp="\\u200d",wp="(?:"+vp+"|"+yp+")",HN="(?:"+jn+"|"+yp+")",Ep="(?:"+xu+"(?:d|ll|m|re|s|t|ve))?",Op="(?:"+xu+"(?:D|LL|M|RE|S|T|VE))?",xp=qN+"?",Sp="["+pp+"]?",$N="(?:"+Cp+"(?:"+[bp,Du,Iu].join("|")+")"+Sp+xp+")*",WN="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",jN="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Dp=Sp+xp+$N,zN="(?:"+[BN,Du,Iu].join("|")+")"+Dp,GN="(?:"+[bp+da+"?",da,Du,Iu,LN].join("|")+")",KN=RegExp(xu,"g"),QN=RegExp(da,"g"),Nu=RegExp(Su+"(?="+Su+")|"+GN+Dp,"g"),ZN=RegExp([jn+"?"+vp+"+"+Ep+"(?="+[gp,jn,"$"].join("|")+")",HN+"+"+Op+"(?="+[gp,jn+wp,"$"].join("|")+")",jn+"?"+wp+"+"+Ep,jn+"+"+Op,jN,WN,_p,zN].join("|"),"g"),JN=RegExp("["+Cp+ca+cp+pp+"]"),YN=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,XN=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],eT=-1,dt={};dt[Ye]=dt[Qe]=dt[Rt]=dt[St]=dt[Zt]=dt[Lt]=dt[_r]=dt[Wn]=dt[Tt]=!0,dt[Fs]=dt[cs]=dt[Oe]=dt[ie]=dt[Fe]=dt[ne]=dt[ke]=dt[rt]=dt[C]=dt[E]=dt[U]=dt[te]=dt[Q]=dt[ee]=dt[se]=!1;var it={};it[Fs]=it[cs]=it[Oe]=it[Fe]=it[ie]=it[ne]=it[Ye]=it[Qe]=it[Rt]=it[St]=it[Zt]=it[C]=it[E]=it[U]=it[te]=it[Q]=it[ee]=it[z]=it[Lt]=it[_r]=it[Wn]=it[Tt]=!0,it[ke]=it[rt]=it[se]=!1;var tT={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},sT={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},rT={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},nT={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},oT=parseFloat,iT=parseInt,Ip=typeof Ho=="object"&&Ho&&Ho.Object===Object&&Ho,aT=typeof self=="object"&&self&&self.Object===Object&&self,Bt=Ip||aT||Function("return this")(),Tu=t&&!t.nodeType&&t,vn=Tu&&!0&&e&&!e.nodeType&&e,Np=vn&&vn.exports===Tu,Au=Np&&Ip.process,bs=function(){try{var I=vn&&vn.require&&vn.require("util").types;return I||Au&&Au.binding&&Au.binding("util")}catch{}}(),Tp=bs&&bs.isArrayBuffer,Ap=bs&&bs.isDate,Mp=bs&&bs.isMap,Pp=bs&&bs.isRegExp,kp=bs&&bs.isSet,Vp=bs&&bs.isTypedArray;function ds(I,F,M){switch(M.length){case 0:return I.call(F);case 1:return I.call(F,M[0]);case 2:return I.call(F,M[0],M[1]);case 3:return I.call(F,M[0],M[1],M[2])}return I.apply(F,M)}function lT(I,F,M,de){for(var Me=-1,Xe=I==null?0:I.length;++Me<Xe;){var At=I[Me];F(de,At,M(At),I)}return de}function Cs(I,F){for(var M=-1,de=I==null?0:I.length;++M<de&&F(I[M],M,I)!==!1;);return I}function uT(I,F){for(var M=I==null?0:I.length;M--&&F(I[M],M,I)!==!1;);return I}function Rp(I,F){for(var M=-1,de=I==null?0:I.length;++M<de;)if(!F(I[M],M,I))return!1;return!0}function qr(I,F){for(var M=-1,de=I==null?0:I.length,Me=0,Xe=[];++M<de;){var At=I[M];F(At,M,I)&&(Xe[Me++]=At)}return Xe}function fa(I,F){var M=I==null?0:I.length;return!!M&&zn(I,F,0)>-1}function Mu(I,F,M){for(var de=-1,Me=I==null?0:I.length;++de<Me;)if(M(F,I[de]))return!0;return!1}function mt(I,F){for(var M=-1,de=I==null?0:I.length,Me=Array(de);++M<de;)Me[M]=F(I[M],M,I);return Me}function Hr(I,F){for(var M=-1,de=F.length,Me=I.length;++M<de;)I[Me+M]=F[M];return I}function Pu(I,F,M,de){var Me=-1,Xe=I==null?0:I.length;for(de&&Xe&&(M=I[++Me]);++Me<Xe;)M=F(M,I[Me],Me,I);return M}function cT(I,F,M,de){var Me=I==null?0:I.length;for(de&&Me&&(M=I[--Me]);Me--;)M=F(M,I[Me],Me,I);return M}function ku(I,F){for(var M=-1,de=I==null?0:I.length;++M<de;)if(F(I[M],M,I))return!0;return!1}var dT=Vu("length");function fT(I){return I.split("")}function hT(I){return I.match(CN)||[]}function Fp(I,F,M){var de;return M(I,function(Me,Xe,At){if(F(Me,Xe,At))return de=Xe,!1}),de}function ha(I,F,M,de){for(var Me=I.length,Xe=M+(de?1:-1);de?Xe--:++Xe<Me;)if(F(I[Xe],Xe,I))return Xe;return-1}function zn(I,F,M){return F===F?xT(I,F,M):ha(I,Up,M)}function pT(I,F,M,de){for(var Me=M-1,Xe=I.length;++Me<Xe;)if(de(I[Me],F))return Me;return-1}function Up(I){return I!==I}function Lp(I,F){var M=I==null?0:I.length;return M?Fu(I,F)/M:wt}function Vu(I){return function(F){return F==null?s:F[I]}}function Ru(I){return function(F){return I==null?s:I[F]}}function Bp(I,F,M,de,Me){return Me(I,function(Xe,At,ot){M=de?(de=!1,Xe):F(M,Xe,At,ot)}),M}function mT(I,F){var M=I.length;for(I.sort(F);M--;)I[M]=I[M].value;return I}function Fu(I,F){for(var M,de=-1,Me=I.length;++de<Me;){var Xe=F(I[de]);Xe!==s&&(M=M===s?Xe:M+Xe)}return M}function Uu(I,F){for(var M=-1,de=Array(I);++M<I;)de[M]=F(M);return de}function gT(I,F){return mt(F,function(M){return[M,I[M]]})}function qp(I){return I&&I.slice(0,jp(I)+1).replace(Ou,"")}function fs(I){return function(F){return I(F)}}function Lu(I,F){return mt(F,function(M){return I[M]})}function Qo(I,F){return I.has(F)}function Hp(I,F){for(var M=-1,de=I.length;++M<de&&zn(F,I[M],0)>-1;);return M}function $p(I,F){for(var M=I.length;M--&&zn(F,I[M],0)>-1;);return M}function _T(I,F){for(var M=I.length,de=0;M--;)I[M]===F&&++de;return de}var vT=Ru(tT),yT=Ru(sT);function bT(I){return"\\"+nT[I]}function CT(I,F){return I==null?s:I[F]}function Gn(I){return JN.test(I)}function wT(I){return YN.test(I)}function ET(I){for(var F,M=[];!(F=I.next()).done;)M.push(F.value);return M}function Bu(I){var F=-1,M=Array(I.size);return I.forEach(function(de,Me){M[++F]=[Me,de]}),M}function Wp(I,F){return function(M){return I(F(M))}}function $r(I,F){for(var M=-1,de=I.length,Me=0,Xe=[];++M<de;){var At=I[M];(At===F||At===p)&&(I[M]=p,Xe[Me++]=M)}return Xe}function pa(I){var F=-1,M=Array(I.size);return I.forEach(function(de){M[++F]=de}),M}function OT(I){var F=-1,M=Array(I.size);return I.forEach(function(de){M[++F]=[de,de]}),M}function xT(I,F,M){for(var de=M-1,Me=I.length;++de<Me;)if(I[de]===F)return de;return-1}function ST(I,F,M){for(var de=M+1;de--;)if(I[de]===F)return de;return de}function Kn(I){return Gn(I)?IT(I):dT(I)}function Us(I){return Gn(I)?NT(I):fT(I)}function jp(I){for(var F=I.length;F--&&_N.test(I.charAt(F)););return F}var DT=Ru(rT);function IT(I){for(var F=Nu.lastIndex=0;Nu.test(I);)++F;return F}function NT(I){return I.match(Nu)||[]}function TT(I){return I.match(ZN)||[]}var AT=function I(F){F=F==null?Bt:Qn.defaults(Bt.Object(),F,Qn.pick(Bt,XN));var M=F.Array,de=F.Date,Me=F.Error,Xe=F.Function,At=F.Math,ot=F.Object,qu=F.RegExp,MT=F.String,ws=F.TypeError,ma=M.prototype,PT=Xe.prototype,Zn=ot.prototype,ga=F["__core-js_shared__"],_a=PT.toString,nt=Zn.hasOwnProperty,kT=0,zp=function(){var r=/[^.]+$/.exec(ga&&ga.keys&&ga.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),va=Zn.toString,VT=_a.call(ot),RT=Bt._,FT=qu("^"+_a.call(nt).replace(Eu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ya=Np?F.Buffer:s,Wr=F.Symbol,ba=F.Uint8Array,Gp=ya?ya.allocUnsafe:s,Ca=Wp(ot.getPrototypeOf,ot),Kp=ot.create,Qp=Zn.propertyIsEnumerable,wa=ma.splice,Zp=Wr?Wr.isConcatSpreadable:s,Zo=Wr?Wr.iterator:s,yn=Wr?Wr.toStringTag:s,Ea=function(){try{var r=On(ot,"defineProperty");return r({},"",{}),r}catch{}}(),UT=F.clearTimeout!==Bt.clearTimeout&&F.clearTimeout,LT=de&&de.now!==Bt.Date.now&&de.now,BT=F.setTimeout!==Bt.setTimeout&&F.setTimeout,Oa=At.ceil,xa=At.floor,Hu=ot.getOwnPropertySymbols,qT=ya?ya.isBuffer:s,Jp=F.isFinite,HT=ma.join,$T=Wp(ot.keys,ot),Mt=At.max,$t=At.min,WT=de.now,jT=F.parseInt,Yp=At.random,zT=ma.reverse,$u=On(F,"DataView"),Jo=On(F,"Map"),Wu=On(F,"Promise"),Jn=On(F,"Set"),Yo=On(F,"WeakMap"),Xo=On(ot,"create"),Sa=Yo&&new Yo,Yn={},GT=xn($u),KT=xn(Jo),QT=xn(Wu),ZT=xn(Jn),JT=xn(Yo),Da=Wr?Wr.prototype:s,ei=Da?Da.valueOf:s,Xp=Da?Da.toString:s;function y(r){if(yt(r)&&!Re(r)&&!(r instanceof je)){if(r instanceof Es)return r;if(nt.call(r,"__wrapped__"))return eg(r)}return new Es(r)}var Xn=function(){function r(){}return function(o){if(!vt(o))return{};if(Kp)return Kp(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function Ia(){}function Es(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}y.templateSettings={escape:dN,evaluate:fN,interpolate:lp,variable:"",imports:{_:y}},y.prototype=Ia.prototype,y.prototype.constructor=y,Es.prototype=Xn(Ia.prototype),Es.prototype.constructor=Es;function je(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ct,this.__views__=[]}function YT(){var r=new je(this.__wrapped__);return r.__actions__=rs(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=rs(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=rs(this.__views__),r}function XT(){if(this.__filtered__){var r=new je(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function eA(){var r=this.__wrapped__.value(),o=this.__dir__,l=Re(r),d=o<0,m=l?r.length:0,b=f3(0,m,this.__views__),w=b.start,S=b.end,N=S-w,L=d?S:w-1,B=this.__iteratees__,j=B.length,le=0,ge=$t(N,this.__takeCount__);if(!l||!d&&m==N&&ge==N)return Em(r,this.__actions__);var De=[];e:for(;N--&&le<ge;){L+=o;for(var Be=-1,Ie=r[L];++Be<j;){var $e=B[Be],Ge=$e.iteratee,ms=$e.type,Xt=Ge(Ie);if(ms==pt)Ie=Xt;else if(!Xt){if(ms==_t)continue e;break e}}De[le++]=Ie}return De}je.prototype=Xn(Ia.prototype),je.prototype.constructor=je;function bn(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function tA(){this.__data__=Xo?Xo(null):{},this.size=0}function sA(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function rA(r){var o=this.__data__;if(Xo){var l=o[r];return l===h?s:l}return nt.call(o,r)?o[r]:s}function nA(r){var o=this.__data__;return Xo?o[r]!==s:nt.call(o,r)}function oA(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Xo&&o===s?h:o,this}bn.prototype.clear=tA,bn.prototype.delete=sA,bn.prototype.get=rA,bn.prototype.has=nA,bn.prototype.set=oA;function vr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function iA(){this.__data__=[],this.size=0}function aA(r){var o=this.__data__,l=Na(o,r);if(l<0)return!1;var d=o.length-1;return l==d?o.pop():wa.call(o,l,1),--this.size,!0}function lA(r){var o=this.__data__,l=Na(o,r);return l<0?s:o[l][1]}function uA(r){return Na(this.__data__,r)>-1}function cA(r,o){var l=this.__data__,d=Na(l,r);return d<0?(++this.size,l.push([r,o])):l[d][1]=o,this}vr.prototype.clear=iA,vr.prototype.delete=aA,vr.prototype.get=lA,vr.prototype.has=uA,vr.prototype.set=cA;function yr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function dA(){this.size=0,this.__data__={hash:new bn,map:new(Jo||vr),string:new bn}}function fA(r){var o=qa(this,r).delete(r);return this.size-=o?1:0,o}function hA(r){return qa(this,r).get(r)}function pA(r){return qa(this,r).has(r)}function mA(r,o){var l=qa(this,r),d=l.size;return l.set(r,o),this.size+=l.size==d?0:1,this}yr.prototype.clear=dA,yr.prototype.delete=fA,yr.prototype.get=hA,yr.prototype.has=pA,yr.prototype.set=mA;function Cn(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new yr;++o<l;)this.add(r[o])}function gA(r){return this.__data__.set(r,h),this}function _A(r){return this.__data__.has(r)}Cn.prototype.add=Cn.prototype.push=gA,Cn.prototype.has=_A;function Ls(r){var o=this.__data__=new vr(r);this.size=o.size}function vA(){this.__data__=new vr,this.size=0}function yA(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function bA(r){return this.__data__.get(r)}function CA(r){return this.__data__.has(r)}function wA(r,o){var l=this.__data__;if(l instanceof vr){var d=l.__data__;if(!Jo||d.length<n-1)return d.push([r,o]),this.size=++l.size,this;l=this.__data__=new yr(d)}return l.set(r,o),this.size=l.size,this}Ls.prototype.clear=vA,Ls.prototype.delete=yA,Ls.prototype.get=bA,Ls.prototype.has=CA,Ls.prototype.set=wA;function em(r,o){var l=Re(r),d=!l&&Sn(r),m=!l&&!d&&Qr(r),b=!l&&!d&&!m&&ro(r),w=l||d||m||b,S=w?Uu(r.length,MT):[],N=S.length;for(var L in r)(o||nt.call(r,L))&&!(w&&(L=="length"||m&&(L=="offset"||L=="parent")||b&&(L=="buffer"||L=="byteLength"||L=="byteOffset")||Er(L,N)))&&S.push(L);return S}function tm(r){var o=r.length;return o?r[tc(0,o-1)]:s}function EA(r,o){return Ha(rs(r),wn(o,0,r.length))}function OA(r){return Ha(rs(r))}function ju(r,o,l){(l!==s&&!Bs(r[o],l)||l===s&&!(o in r))&&br(r,o,l)}function ti(r,o,l){var d=r[o];(!(nt.call(r,o)&&Bs(d,l))||l===s&&!(o in r))&&br(r,o,l)}function Na(r,o){for(var l=r.length;l--;)if(Bs(r[l][0],o))return l;return-1}function xA(r,o,l,d){return jr(r,function(m,b,w){o(d,m,l(m),w)}),d}function sm(r,o){return r&&rr(o,Ft(o),r)}function SA(r,o){return r&&rr(o,os(o),r)}function br(r,o,l){o=="__proto__"&&Ea?Ea(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function zu(r,o){for(var l=-1,d=o.length,m=M(d),b=r==null;++l<d;)m[l]=b?s:Sc(r,o[l]);return m}function wn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Os(r,o,l,d,m,b){var w,S=o&g,N=o&v,L=o&O;if(l&&(w=m?l(r,d,m,b):l(r)),w!==s)return w;if(!vt(r))return r;var B=Re(r);if(B){if(w=p3(r),!S)return rs(r,w)}else{var j=Wt(r),le=j==rt||j==Ve;if(Qr(r))return Sm(r,S);if(j==U||j==Fs||le&&!m){if(w=N||le?{}:jm(r),!S)return N?r3(r,SA(w,r)):s3(r,sm(w,r))}else{if(!it[j])return m?r:{};w=m3(r,j,S)}}b||(b=new Ls);var ge=b.get(r);if(ge)return ge;b.set(r,w),bg(r)?r.forEach(function(Ie){w.add(Os(Ie,o,l,Ie,r,b))}):vg(r)&&r.forEach(function(Ie,$e){w.set($e,Os(Ie,o,l,$e,r,b))});var De=L?N?fc:dc:N?os:Ft,Be=B?s:De(r);return Cs(Be||r,function(Ie,$e){Be&&($e=Ie,Ie=r[$e]),ti(w,$e,Os(Ie,o,l,$e,r,b))}),w}function DA(r){var o=Ft(r);return function(l){return rm(l,r,o)}}function rm(r,o,l){var d=l.length;if(r==null)return!d;for(r=ot(r);d--;){var m=l[d],b=o[m],w=r[m];if(w===s&&!(m in r)||!b(w))return!1}return!0}function nm(r,o,l){if(typeof r!="function")throw new ws(u);return li(function(){r.apply(s,l)},o)}function si(r,o,l,d){var m=-1,b=fa,w=!0,S=r.length,N=[],L=o.length;if(!S)return N;l&&(o=mt(o,fs(l))),d?(b=Mu,w=!1):o.length>=n&&(b=Qo,w=!1,o=new Cn(o));e:for(;++m<S;){var B=r[m],j=l==null?B:l(B);if(B=d||B!==0?B:0,w&&j===j){for(var le=L;le--;)if(o[le]===j)continue e;N.push(B)}else b(o,j,d)||N.push(B)}return N}var jr=Am(sr),om=Am(Ku,!0);function IA(r,o){var l=!0;return jr(r,function(d,m,b){return l=!!o(d,m,b),l}),l}function Ta(r,o,l){for(var d=-1,m=r.length;++d<m;){var b=r[d],w=o(b);if(w!=null&&(S===s?w===w&&!ps(w):l(w,S)))var S=w,N=b}return N}function NA(r,o,l,d){var m=r.length;for(l=Ue(l),l<0&&(l=-l>m?0:m+l),d=d===s||d>m?m:Ue(d),d<0&&(d+=m),d=l>d?0:wg(d);l<d;)r[l++]=o;return r}function im(r,o){var l=[];return jr(r,function(d,m,b){o(d,m,b)&&l.push(d)}),l}function qt(r,o,l,d,m){var b=-1,w=r.length;for(l||(l=_3),m||(m=[]);++b<w;){var S=r[b];o>0&&l(S)?o>1?qt(S,o-1,l,d,m):Hr(m,S):d||(m[m.length]=S)}return m}var Gu=Mm(),am=Mm(!0);function sr(r,o){return r&&Gu(r,o,Ft)}function Ku(r,o){return r&&am(r,o,Ft)}function Aa(r,o){return qr(o,function(l){return Or(r[l])})}function En(r,o){o=Gr(o,r);for(var l=0,d=o.length;r!=null&&l<d;)r=r[nr(o[l++])];return l&&l==d?r:s}function lm(r,o,l){var d=o(r);return Re(r)?d:Hr(d,l(r))}function Jt(r){return r==null?r===s?ve:P:yn&&yn in ot(r)?d3(r):O3(r)}function Qu(r,o){return r>o}function TA(r,o){return r!=null&&nt.call(r,o)}function AA(r,o){return r!=null&&o in ot(r)}function MA(r,o,l){return r>=$t(o,l)&&r<Mt(o,l)}function Zu(r,o,l){for(var d=l?Mu:fa,m=r[0].length,b=r.length,w=b,S=M(b),N=1/0,L=[];w--;){var B=r[w];w&&o&&(B=mt(B,fs(o))),N=$t(B.length,N),S[w]=!l&&(o||m>=120&&B.length>=120)?new Cn(w&&B):s}B=r[0];var j=-1,le=S[0];e:for(;++j<m&&L.length<N;){var ge=B[j],De=o?o(ge):ge;if(ge=l||ge!==0?ge:0,!(le?Qo(le,De):d(L,De,l))){for(w=b;--w;){var Be=S[w];if(!(Be?Qo(Be,De):d(r[w],De,l)))continue e}le&&le.push(De),L.push(ge)}}return L}function PA(r,o,l,d){return sr(r,function(m,b,w){o(d,l(m),b,w)}),d}function ri(r,o,l){o=Gr(o,r),r=Qm(r,o);var d=r==null?r:r[nr(Ss(o))];return d==null?s:ds(d,r,l)}function um(r){return yt(r)&&Jt(r)==Fs}function kA(r){return yt(r)&&Jt(r)==Oe}function VA(r){return yt(r)&&Jt(r)==ne}function ni(r,o,l,d,m){return r===o?!0:r==null||o==null||!yt(r)&&!yt(o)?r!==r&&o!==o:RA(r,o,l,d,ni,m)}function RA(r,o,l,d,m,b){var w=Re(r),S=Re(o),N=w?cs:Wt(r),L=S?cs:Wt(o);N=N==Fs?U:N,L=L==Fs?U:L;var B=N==U,j=L==U,le=N==L;if(le&&Qr(r)){if(!Qr(o))return!1;w=!0,B=!1}if(le&&!B)return b||(b=new Ls),w||ro(r)?Hm(r,o,l,d,m,b):u3(r,o,N,l,d,m,b);if(!(l&k)){var ge=B&&nt.call(r,"__wrapped__"),De=j&&nt.call(o,"__wrapped__");if(ge||De){var Be=ge?r.value():r,Ie=De?o.value():o;return b||(b=new Ls),m(Be,Ie,l,d,b)}}return le?(b||(b=new Ls),c3(r,o,l,d,m,b)):!1}function FA(r){return yt(r)&&Wt(r)==C}function Ju(r,o,l,d){var m=l.length,b=m,w=!d;if(r==null)return!b;for(r=ot(r);m--;){var S=l[m];if(w&&S[2]?S[1]!==r[S[0]]:!(S[0]in r))return!1}for(;++m<b;){S=l[m];var N=S[0],L=r[N],B=S[1];if(w&&S[2]){if(L===s&&!(N in r))return!1}else{var j=new Ls;if(d)var le=d(L,B,N,r,o,j);if(!(le===s?ni(B,L,k|V,d,j):le))return!1}}return!0}function cm(r){if(!vt(r)||y3(r))return!1;var o=Or(r)?FT:DN;return o.test(xn(r))}function UA(r){return yt(r)&&Jt(r)==te}function LA(r){return yt(r)&&Wt(r)==Q}function BA(r){return yt(r)&&Ka(r.length)&&!!dt[Jt(r)]}function dm(r){return typeof r=="function"?r:r==null?is:typeof r=="object"?Re(r)?pm(r[0],r[1]):hm(r):Pg(r)}function Yu(r){if(!ai(r))return $T(r);var o=[];for(var l in ot(r))nt.call(r,l)&&l!="constructor"&&o.push(l);return o}function qA(r){if(!vt(r))return E3(r);var o=ai(r),l=[];for(var d in r)d=="constructor"&&(o||!nt.call(r,d))||l.push(d);return l}function Xu(r,o){return r<o}function fm(r,o){var l=-1,d=ns(r)?M(r.length):[];return jr(r,function(m,b,w){d[++l]=o(m,b,w)}),d}function hm(r){var o=pc(r);return o.length==1&&o[0][2]?Gm(o[0][0],o[0][1]):function(l){return l===r||Ju(l,r,o)}}function pm(r,o){return gc(r)&&zm(o)?Gm(nr(r),o):function(l){var d=Sc(l,r);return d===s&&d===o?Dc(l,r):ni(o,d,k|V)}}function Ma(r,o,l,d,m){r!==o&&Gu(o,function(b,w){if(m||(m=new Ls),vt(b))HA(r,o,w,l,Ma,d,m);else{var S=d?d(vc(r,w),b,w+"",r,o,m):s;S===s&&(S=b),ju(r,w,S)}},os)}function HA(r,o,l,d,m,b,w){var S=vc(r,l),N=vc(o,l),L=w.get(N);if(L){ju(r,l,L);return}var B=b?b(S,N,l+"",r,o,w):s,j=B===s;if(j){var le=Re(N),ge=!le&&Qr(N),De=!le&&!ge&&ro(N);B=N,le||ge||De?Re(S)?B=S:Et(S)?B=rs(S):ge?(j=!1,B=Sm(N,!0)):De?(j=!1,B=Dm(N,!0)):B=[]:ui(N)||Sn(N)?(B=S,Sn(S)?B=Eg(S):(!vt(S)||Or(S))&&(B=jm(N))):j=!1}j&&(w.set(N,B),m(B,N,d,b,w),w.delete(N)),ju(r,l,B)}function mm(r,o){var l=r.length;if(l)return o+=o<0?l:0,Er(o,l)?r[o]:s}function gm(r,o,l){o.length?o=mt(o,function(b){return Re(b)?function(w){return En(w,b.length===1?b[0]:b)}:b}):o=[is];var d=-1;o=mt(o,fs(xe()));var m=fm(r,function(b,w,S){var N=mt(o,function(L){return L(b)});return{criteria:N,index:++d,value:b}});return mT(m,function(b,w){return t3(b,w,l)})}function $A(r,o){return _m(r,o,function(l,d){return Dc(r,d)})}function _m(r,o,l){for(var d=-1,m=o.length,b={};++d<m;){var w=o[d],S=En(r,w);l(S,w)&&oi(b,Gr(w,r),S)}return b}function WA(r){return function(o){return En(o,r)}}function ec(r,o,l,d){var m=d?pT:zn,b=-1,w=o.length,S=r;for(r===o&&(o=rs(o)),l&&(S=mt(r,fs(l)));++b<w;)for(var N=0,L=o[b],B=l?l(L):L;(N=m(S,B,N,d))>-1;)S!==r&&wa.call(S,N,1),wa.call(r,N,1);return r}function vm(r,o){for(var l=r?o.length:0,d=l-1;l--;){var m=o[l];if(l==d||m!==b){var b=m;Er(m)?wa.call(r,m,1):nc(r,m)}}return r}function tc(r,o){return r+xa(Yp()*(o-r+1))}function jA(r,o,l,d){for(var m=-1,b=Mt(Oa((o-r)/(l||1)),0),w=M(b);b--;)w[d?b:++m]=r,r+=l;return w}function sc(r,o){var l="";if(!r||o<1||o>ye)return l;do o%2&&(l+=r),o=xa(o/2),o&&(r+=r);while(o);return l}function He(r,o){return yc(Km(r,o,is),r+"")}function zA(r){return tm(no(r))}function GA(r,o){var l=no(r);return Ha(l,wn(o,0,l.length))}function oi(r,o,l,d){if(!vt(r))return r;o=Gr(o,r);for(var m=-1,b=o.length,w=b-1,S=r;S!=null&&++m<b;){var N=nr(o[m]),L=l;if(N==="__proto__"||N==="constructor"||N==="prototype")return r;if(m!=w){var B=S[N];L=d?d(B,N,S):s,L===s&&(L=vt(B)?B:Er(o[m+1])?[]:{})}ti(S,N,L),S=S[N]}return r}var ym=Sa?function(r,o){return Sa.set(r,o),r}:is,KA=Ea?function(r,o){return Ea(r,"toString",{configurable:!0,enumerable:!1,value:Nc(o),writable:!0})}:is;function QA(r){return Ha(no(r))}function xs(r,o,l){var d=-1,m=r.length;o<0&&(o=-o>m?0:m+o),l=l>m?m:l,l<0&&(l+=m),m=o>l?0:l-o>>>0,o>>>=0;for(var b=M(m);++d<m;)b[d]=r[d+o];return b}function ZA(r,o){var l;return jr(r,function(d,m,b){return l=o(d,m,b),!l}),!!l}function Pa(r,o,l){var d=0,m=r==null?d:r.length;if(typeof o=="number"&&o===o&&m<=ss){for(;d<m;){var b=d+m>>>1,w=r[b];w!==null&&!ps(w)&&(l?w<=o:w<o)?d=b+1:m=b}return m}return rc(r,o,is,l)}function rc(r,o,l,d){var m=0,b=r==null?0:r.length;if(b===0)return 0;o=l(o);for(var w=o!==o,S=o===null,N=ps(o),L=o===s;m<b;){var B=xa((m+b)/2),j=l(r[B]),le=j!==s,ge=j===null,De=j===j,Be=ps(j);if(w)var Ie=d||De;else L?Ie=De&&(d||le):S?Ie=De&&le&&(d||!ge):N?Ie=De&&le&&!ge&&(d||!Be):ge||Be?Ie=!1:Ie=d?j<=o:j<o;Ie?m=B+1:b=B}return $t(b,vs)}function bm(r,o){for(var l=-1,d=r.length,m=0,b=[];++l<d;){var w=r[l],S=o?o(w):w;if(!l||!Bs(S,N)){var N=S;b[m++]=w===0?0:w}}return b}function Cm(r){return typeof r=="number"?r:ps(r)?wt:+r}function hs(r){if(typeof r=="string")return r;if(Re(r))return mt(r,hs)+"";if(ps(r))return Xp?Xp.call(r):"";var o=r+"";return o=="0"&&1/r==-K?"-0":o}function zr(r,o,l){var d=-1,m=fa,b=r.length,w=!0,S=[],N=S;if(l)w=!1,m=Mu;else if(b>=n){var L=o?null:a3(r);if(L)return pa(L);w=!1,m=Qo,N=new Cn}else N=o?[]:S;e:for(;++d<b;){var B=r[d],j=o?o(B):B;if(B=l||B!==0?B:0,w&&j===j){for(var le=N.length;le--;)if(N[le]===j)continue e;o&&N.push(j),S.push(B)}else m(N,j,l)||(N!==S&&N.push(j),S.push(B))}return S}function nc(r,o){return o=Gr(o,r),r=Qm(r,o),r==null||delete r[nr(Ss(o))]}function wm(r,o,l,d){return oi(r,o,l(En(r,o)),d)}function ka(r,o,l,d){for(var m=r.length,b=d?m:-1;(d?b--:++b<m)&&o(r[b],b,r););return l?xs(r,d?0:b,d?b+1:m):xs(r,d?b+1:0,d?m:b)}function Em(r,o){var l=r;return l instanceof je&&(l=l.value()),Pu(o,function(d,m){return m.func.apply(m.thisArg,Hr([d],m.args))},l)}function oc(r,o,l){var d=r.length;if(d<2)return d?zr(r[0]):[];for(var m=-1,b=M(d);++m<d;)for(var w=r[m],S=-1;++S<d;)S!=m&&(b[m]=si(b[m]||w,r[S],o,l));return zr(qt(b,1),o,l)}function Om(r,o,l){for(var d=-1,m=r.length,b=o.length,w={};++d<m;){var S=d<b?o[d]:s;l(w,r[d],S)}return w}function ic(r){return Et(r)?r:[]}function ac(r){return typeof r=="function"?r:is}function Gr(r,o){return Re(r)?r:gc(r,o)?[r]:Xm(st(r))}var JA=He;function Kr(r,o,l){var d=r.length;return l=l===s?d:l,!o&&l>=d?r:xs(r,o,l)}var xm=UT||function(r){return Bt.clearTimeout(r)};function Sm(r,o){if(o)return r.slice();var l=r.length,d=Gp?Gp(l):new r.constructor(l);return r.copy(d),d}function lc(r){var o=new r.constructor(r.byteLength);return new ba(o).set(new ba(r)),o}function YA(r,o){var l=o?lc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function XA(r){var o=new r.constructor(r.source,up.exec(r));return o.lastIndex=r.lastIndex,o}function e3(r){return ei?ot(ei.call(r)):{}}function Dm(r,o){var l=o?lc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Im(r,o){if(r!==o){var l=r!==s,d=r===null,m=r===r,b=ps(r),w=o!==s,S=o===null,N=o===o,L=ps(o);if(!S&&!L&&!b&&r>o||b&&w&&N&&!S&&!L||d&&w&&N||!l&&N||!m)return 1;if(!d&&!b&&!L&&r<o||L&&l&&m&&!d&&!b||S&&l&&m||!w&&m||!N)return-1}return 0}function t3(r,o,l){for(var d=-1,m=r.criteria,b=o.criteria,w=m.length,S=l.length;++d<w;){var N=Im(m[d],b[d]);if(N){if(d>=S)return N;var L=l[d];return N*(L=="desc"?-1:1)}}return r.index-o.index}function Nm(r,o,l,d){for(var m=-1,b=r.length,w=l.length,S=-1,N=o.length,L=Mt(b-w,0),B=M(N+L),j=!d;++S<N;)B[S]=o[S];for(;++m<w;)(j||m<b)&&(B[l[m]]=r[m]);for(;L--;)B[S++]=r[m++];return B}function Tm(r,o,l,d){for(var m=-1,b=r.length,w=-1,S=l.length,N=-1,L=o.length,B=Mt(b-S,0),j=M(B+L),le=!d;++m<B;)j[m]=r[m];for(var ge=m;++N<L;)j[ge+N]=o[N];for(;++w<S;)(le||m<b)&&(j[ge+l[w]]=r[m++]);return j}function rs(r,o){var l=-1,d=r.length;for(o||(o=M(d));++l<d;)o[l]=r[l];return o}function rr(r,o,l,d){var m=!l;l||(l={});for(var b=-1,w=o.length;++b<w;){var S=o[b],N=d?d(l[S],r[S],S,l,r):s;N===s&&(N=r[S]),m?br(l,S,N):ti(l,S,N)}return l}function s3(r,o){return rr(r,mc(r),o)}function r3(r,o){return rr(r,$m(r),o)}function Va(r,o){return function(l,d){var m=Re(l)?lT:xA,b=o?o():{};return m(l,r,xe(d,2),b)}}function eo(r){return He(function(o,l){var d=-1,m=l.length,b=m>1?l[m-1]:s,w=m>2?l[2]:s;for(b=r.length>3&&typeof b=="function"?(m--,b):s,w&&Yt(l[0],l[1],w)&&(b=m<3?s:b,m=1),o=ot(o);++d<m;){var S=l[d];S&&r(o,S,d,b)}return o})}function Am(r,o){return function(l,d){if(l==null)return l;if(!ns(l))return r(l,d);for(var m=l.length,b=o?m:-1,w=ot(l);(o?b--:++b<m)&&d(w[b],b,w)!==!1;);return l}}function Mm(r){return function(o,l,d){for(var m=-1,b=ot(o),w=d(o),S=w.length;S--;){var N=w[r?S:++m];if(l(b[N],N,b)===!1)break}return o}}function n3(r,o,l){var d=o&re,m=ii(r);function b(){var w=this&&this!==Bt&&this instanceof b?m:r;return w.apply(d?l:this,arguments)}return b}function Pm(r){return function(o){o=st(o);var l=Gn(o)?Us(o):s,d=l?l[0]:o.charAt(0),m=l?Kr(l,1).join(""):o.slice(1);return d[r]()+m}}function to(r){return function(o){return Pu(Ag(Tg(o).replace(KN,"")),r,"")}}function ii(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Xn(r.prototype),d=r.apply(l,o);return vt(d)?d:l}}function o3(r,o,l){var d=ii(r);function m(){for(var b=arguments.length,w=M(b),S=b,N=so(m);S--;)w[S]=arguments[S];var L=b<3&&w[0]!==N&&w[b-1]!==N?[]:$r(w,N);if(b-=L.length,b<l)return Um(r,o,Ra,m.placeholder,s,w,L,s,s,l-b);var B=this&&this!==Bt&&this instanceof m?d:r;return ds(B,this,w)}return m}function km(r){return function(o,l,d){var m=ot(o);if(!ns(o)){var b=xe(l,3);o=Ft(o),l=function(S){return b(m[S],S,m)}}var w=r(o,l,d);return w>-1?m[b?o[w]:w]:s}}function Vm(r){return wr(function(o){var l=o.length,d=l,m=Es.prototype.thru;for(r&&o.reverse();d--;){var b=o[d];if(typeof b!="function")throw new ws(u);if(m&&!w&&Ba(b)=="wrapper")var w=new Es([],!0)}for(d=w?d:l;++d<l;){b=o[d];var S=Ba(b),N=S=="wrapper"?hc(b):s;N&&_c(N[0])&&N[1]==(_e|Y|X|Ne)&&!N[4].length&&N[9]==1?w=w[Ba(N[0])].apply(w,N[3]):w=b.length==1&&_c(b)?w[S]():w.thru(b)}return function(){var L=arguments,B=L[0];if(w&&L.length==1&&Re(B))return w.plant(B).value();for(var j=0,le=l?o[j].apply(this,L):B;++j<l;)le=o[j].call(this,le);return le}})}function Ra(r,o,l,d,m,b,w,S,N,L){var B=o&_e,j=o&re,le=o&J,ge=o&(Y|Ee),De=o&ae,Be=le?s:ii(r);function Ie(){for(var $e=arguments.length,Ge=M($e),ms=$e;ms--;)Ge[ms]=arguments[ms];if(ge)var Xt=so(Ie),gs=_T(Ge,Xt);if(d&&(Ge=Nm(Ge,d,m,ge)),b&&(Ge=Tm(Ge,b,w,ge)),$e-=gs,ge&&$e<L){var Ot=$r(Ge,Xt);return Um(r,o,Ra,Ie.placeholder,l,Ge,Ot,S,N,L-$e)}var qs=j?l:this,Sr=le?qs[r]:r;return $e=Ge.length,S?Ge=x3(Ge,S):De&&$e>1&&Ge.reverse(),B&&N<$e&&(Ge.length=N),this&&this!==Bt&&this instanceof Ie&&(Sr=Be||ii(Sr)),Sr.apply(qs,Ge)}return Ie}function Rm(r,o){return function(l,d){return PA(l,r,o(d),{})}}function Fa(r,o){return function(l,d){var m;if(l===s&&d===s)return o;if(l!==s&&(m=l),d!==s){if(m===s)return d;typeof l=="string"||typeof d=="string"?(l=hs(l),d=hs(d)):(l=Cm(l),d=Cm(d)),m=r(l,d)}return m}}function uc(r){return wr(function(o){return o=mt(o,fs(xe())),He(function(l){var d=this;return r(o,function(m){return ds(m,d,l)})})})}function Ua(r,o){o=o===s?" ":hs(o);var l=o.length;if(l<2)return l?sc(o,r):o;var d=sc(o,Oa(r/Kn(o)));return Gn(o)?Kr(Us(d),0,r).join(""):d.slice(0,r)}function i3(r,o,l,d){var m=o&re,b=ii(r);function w(){for(var S=-1,N=arguments.length,L=-1,B=d.length,j=M(B+N),le=this&&this!==Bt&&this instanceof w?b:r;++L<B;)j[L]=d[L];for(;N--;)j[L++]=arguments[++S];return ds(le,m?l:this,j)}return w}function Fm(r){return function(o,l,d){return d&&typeof d!="number"&&Yt(o,l,d)&&(l=d=s),o=xr(o),l===s?(l=o,o=0):l=xr(l),d=d===s?o<l?1:-1:xr(d),jA(o,l,d,r)}}function La(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Ds(o),l=Ds(l)),r(o,l)}}function Um(r,o,l,d,m,b,w,S,N,L){var B=o&Y,j=B?w:s,le=B?s:w,ge=B?b:s,De=B?s:b;o|=B?X:fe,o&=~(B?fe:X),o&oe||(o&=~(re|J));var Be=[r,o,m,ge,j,De,le,S,N,L],Ie=l.apply(s,Be);return _c(r)&&Zm(Ie,Be),Ie.placeholder=d,Jm(Ie,r,o)}function cc(r){var o=At[r];return function(l,d){if(l=Ds(l),d=d==null?0:$t(Ue(d),292),d&&Jp(l)){var m=(st(l)+"e").split("e"),b=o(m[0]+"e"+(+m[1]+d));return m=(st(b)+"e").split("e"),+(m[0]+"e"+(+m[1]-d))}return o(l)}}var a3=Jn&&1/pa(new Jn([,-0]))[1]==K?function(r){return new Jn(r)}:Mc;function Lm(r){return function(o){var l=Wt(o);return l==C?Bu(o):l==Q?OT(o):gT(o,r(o))}}function Cr(r,o,l,d,m,b,w,S){var N=o&J;if(!N&&typeof r!="function")throw new ws(u);var L=d?d.length:0;if(L||(o&=~(X|fe),d=m=s),w=w===s?w:Mt(Ue(w),0),S=S===s?S:Ue(S),L-=m?m.length:0,o&fe){var B=d,j=m;d=m=s}var le=N?s:hc(r),ge=[r,o,l,d,m,B,j,b,w,S];if(le&&w3(ge,le),r=ge[0],o=ge[1],l=ge[2],d=ge[3],m=ge[4],S=ge[9]=ge[9]===s?N?0:r.length:Mt(ge[9]-L,0),!S&&o&(Y|Ee)&&(o&=~(Y|Ee)),!o||o==re)var De=n3(r,o,l);else o==Y||o==Ee?De=o3(r,o,S):(o==X||o==(re|X))&&!m.length?De=i3(r,o,l,d):De=Ra.apply(s,ge);var Be=le?ym:Zm;return Jm(Be(De,ge),r,o)}function Bm(r,o,l,d){return r===s||Bs(r,Zn[l])&&!nt.call(d,l)?o:r}function qm(r,o,l,d,m,b){return vt(r)&&vt(o)&&(b.set(o,r),Ma(r,o,s,qm,b),b.delete(o)),r}function l3(r){return ui(r)?s:r}function Hm(r,o,l,d,m,b){var w=l&k,S=r.length,N=o.length;if(S!=N&&!(w&&N>S))return!1;var L=b.get(r),B=b.get(o);if(L&&B)return L==o&&B==r;var j=-1,le=!0,ge=l&V?new Cn:s;for(b.set(r,o),b.set(o,r);++j<S;){var De=r[j],Be=o[j];if(d)var Ie=w?d(Be,De,j,o,r,b):d(De,Be,j,r,o,b);if(Ie!==s){if(Ie)continue;le=!1;break}if(ge){if(!ku(o,function($e,Ge){if(!Qo(ge,Ge)&&(De===$e||m(De,$e,l,d,b)))return ge.push(Ge)})){le=!1;break}}else if(!(De===Be||m(De,Be,l,d,b))){le=!1;break}}return b.delete(r),b.delete(o),le}function u3(r,o,l,d,m,b,w){switch(l){case Fe:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Oe:return!(r.byteLength!=o.byteLength||!b(new ba(r),new ba(o)));case ie:case ne:case E:return Bs(+r,+o);case ke:return r.name==o.name&&r.message==o.message;case te:case ee:return r==o+"";case C:var S=Bu;case Q:var N=d&k;if(S||(S=pa),r.size!=o.size&&!N)return!1;var L=w.get(r);if(L)return L==o;d|=V,w.set(r,o);var B=Hm(S(r),S(o),d,m,b,w);return w.delete(r),B;case z:if(ei)return ei.call(r)==ei.call(o)}return!1}function c3(r,o,l,d,m,b){var w=l&k,S=dc(r),N=S.length,L=dc(o),B=L.length;if(N!=B&&!w)return!1;for(var j=N;j--;){var le=S[j];if(!(w?le in o:nt.call(o,le)))return!1}var ge=b.get(r),De=b.get(o);if(ge&&De)return ge==o&&De==r;var Be=!0;b.set(r,o),b.set(o,r);for(var Ie=w;++j<N;){le=S[j];var $e=r[le],Ge=o[le];if(d)var ms=w?d(Ge,$e,le,o,r,b):d($e,Ge,le,r,o,b);if(!(ms===s?$e===Ge||m($e,Ge,l,d,b):ms)){Be=!1;break}Ie||(Ie=le=="constructor")}if(Be&&!Ie){var Xt=r.constructor,gs=o.constructor;Xt!=gs&&"constructor"in r&&"constructor"in o&&!(typeof Xt=="function"&&Xt instanceof Xt&&typeof gs=="function"&&gs instanceof gs)&&(Be=!1)}return b.delete(r),b.delete(o),Be}function wr(r){return yc(Km(r,s,rg),r+"")}function dc(r){return lm(r,Ft,mc)}function fc(r){return lm(r,os,$m)}var hc=Sa?function(r){return Sa.get(r)}:Mc;function Ba(r){for(var o=r.name+"",l=Yn[o],d=nt.call(Yn,o)?l.length:0;d--;){var m=l[d],b=m.func;if(b==null||b==r)return m.name}return o}function so(r){var o=nt.call(y,"placeholder")?y:r;return o.placeholder}function xe(){var r=y.iteratee||Tc;return r=r===Tc?dm:r,arguments.length?r(arguments[0],arguments[1]):r}function qa(r,o){var l=r.__data__;return v3(o)?l[typeof o=="string"?"string":"hash"]:l.map}function pc(r){for(var o=Ft(r),l=o.length;l--;){var d=o[l],m=r[d];o[l]=[d,m,zm(m)]}return o}function On(r,o){var l=CT(r,o);return cm(l)?l:s}function d3(r){var o=nt.call(r,yn),l=r[yn];try{r[yn]=s;var d=!0}catch{}var m=va.call(r);return d&&(o?r[yn]=l:delete r[yn]),m}var mc=Hu?function(r){return r==null?[]:(r=ot(r),qr(Hu(r),function(o){return Qp.call(r,o)}))}:Pc,$m=Hu?function(r){for(var o=[];r;)Hr(o,mc(r)),r=Ca(r);return o}:Pc,Wt=Jt;($u&&Wt(new $u(new ArrayBuffer(1)))!=Fe||Jo&&Wt(new Jo)!=C||Wu&&Wt(Wu.resolve())!=H||Jn&&Wt(new Jn)!=Q||Yo&&Wt(new Yo)!=se)&&(Wt=function(r){var o=Jt(r),l=o==U?r.constructor:s,d=l?xn(l):"";if(d)switch(d){case GT:return Fe;case KT:return C;case QT:return H;case ZT:return Q;case JT:return se}return o});function f3(r,o,l){for(var d=-1,m=l.length;++d<m;){var b=l[d],w=b.size;switch(b.type){case"drop":r+=w;break;case"dropRight":o-=w;break;case"take":o=$t(o,r+w);break;case"takeRight":r=Mt(r,o-w);break}}return{start:r,end:o}}function h3(r){var o=r.match(yN);return o?o[1].split(bN):[]}function Wm(r,o,l){o=Gr(o,r);for(var d=-1,m=o.length,b=!1;++d<m;){var w=nr(o[d]);if(!(b=r!=null&&l(r,w)))break;r=r[w]}return b||++d!=m?b:(m=r==null?0:r.length,!!m&&Ka(m)&&Er(w,m)&&(Re(r)||Sn(r)))}function p3(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&nt.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function jm(r){return typeof r.constructor=="function"&&!ai(r)?Xn(Ca(r)):{}}function m3(r,o,l){var d=r.constructor;switch(o){case Oe:return lc(r);case ie:case ne:return new d(+r);case Fe:return YA(r,l);case Ye:case Qe:case Rt:case St:case Zt:case Lt:case _r:case Wn:case Tt:return Dm(r,l);case C:return new d;case E:case ee:return new d(r);case te:return XA(r);case Q:return new d;case z:return e3(r)}}function g3(r,o){var l=o.length;if(!l)return r;var d=l-1;return o[d]=(l>1?"& ":"")+o[d],o=o.join(l>2?", ":" "),r.replace(vN,`{
/* [wrapped with `+o+`] */
`)}function _3(r){return Re(r)||Sn(r)||!!(Zp&&r&&r[Zp])}function Er(r,o){var l=typeof r;return o=o??ye,!!o&&(l=="number"||l!="symbol"&&NN.test(r))&&r>-1&&r%1==0&&r<o}function Yt(r,o,l){if(!vt(l))return!1;var d=typeof o;return(d=="number"?ns(l)&&Er(o,l.length):d=="string"&&o in l)?Bs(l[o],r):!1}function gc(r,o){if(Re(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||ps(r)?!0:pN.test(r)||!hN.test(r)||o!=null&&r in ot(o)}function v3(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function _c(r){var o=Ba(r),l=y[o];if(typeof l!="function"||!(o in je.prototype))return!1;if(r===l)return!0;var d=hc(l);return!!d&&r===d[0]}function y3(r){return!!zp&&zp in r}var b3=ga?Or:kc;function ai(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||Zn;return r===l}function zm(r){return r===r&&!vt(r)}function Gm(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in ot(l))}}function C3(r){var o=za(r,function(d){return l.size===_&&l.clear(),d}),l=o.cache;return o}function w3(r,o){var l=r[1],d=o[1],m=l|d,b=m<(re|J|_e),w=d==_e&&l==Y||d==_e&&l==Ne&&r[7].length<=o[8]||d==(_e|Ne)&&o[7].length<=o[8]&&l==Y;if(!(b||w))return r;d&re&&(r[2]=o[2],m|=l&re?0:oe);var S=o[3];if(S){var N=r[3];r[3]=N?Nm(N,S,o[4]):S,r[4]=N?$r(r[3],p):o[4]}return S=o[5],S&&(N=r[5],r[5]=N?Tm(N,S,o[6]):S,r[6]=N?$r(r[5],p):o[6]),S=o[7],S&&(r[7]=S),d&_e&&(r[8]=r[8]==null?o[8]:$t(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=m,r}function E3(r){var o=[];if(r!=null)for(var l in ot(r))o.push(l);return o}function O3(r){return va.call(r)}function Km(r,o,l){return o=Mt(o===s?r.length-1:o,0),function(){for(var d=arguments,m=-1,b=Mt(d.length-o,0),w=M(b);++m<b;)w[m]=d[o+m];m=-1;for(var S=M(o+1);++m<o;)S[m]=d[m];return S[o]=l(w),ds(r,this,S)}}function Qm(r,o){return o.length<2?r:En(r,xs(o,0,-1))}function x3(r,o){for(var l=r.length,d=$t(o.length,l),m=rs(r);d--;){var b=o[d];r[d]=Er(b,l)?m[b]:s}return r}function vc(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var Zm=Ym(ym),li=BT||function(r,o){return Bt.setTimeout(r,o)},yc=Ym(KA);function Jm(r,o,l){var d=o+"";return yc(r,g3(d,S3(h3(d),l)))}function Ym(r){var o=0,l=0;return function(){var d=WT(),m=ze-(d-l);if(l=d,m>0){if(++o>=ue)return arguments[0]}else o=0;return r.apply(s,arguments)}}function Ha(r,o){var l=-1,d=r.length,m=d-1;for(o=o===s?d:o;++l<o;){var b=tc(l,m),w=r[b];r[b]=r[l],r[l]=w}return r.length=o,r}var Xm=C3(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(mN,function(l,d,m,b){o.push(m?b.replace(EN,"$1"):d||l)}),o});function nr(r){if(typeof r=="string"||ps(r))return r;var o=r+"";return o=="0"&&1/r==-K?"-0":o}function xn(r){if(r!=null){try{return _a.call(r)}catch{}try{return r+""}catch{}}return""}function S3(r,o){return Cs(tr,function(l){var d="_."+l[0];o&l[1]&&!fa(r,d)&&r.push(d)}),r.sort()}function eg(r){if(r instanceof je)return r.clone();var o=new Es(r.__wrapped__,r.__chain__);return o.__actions__=rs(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function D3(r,o,l){(l?Yt(r,o,l):o===s)?o=1:o=Mt(Ue(o),0);var d=r==null?0:r.length;if(!d||o<1)return[];for(var m=0,b=0,w=M(Oa(d/o));m<d;)w[b++]=xs(r,m,m+=o);return w}function I3(r){for(var o=-1,l=r==null?0:r.length,d=0,m=[];++o<l;){var b=r[o];b&&(m[d++]=b)}return m}function N3(){var r=arguments.length;if(!r)return[];for(var o=M(r-1),l=arguments[0],d=r;d--;)o[d-1]=arguments[d];return Hr(Re(l)?rs(l):[l],qt(o,1))}var T3=He(function(r,o){return Et(r)?si(r,qt(o,1,Et,!0)):[]}),A3=He(function(r,o){var l=Ss(o);return Et(l)&&(l=s),Et(r)?si(r,qt(o,1,Et,!0),xe(l,2)):[]}),M3=He(function(r,o){var l=Ss(o);return Et(l)&&(l=s),Et(r)?si(r,qt(o,1,Et,!0),s,l):[]});function P3(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Ue(o),xs(r,o<0?0:o,d)):[]}function k3(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Ue(o),o=d-o,xs(r,0,o<0?0:o)):[]}function V3(r,o){return r&&r.length?ka(r,xe(o,3),!0,!0):[]}function R3(r,o){return r&&r.length?ka(r,xe(o,3),!0):[]}function F3(r,o,l,d){var m=r==null?0:r.length;return m?(l&&typeof l!="number"&&Yt(r,o,l)&&(l=0,d=m),NA(r,o,l,d)):[]}function tg(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=l==null?0:Ue(l);return m<0&&(m=Mt(d+m,0)),ha(r,xe(o,3),m)}function sg(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=d-1;return l!==s&&(m=Ue(l),m=l<0?Mt(d+m,0):$t(m,d-1)),ha(r,xe(o,3),m,!0)}function rg(r){var o=r==null?0:r.length;return o?qt(r,1):[]}function U3(r){var o=r==null?0:r.length;return o?qt(r,K):[]}function L3(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:Ue(o),qt(r,o)):[]}function B3(r){for(var o=-1,l=r==null?0:r.length,d={};++o<l;){var m=r[o];d[m[0]]=m[1]}return d}function ng(r){return r&&r.length?r[0]:s}function q3(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=l==null?0:Ue(l);return m<0&&(m=Mt(d+m,0)),zn(r,o,m)}function H3(r){var o=r==null?0:r.length;return o?xs(r,0,-1):[]}var $3=He(function(r){var o=mt(r,ic);return o.length&&o[0]===r[0]?Zu(o):[]}),W3=He(function(r){var o=Ss(r),l=mt(r,ic);return o===Ss(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Zu(l,xe(o,2)):[]}),j3=He(function(r){var o=Ss(r),l=mt(r,ic);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Zu(l,s,o):[]});function z3(r,o){return r==null?"":HT.call(r,o)}function Ss(r){var o=r==null?0:r.length;return o?r[o-1]:s}function G3(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var m=d;return l!==s&&(m=Ue(l),m=m<0?Mt(d+m,0):$t(m,d-1)),o===o?ST(r,o,m):ha(r,Up,m,!0)}function K3(r,o){return r&&r.length?mm(r,Ue(o)):s}var Q3=He(og);function og(r,o){return r&&r.length&&o&&o.length?ec(r,o):r}function Z3(r,o,l){return r&&r.length&&o&&o.length?ec(r,o,xe(l,2)):r}function J3(r,o,l){return r&&r.length&&o&&o.length?ec(r,o,s,l):r}var Y3=wr(function(r,o){var l=r==null?0:r.length,d=zu(r,o);return vm(r,mt(o,function(m){return Er(m,l)?+m:m}).sort(Im)),d});function X3(r,o){var l=[];if(!(r&&r.length))return l;var d=-1,m=[],b=r.length;for(o=xe(o,3);++d<b;){var w=r[d];o(w,d,r)&&(l.push(w),m.push(d))}return vm(r,m),l}function bc(r){return r==null?r:zT.call(r)}function e4(r,o,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&Yt(r,o,l)?(o=0,l=d):(o=o==null?0:Ue(o),l=l===s?d:Ue(l)),xs(r,o,l)):[]}function t4(r,o){return Pa(r,o)}function s4(r,o,l){return rc(r,o,xe(l,2))}function r4(r,o){var l=r==null?0:r.length;if(l){var d=Pa(r,o);if(d<l&&Bs(r[d],o))return d}return-1}function n4(r,o){return Pa(r,o,!0)}function o4(r,o,l){return rc(r,o,xe(l,2),!0)}function i4(r,o){var l=r==null?0:r.length;if(l){var d=Pa(r,o,!0)-1;if(Bs(r[d],o))return d}return-1}function a4(r){return r&&r.length?bm(r):[]}function l4(r,o){return r&&r.length?bm(r,xe(o,2)):[]}function u4(r){var o=r==null?0:r.length;return o?xs(r,1,o):[]}function c4(r,o,l){return r&&r.length?(o=l||o===s?1:Ue(o),xs(r,0,o<0?0:o)):[]}function d4(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Ue(o),o=d-o,xs(r,o<0?0:o,d)):[]}function f4(r,o){return r&&r.length?ka(r,xe(o,3),!1,!0):[]}function h4(r,o){return r&&r.length?ka(r,xe(o,3)):[]}var p4=He(function(r){return zr(qt(r,1,Et,!0))}),m4=He(function(r){var o=Ss(r);return Et(o)&&(o=s),zr(qt(r,1,Et,!0),xe(o,2))}),g4=He(function(r){var o=Ss(r);return o=typeof o=="function"?o:s,zr(qt(r,1,Et,!0),s,o)});function _4(r){return r&&r.length?zr(r):[]}function v4(r,o){return r&&r.length?zr(r,xe(o,2)):[]}function y4(r,o){return o=typeof o=="function"?o:s,r&&r.length?zr(r,s,o):[]}function Cc(r){if(!(r&&r.length))return[];var o=0;return r=qr(r,function(l){if(Et(l))return o=Mt(l.length,o),!0}),Uu(o,function(l){return mt(r,Vu(l))})}function ig(r,o){if(!(r&&r.length))return[];var l=Cc(r);return o==null?l:mt(l,function(d){return ds(o,s,d)})}var b4=He(function(r,o){return Et(r)?si(r,o):[]}),C4=He(function(r){return oc(qr(r,Et))}),w4=He(function(r){var o=Ss(r);return Et(o)&&(o=s),oc(qr(r,Et),xe(o,2))}),E4=He(function(r){var o=Ss(r);return o=typeof o=="function"?o:s,oc(qr(r,Et),s,o)}),O4=He(Cc);function x4(r,o){return Om(r||[],o||[],ti)}function S4(r,o){return Om(r||[],o||[],oi)}var D4=He(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,ig(r,l)});function ag(r){var o=y(r);return o.__chain__=!0,o}function I4(r,o){return o(r),r}function $a(r,o){return o(r)}var N4=wr(function(r){var o=r.length,l=o?r[0]:0,d=this.__wrapped__,m=function(b){return zu(b,r)};return o>1||this.__actions__.length||!(d instanceof je)||!Er(l)?this.thru(m):(d=d.slice(l,+l+(o?1:0)),d.__actions__.push({func:$a,args:[m],thisArg:s}),new Es(d,this.__chain__).thru(function(b){return o&&!b.length&&b.push(s),b}))});function T4(){return ag(this)}function A4(){return new Es(this.value(),this.__chain__)}function M4(){this.__values__===s&&(this.__values__=Cg(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function P4(){return this}function k4(r){for(var o,l=this;l instanceof Ia;){var d=eg(l);d.__index__=0,d.__values__=s,o?m.__wrapped__=d:o=d;var m=d;l=l.__wrapped__}return m.__wrapped__=r,o}function V4(){var r=this.__wrapped__;if(r instanceof je){var o=r;return this.__actions__.length&&(o=new je(this)),o=o.reverse(),o.__actions__.push({func:$a,args:[bc],thisArg:s}),new Es(o,this.__chain__)}return this.thru(bc)}function R4(){return Em(this.__wrapped__,this.__actions__)}var F4=Va(function(r,o,l){nt.call(r,l)?++r[l]:br(r,l,1)});function U4(r,o,l){var d=Re(r)?Rp:IA;return l&&Yt(r,o,l)&&(o=s),d(r,xe(o,3))}function L4(r,o){var l=Re(r)?qr:im;return l(r,xe(o,3))}var B4=km(tg),q4=km(sg);function H4(r,o){return qt(Wa(r,o),1)}function $4(r,o){return qt(Wa(r,o),K)}function W4(r,o,l){return l=l===s?1:Ue(l),qt(Wa(r,o),l)}function lg(r,o){var l=Re(r)?Cs:jr;return l(r,xe(o,3))}function ug(r,o){var l=Re(r)?uT:om;return l(r,xe(o,3))}var j4=Va(function(r,o,l){nt.call(r,l)?r[l].push(o):br(r,l,[o])});function z4(r,o,l,d){r=ns(r)?r:no(r),l=l&&!d?Ue(l):0;var m=r.length;return l<0&&(l=Mt(m+l,0)),Qa(r)?l<=m&&r.indexOf(o,l)>-1:!!m&&zn(r,o,l)>-1}var G4=He(function(r,o,l){var d=-1,m=typeof o=="function",b=ns(r)?M(r.length):[];return jr(r,function(w){b[++d]=m?ds(o,w,l):ri(w,o,l)}),b}),K4=Va(function(r,o,l){br(r,l,o)});function Wa(r,o){var l=Re(r)?mt:fm;return l(r,xe(o,3))}function Q4(r,o,l,d){return r==null?[]:(Re(o)||(o=o==null?[]:[o]),l=d?s:l,Re(l)||(l=l==null?[]:[l]),gm(r,o,l))}var Z4=Va(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function J4(r,o,l){var d=Re(r)?Pu:Bp,m=arguments.length<3;return d(r,xe(o,4),l,m,jr)}function Y4(r,o,l){var d=Re(r)?cT:Bp,m=arguments.length<3;return d(r,xe(o,4),l,m,om)}function X4(r,o){var l=Re(r)?qr:im;return l(r,Ga(xe(o,3)))}function eM(r){var o=Re(r)?tm:zA;return o(r)}function tM(r,o,l){(l?Yt(r,o,l):o===s)?o=1:o=Ue(o);var d=Re(r)?EA:GA;return d(r,o)}function sM(r){var o=Re(r)?OA:QA;return o(r)}function rM(r){if(r==null)return 0;if(ns(r))return Qa(r)?Kn(r):r.length;var o=Wt(r);return o==C||o==Q?r.size:Yu(r).length}function nM(r,o,l){var d=Re(r)?ku:ZA;return l&&Yt(r,o,l)&&(o=s),d(r,xe(o,3))}var oM=He(function(r,o){if(r==null)return[];var l=o.length;return l>1&&Yt(r,o[0],o[1])?o=[]:l>2&&Yt(o[0],o[1],o[2])&&(o=[o[0]]),gm(r,qt(o,1),[])}),ja=LT||function(){return Bt.Date.now()};function iM(r,o){if(typeof o!="function")throw new ws(u);return r=Ue(r),function(){if(--r<1)return o.apply(this,arguments)}}function cg(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,Cr(r,_e,s,s,s,s,o)}function dg(r,o){var l;if(typeof o!="function")throw new ws(u);return r=Ue(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var wc=He(function(r,o,l){var d=re;if(l.length){var m=$r(l,so(wc));d|=X}return Cr(r,d,o,l,m)}),fg=He(function(r,o,l){var d=re|J;if(l.length){var m=$r(l,so(fg));d|=X}return Cr(o,d,r,l,m)});function hg(r,o,l){o=l?s:o;var d=Cr(r,Y,s,s,s,s,s,o);return d.placeholder=hg.placeholder,d}function pg(r,o,l){o=l?s:o;var d=Cr(r,Ee,s,s,s,s,s,o);return d.placeholder=pg.placeholder,d}function mg(r,o,l){var d,m,b,w,S,N,L=0,B=!1,j=!1,le=!0;if(typeof r!="function")throw new ws(u);o=Ds(o)||0,vt(l)&&(B=!!l.leading,j="maxWait"in l,b=j?Mt(Ds(l.maxWait)||0,o):b,le="trailing"in l?!!l.trailing:le);function ge(Ot){var qs=d,Sr=m;return d=m=s,L=Ot,w=r.apply(Sr,qs),w}function De(Ot){return L=Ot,S=li($e,o),B?ge(Ot):w}function Be(Ot){var qs=Ot-N,Sr=Ot-L,kg=o-qs;return j?$t(kg,b-Sr):kg}function Ie(Ot){var qs=Ot-N,Sr=Ot-L;return N===s||qs>=o||qs<0||j&&Sr>=b}function $e(){var Ot=ja();if(Ie(Ot))return Ge(Ot);S=li($e,Be(Ot))}function Ge(Ot){return S=s,le&&d?ge(Ot):(d=m=s,w)}function ms(){S!==s&&xm(S),L=0,d=N=m=S=s}function Xt(){return S===s?w:Ge(ja())}function gs(){var Ot=ja(),qs=Ie(Ot);if(d=arguments,m=this,N=Ot,qs){if(S===s)return De(N);if(j)return xm(S),S=li($e,o),ge(N)}return S===s&&(S=li($e,o)),w}return gs.cancel=ms,gs.flush=Xt,gs}var aM=He(function(r,o){return nm(r,1,o)}),lM=He(function(r,o,l){return nm(r,Ds(o)||0,l)});function uM(r){return Cr(r,ae)}function za(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new ws(u);var l=function(){var d=arguments,m=o?o.apply(this,d):d[0],b=l.cache;if(b.has(m))return b.get(m);var w=r.apply(this,d);return l.cache=b.set(m,w)||b,w};return l.cache=new(za.Cache||yr),l}za.Cache=yr;function Ga(r){if(typeof r!="function")throw new ws(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function cM(r){return dg(2,r)}var dM=JA(function(r,o){o=o.length==1&&Re(o[0])?mt(o[0],fs(xe())):mt(qt(o,1),fs(xe()));var l=o.length;return He(function(d){for(var m=-1,b=$t(d.length,l);++m<b;)d[m]=o[m].call(this,d[m]);return ds(r,this,d)})}),Ec=He(function(r,o){var l=$r(o,so(Ec));return Cr(r,X,s,o,l)}),gg=He(function(r,o){var l=$r(o,so(gg));return Cr(r,fe,s,o,l)}),fM=wr(function(r,o){return Cr(r,Ne,s,s,s,o)});function hM(r,o){if(typeof r!="function")throw new ws(u);return o=o===s?o:Ue(o),He(r,o)}function pM(r,o){if(typeof r!="function")throw new ws(u);return o=o==null?0:Mt(Ue(o),0),He(function(l){var d=l[o],m=Kr(l,0,o);return d&&Hr(m,d),ds(r,this,m)})}function mM(r,o,l){var d=!0,m=!0;if(typeof r!="function")throw new ws(u);return vt(l)&&(d="leading"in l?!!l.leading:d,m="trailing"in l?!!l.trailing:m),mg(r,o,{leading:d,maxWait:o,trailing:m})}function gM(r){return cg(r,1)}function _M(r,o){return Ec(ac(o),r)}function vM(){if(!arguments.length)return[];var r=arguments[0];return Re(r)?r:[r]}function yM(r){return Os(r,O)}function bM(r,o){return o=typeof o=="function"?o:s,Os(r,O,o)}function CM(r){return Os(r,g|O)}function wM(r,o){return o=typeof o=="function"?o:s,Os(r,g|O,o)}function EM(r,o){return o==null||rm(r,o,Ft(o))}function Bs(r,o){return r===o||r!==r&&o!==o}var OM=La(Qu),xM=La(function(r,o){return r>=o}),Sn=um(function(){return arguments}())?um:function(r){return yt(r)&&nt.call(r,"callee")&&!Qp.call(r,"callee")},Re=M.isArray,SM=Tp?fs(Tp):kA;function ns(r){return r!=null&&Ka(r.length)&&!Or(r)}function Et(r){return yt(r)&&ns(r)}function DM(r){return r===!0||r===!1||yt(r)&&Jt(r)==ie}var Qr=qT||kc,IM=Ap?fs(Ap):VA;function NM(r){return yt(r)&&r.nodeType===1&&!ui(r)}function TM(r){if(r==null)return!0;if(ns(r)&&(Re(r)||typeof r=="string"||typeof r.splice=="function"||Qr(r)||ro(r)||Sn(r)))return!r.length;var o=Wt(r);if(o==C||o==Q)return!r.size;if(ai(r))return!Yu(r).length;for(var l in r)if(nt.call(r,l))return!1;return!0}function AM(r,o){return ni(r,o)}function MM(r,o,l){l=typeof l=="function"?l:s;var d=l?l(r,o):s;return d===s?ni(r,o,s,l):!!d}function Oc(r){if(!yt(r))return!1;var o=Jt(r);return o==ke||o==pe||typeof r.message=="string"&&typeof r.name=="string"&&!ui(r)}function PM(r){return typeof r=="number"&&Jp(r)}function Or(r){if(!vt(r))return!1;var o=Jt(r);return o==rt||o==Ve||o==R||o==W}function _g(r){return typeof r=="number"&&r==Ue(r)}function Ka(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=ye}function vt(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function yt(r){return r!=null&&typeof r=="object"}var vg=Mp?fs(Mp):FA;function kM(r,o){return r===o||Ju(r,o,pc(o))}function VM(r,o,l){return l=typeof l=="function"?l:s,Ju(r,o,pc(o),l)}function RM(r){return yg(r)&&r!=+r}function FM(r){if(b3(r))throw new Me(a);return cm(r)}function UM(r){return r===null}function LM(r){return r==null}function yg(r){return typeof r=="number"||yt(r)&&Jt(r)==E}function ui(r){if(!yt(r)||Jt(r)!=U)return!1;var o=Ca(r);if(o===null)return!0;var l=nt.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&_a.call(l)==VT}var xc=Pp?fs(Pp):UA;function BM(r){return _g(r)&&r>=-ye&&r<=ye}var bg=kp?fs(kp):LA;function Qa(r){return typeof r=="string"||!Re(r)&&yt(r)&&Jt(r)==ee}function ps(r){return typeof r=="symbol"||yt(r)&&Jt(r)==z}var ro=Vp?fs(Vp):BA;function qM(r){return r===s}function HM(r){return yt(r)&&Wt(r)==se}function $M(r){return yt(r)&&Jt(r)==me}var WM=La(Xu),jM=La(function(r,o){return r<=o});function Cg(r){if(!r)return[];if(ns(r))return Qa(r)?Us(r):rs(r);if(Zo&&r[Zo])return ET(r[Zo]());var o=Wt(r),l=o==C?Bu:o==Q?pa:no;return l(r)}function xr(r){if(!r)return r===0?r:0;if(r=Ds(r),r===K||r===-K){var o=r<0?-1:1;return o*be}return r===r?r:0}function Ue(r){var o=xr(r),l=o%1;return o===o?l?o-l:o:0}function wg(r){return r?wn(Ue(r),0,ct):0}function Ds(r){if(typeof r=="number")return r;if(ps(r))return wt;if(vt(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=vt(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=qp(r);var l=SN.test(r);return l||IN.test(r)?iT(r.slice(2),l?2:8):xN.test(r)?wt:+r}function Eg(r){return rr(r,os(r))}function zM(r){return r?wn(Ue(r),-ye,ye):r===0?r:0}function st(r){return r==null?"":hs(r)}var GM=eo(function(r,o){if(ai(o)||ns(o)){rr(o,Ft(o),r);return}for(var l in o)nt.call(o,l)&&ti(r,l,o[l])}),Og=eo(function(r,o){rr(o,os(o),r)}),Za=eo(function(r,o,l,d){rr(o,os(o),r,d)}),KM=eo(function(r,o,l,d){rr(o,Ft(o),r,d)}),QM=wr(zu);function ZM(r,o){var l=Xn(r);return o==null?l:sm(l,o)}var JM=He(function(r,o){r=ot(r);var l=-1,d=o.length,m=d>2?o[2]:s;for(m&&Yt(o[0],o[1],m)&&(d=1);++l<d;)for(var b=o[l],w=os(b),S=-1,N=w.length;++S<N;){var L=w[S],B=r[L];(B===s||Bs(B,Zn[L])&&!nt.call(r,L))&&(r[L]=b[L])}return r}),YM=He(function(r){return r.push(s,qm),ds(xg,s,r)});function XM(r,o){return Fp(r,xe(o,3),sr)}function e5(r,o){return Fp(r,xe(o,3),Ku)}function t5(r,o){return r==null?r:Gu(r,xe(o,3),os)}function s5(r,o){return r==null?r:am(r,xe(o,3),os)}function r5(r,o){return r&&sr(r,xe(o,3))}function n5(r,o){return r&&Ku(r,xe(o,3))}function o5(r){return r==null?[]:Aa(r,Ft(r))}function i5(r){return r==null?[]:Aa(r,os(r))}function Sc(r,o,l){var d=r==null?s:En(r,o);return d===s?l:d}function a5(r,o){return r!=null&&Wm(r,o,TA)}function Dc(r,o){return r!=null&&Wm(r,o,AA)}var l5=Rm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=va.call(o)),r[o]=l},Nc(is)),u5=Rm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=va.call(o)),nt.call(r,o)?r[o].push(l):r[o]=[l]},xe),c5=He(ri);function Ft(r){return ns(r)?em(r):Yu(r)}function os(r){return ns(r)?em(r,!0):qA(r)}function d5(r,o){var l={};return o=xe(o,3),sr(r,function(d,m,b){br(l,o(d,m,b),d)}),l}function f5(r,o){var l={};return o=xe(o,3),sr(r,function(d,m,b){br(l,m,o(d,m,b))}),l}var h5=eo(function(r,o,l){Ma(r,o,l)}),xg=eo(function(r,o,l,d){Ma(r,o,l,d)}),p5=wr(function(r,o){var l={};if(r==null)return l;var d=!1;o=mt(o,function(b){return b=Gr(b,r),d||(d=b.length>1),b}),rr(r,fc(r),l),d&&(l=Os(l,g|v|O,l3));for(var m=o.length;m--;)nc(l,o[m]);return l});function m5(r,o){return Sg(r,Ga(xe(o)))}var g5=wr(function(r,o){return r==null?{}:$A(r,o)});function Sg(r,o){if(r==null)return{};var l=mt(fc(r),function(d){return[d]});return o=xe(o),_m(r,l,function(d,m){return o(d,m[0])})}function _5(r,o,l){o=Gr(o,r);var d=-1,m=o.length;for(m||(m=1,r=s);++d<m;){var b=r==null?s:r[nr(o[d])];b===s&&(d=m,b=l),r=Or(b)?b.call(r):b}return r}function v5(r,o,l){return r==null?r:oi(r,o,l)}function y5(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:oi(r,o,l,d)}var Dg=Lm(Ft),Ig=Lm(os);function b5(r,o,l){var d=Re(r),m=d||Qr(r)||ro(r);if(o=xe(o,4),l==null){var b=r&&r.constructor;m?l=d?new b:[]:vt(r)?l=Or(b)?Xn(Ca(r)):{}:l={}}return(m?Cs:sr)(r,function(w,S,N){return o(l,w,S,N)}),l}function C5(r,o){return r==null?!0:nc(r,o)}function w5(r,o,l){return r==null?r:wm(r,o,ac(l))}function E5(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:wm(r,o,ac(l),d)}function no(r){return r==null?[]:Lu(r,Ft(r))}function O5(r){return r==null?[]:Lu(r,os(r))}function x5(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Ds(l),l=l===l?l:0),o!==s&&(o=Ds(o),o=o===o?o:0),wn(Ds(r),o,l)}function S5(r,o,l){return o=xr(o),l===s?(l=o,o=0):l=xr(l),r=Ds(r),MA(r,o,l)}function D5(r,o,l){if(l&&typeof l!="boolean"&&Yt(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=xr(r),o===s?(o=r,r=0):o=xr(o)),r>o){var d=r;r=o,o=d}if(l||r%1||o%1){var m=Yp();return $t(r+m*(o-r+oT("1e-"+((m+"").length-1))),o)}return tc(r,o)}var I5=to(function(r,o,l){return o=o.toLowerCase(),r+(l?Ng(o):o)});function Ng(r){return Ic(st(r).toLowerCase())}function Tg(r){return r=st(r),r&&r.replace(TN,vT).replace(QN,"")}function N5(r,o,l){r=st(r),o=hs(o);var d=r.length;l=l===s?d:wn(Ue(l),0,d);var m=l;return l-=o.length,l>=0&&r.slice(l,m)==o}function T5(r){return r=st(r),r&&cN.test(r)?r.replace(ap,yT):r}function A5(r){return r=st(r),r&&gN.test(r)?r.replace(Eu,"\\$&"):r}var M5=to(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),P5=to(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),k5=Pm("toLowerCase");function V5(r,o,l){r=st(r),o=Ue(o);var d=o?Kn(r):0;if(!o||d>=o)return r;var m=(o-d)/2;return Ua(xa(m),l)+r+Ua(Oa(m),l)}function R5(r,o,l){r=st(r),o=Ue(o);var d=o?Kn(r):0;return o&&d<o?r+Ua(o-d,l):r}function F5(r,o,l){r=st(r),o=Ue(o);var d=o?Kn(r):0;return o&&d<o?Ua(o-d,l)+r:r}function U5(r,o,l){return l||o==null?o=0:o&&(o=+o),jT(st(r).replace(Ou,""),o||0)}function L5(r,o,l){return(l?Yt(r,o,l):o===s)?o=1:o=Ue(o),sc(st(r),o)}function B5(){var r=arguments,o=st(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var q5=to(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function H5(r,o,l){return l&&typeof l!="number"&&Yt(r,o,l)&&(o=l=s),l=l===s?ct:l>>>0,l?(r=st(r),r&&(typeof o=="string"||o!=null&&!xc(o))&&(o=hs(o),!o&&Gn(r))?Kr(Us(r),0,l):r.split(o,l)):[]}var $5=to(function(r,o,l){return r+(l?" ":"")+Ic(o)});function W5(r,o,l){return r=st(r),l=l==null?0:wn(Ue(l),0,r.length),o=hs(o),r.slice(l,l+o.length)==o}function j5(r,o,l){var d=y.templateSettings;l&&Yt(r,o,l)&&(o=s),r=st(r),o=Za({},o,d,Bm);var m=Za({},o.imports,d.imports,Bm),b=Ft(m),w=Lu(m,b),S,N,L=0,B=o.interpolate||ua,j="__p += '",le=qu((o.escape||ua).source+"|"+B.source+"|"+(B===lp?ON:ua).source+"|"+(o.evaluate||ua).source+"|$","g"),ge="//# sourceURL="+(nt.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++eT+"]")+`
`;r.replace(le,function(Ie,$e,Ge,ms,Xt,gs){return Ge||(Ge=ms),j+=r.slice(L,gs).replace(AN,bT),$e&&(S=!0,j+=`' +
__e(`+$e+`) +
'`),Xt&&(N=!0,j+=`';
`+Xt+`;
__p += '`),Ge&&(j+=`' +
((__t = (`+Ge+`)) == null ? '' : __t) +
'`),L=gs+Ie.length,Ie}),j+=`';
`;var De=nt.call(o,"variable")&&o.variable;if(!De)j=`with (obj) {
`+j+`
}
`;else if(wN.test(De))throw new Me(c);j=(N?j.replace(ys,""):j).replace(la,"$1").replace(lN,"$1;"),j="function("+(De||"obj")+`) {
`+(De?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(S?", __e = _.escape":"")+(N?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+j+`return __p
}`;var Be=Mg(function(){return Xe(b,ge+"return "+j).apply(s,w)});if(Be.source=j,Oc(Be))throw Be;return Be}function z5(r){return st(r).toLowerCase()}function G5(r){return st(r).toUpperCase()}function K5(r,o,l){if(r=st(r),r&&(l||o===s))return qp(r);if(!r||!(o=hs(o)))return r;var d=Us(r),m=Us(o),b=Hp(d,m),w=$p(d,m)+1;return Kr(d,b,w).join("")}function Q5(r,o,l){if(r=st(r),r&&(l||o===s))return r.slice(0,jp(r)+1);if(!r||!(o=hs(o)))return r;var d=Us(r),m=$p(d,Us(o))+1;return Kr(d,0,m).join("")}function Z5(r,o,l){if(r=st(r),r&&(l||o===s))return r.replace(Ou,"");if(!r||!(o=hs(o)))return r;var d=Us(r),m=Hp(d,Us(o));return Kr(d,m).join("")}function J5(r,o){var l=A,d=we;if(vt(o)){var m="separator"in o?o.separator:m;l="length"in o?Ue(o.length):l,d="omission"in o?hs(o.omission):d}r=st(r);var b=r.length;if(Gn(r)){var w=Us(r);b=w.length}if(l>=b)return r;var S=l-Kn(d);if(S<1)return d;var N=w?Kr(w,0,S).join(""):r.slice(0,S);if(m===s)return N+d;if(w&&(S+=N.length-S),xc(m)){if(r.slice(S).search(m)){var L,B=N;for(m.global||(m=qu(m.source,st(up.exec(m))+"g")),m.lastIndex=0;L=m.exec(B);)var j=L.index;N=N.slice(0,j===s?S:j)}}else if(r.indexOf(hs(m),S)!=S){var le=N.lastIndexOf(m);le>-1&&(N=N.slice(0,le))}return N+d}function Y5(r){return r=st(r),r&&uN.test(r)?r.replace(ip,DT):r}var X5=to(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),Ic=Pm("toUpperCase");function Ag(r,o,l){return r=st(r),o=l?s:o,o===s?wT(r)?TT(r):hT(r):r.match(o)||[]}var Mg=He(function(r,o){try{return ds(r,s,o)}catch(l){return Oc(l)?l:new Me(l)}}),eP=wr(function(r,o){return Cs(o,function(l){l=nr(l),br(r,l,wc(r[l],r))}),r});function tP(r){var o=r==null?0:r.length,l=xe();return r=o?mt(r,function(d){if(typeof d[1]!="function")throw new ws(u);return[l(d[0]),d[1]]}):[],He(function(d){for(var m=-1;++m<o;){var b=r[m];if(ds(b[0],this,d))return ds(b[1],this,d)}})}function sP(r){return DA(Os(r,g))}function Nc(r){return function(){return r}}function rP(r,o){return r==null||r!==r?o:r}var nP=Vm(),oP=Vm(!0);function is(r){return r}function Tc(r){return dm(typeof r=="function"?r:Os(r,g))}function iP(r){return hm(Os(r,g))}function aP(r,o){return pm(r,Os(o,g))}var lP=He(function(r,o){return function(l){return ri(l,r,o)}}),uP=He(function(r,o){return function(l){return ri(r,l,o)}});function Ac(r,o,l){var d=Ft(o),m=Aa(o,d);l==null&&!(vt(o)&&(m.length||!d.length))&&(l=o,o=r,r=this,m=Aa(o,Ft(o)));var b=!(vt(l)&&"chain"in l)||!!l.chain,w=Or(r);return Cs(m,function(S){var N=o[S];r[S]=N,w&&(r.prototype[S]=function(){var L=this.__chain__;if(b||L){var B=r(this.__wrapped__),j=B.__actions__=rs(this.__actions__);return j.push({func:N,args:arguments,thisArg:r}),B.__chain__=L,B}return N.apply(r,Hr([this.value()],arguments))})}),r}function cP(){return Bt._===this&&(Bt._=RT),this}function Mc(){}function dP(r){return r=Ue(r),He(function(o){return mm(o,r)})}var fP=uc(mt),hP=uc(Rp),pP=uc(ku);function Pg(r){return gc(r)?Vu(nr(r)):WA(r)}function mP(r){return function(o){return r==null?s:En(r,o)}}var gP=Fm(),_P=Fm(!0);function Pc(){return[]}function kc(){return!1}function vP(){return{}}function yP(){return""}function bP(){return!0}function CP(r,o){if(r=Ue(r),r<1||r>ye)return[];var l=ct,d=$t(r,ct);o=xe(o),r-=ct;for(var m=Uu(d,o);++l<r;)o(l);return m}function wP(r){return Re(r)?mt(r,nr):ps(r)?[r]:rs(Xm(st(r)))}function EP(r){var o=++kT;return st(r)+o}var OP=Fa(function(r,o){return r+o},0),xP=cc("ceil"),SP=Fa(function(r,o){return r/o},1),DP=cc("floor");function IP(r){return r&&r.length?Ta(r,is,Qu):s}function NP(r,o){return r&&r.length?Ta(r,xe(o,2),Qu):s}function TP(r){return Lp(r,is)}function AP(r,o){return Lp(r,xe(o,2))}function MP(r){return r&&r.length?Ta(r,is,Xu):s}function PP(r,o){return r&&r.length?Ta(r,xe(o,2),Xu):s}var kP=Fa(function(r,o){return r*o},1),VP=cc("round"),RP=Fa(function(r,o){return r-o},0);function FP(r){return r&&r.length?Fu(r,is):0}function UP(r,o){return r&&r.length?Fu(r,xe(o,2)):0}return y.after=iM,y.ary=cg,y.assign=GM,y.assignIn=Og,y.assignInWith=Za,y.assignWith=KM,y.at=QM,y.before=dg,y.bind=wc,y.bindAll=eP,y.bindKey=fg,y.castArray=vM,y.chain=ag,y.chunk=D3,y.compact=I3,y.concat=N3,y.cond=tP,y.conforms=sP,y.constant=Nc,y.countBy=F4,y.create=ZM,y.curry=hg,y.curryRight=pg,y.debounce=mg,y.defaults=JM,y.defaultsDeep=YM,y.defer=aM,y.delay=lM,y.difference=T3,y.differenceBy=A3,y.differenceWith=M3,y.drop=P3,y.dropRight=k3,y.dropRightWhile=V3,y.dropWhile=R3,y.fill=F3,y.filter=L4,y.flatMap=H4,y.flatMapDeep=$4,y.flatMapDepth=W4,y.flatten=rg,y.flattenDeep=U3,y.flattenDepth=L3,y.flip=uM,y.flow=nP,y.flowRight=oP,y.fromPairs=B3,y.functions=o5,y.functionsIn=i5,y.groupBy=j4,y.initial=H3,y.intersection=$3,y.intersectionBy=W3,y.intersectionWith=j3,y.invert=l5,y.invertBy=u5,y.invokeMap=G4,y.iteratee=Tc,y.keyBy=K4,y.keys=Ft,y.keysIn=os,y.map=Wa,y.mapKeys=d5,y.mapValues=f5,y.matches=iP,y.matchesProperty=aP,y.memoize=za,y.merge=h5,y.mergeWith=xg,y.method=lP,y.methodOf=uP,y.mixin=Ac,y.negate=Ga,y.nthArg=dP,y.omit=p5,y.omitBy=m5,y.once=cM,y.orderBy=Q4,y.over=fP,y.overArgs=dM,y.overEvery=hP,y.overSome=pP,y.partial=Ec,y.partialRight=gg,y.partition=Z4,y.pick=g5,y.pickBy=Sg,y.property=Pg,y.propertyOf=mP,y.pull=Q3,y.pullAll=og,y.pullAllBy=Z3,y.pullAllWith=J3,y.pullAt=Y3,y.range=gP,y.rangeRight=_P,y.rearg=fM,y.reject=X4,y.remove=X3,y.rest=hM,y.reverse=bc,y.sampleSize=tM,y.set=v5,y.setWith=y5,y.shuffle=sM,y.slice=e4,y.sortBy=oM,y.sortedUniq=a4,y.sortedUniqBy=l4,y.split=H5,y.spread=pM,y.tail=u4,y.take=c4,y.takeRight=d4,y.takeRightWhile=f4,y.takeWhile=h4,y.tap=I4,y.throttle=mM,y.thru=$a,y.toArray=Cg,y.toPairs=Dg,y.toPairsIn=Ig,y.toPath=wP,y.toPlainObject=Eg,y.transform=b5,y.unary=gM,y.union=p4,y.unionBy=m4,y.unionWith=g4,y.uniq=_4,y.uniqBy=v4,y.uniqWith=y4,y.unset=C5,y.unzip=Cc,y.unzipWith=ig,y.update=w5,y.updateWith=E5,y.values=no,y.valuesIn=O5,y.without=b4,y.words=Ag,y.wrap=_M,y.xor=C4,y.xorBy=w4,y.xorWith=E4,y.zip=O4,y.zipObject=x4,y.zipObjectDeep=S4,y.zipWith=D4,y.entries=Dg,y.entriesIn=Ig,y.extend=Og,y.extendWith=Za,Ac(y,y),y.add=OP,y.attempt=Mg,y.camelCase=I5,y.capitalize=Ng,y.ceil=xP,y.clamp=x5,y.clone=yM,y.cloneDeep=CM,y.cloneDeepWith=wM,y.cloneWith=bM,y.conformsTo=EM,y.deburr=Tg,y.defaultTo=rP,y.divide=SP,y.endsWith=N5,y.eq=Bs,y.escape=T5,y.escapeRegExp=A5,y.every=U4,y.find=B4,y.findIndex=tg,y.findKey=XM,y.findLast=q4,y.findLastIndex=sg,y.findLastKey=e5,y.floor=DP,y.forEach=lg,y.forEachRight=ug,y.forIn=t5,y.forInRight=s5,y.forOwn=r5,y.forOwnRight=n5,y.get=Sc,y.gt=OM,y.gte=xM,y.has=a5,y.hasIn=Dc,y.head=ng,y.identity=is,y.includes=z4,y.indexOf=q3,y.inRange=S5,y.invoke=c5,y.isArguments=Sn,y.isArray=Re,y.isArrayBuffer=SM,y.isArrayLike=ns,y.isArrayLikeObject=Et,y.isBoolean=DM,y.isBuffer=Qr,y.isDate=IM,y.isElement=NM,y.isEmpty=TM,y.isEqual=AM,y.isEqualWith=MM,y.isError=Oc,y.isFinite=PM,y.isFunction=Or,y.isInteger=_g,y.isLength=Ka,y.isMap=vg,y.isMatch=kM,y.isMatchWith=VM,y.isNaN=RM,y.isNative=FM,y.isNil=LM,y.isNull=UM,y.isNumber=yg,y.isObject=vt,y.isObjectLike=yt,y.isPlainObject=ui,y.isRegExp=xc,y.isSafeInteger=BM,y.isSet=bg,y.isString=Qa,y.isSymbol=ps,y.isTypedArray=ro,y.isUndefined=qM,y.isWeakMap=HM,y.isWeakSet=$M,y.join=z3,y.kebabCase=M5,y.last=Ss,y.lastIndexOf=G3,y.lowerCase=P5,y.lowerFirst=k5,y.lt=WM,y.lte=jM,y.max=IP,y.maxBy=NP,y.mean=TP,y.meanBy=AP,y.min=MP,y.minBy=PP,y.stubArray=Pc,y.stubFalse=kc,y.stubObject=vP,y.stubString=yP,y.stubTrue=bP,y.multiply=kP,y.nth=K3,y.noConflict=cP,y.noop=Mc,y.now=ja,y.pad=V5,y.padEnd=R5,y.padStart=F5,y.parseInt=U5,y.random=D5,y.reduce=J4,y.reduceRight=Y4,y.repeat=L5,y.replace=B5,y.result=_5,y.round=VP,y.runInContext=I,y.sample=eM,y.size=rM,y.snakeCase=q5,y.some=nM,y.sortedIndex=t4,y.sortedIndexBy=s4,y.sortedIndexOf=r4,y.sortedLastIndex=n4,y.sortedLastIndexBy=o4,y.sortedLastIndexOf=i4,y.startCase=$5,y.startsWith=W5,y.subtract=RP,y.sum=FP,y.sumBy=UP,y.template=j5,y.times=CP,y.toFinite=xr,y.toInteger=Ue,y.toLength=wg,y.toLower=z5,y.toNumber=Ds,y.toSafeInteger=zM,y.toString=st,y.toUpper=G5,y.trim=K5,y.trimEnd=Q5,y.trimStart=Z5,y.truncate=J5,y.unescape=Y5,y.uniqueId=EP,y.upperCase=X5,y.upperFirst=Ic,y.each=lg,y.eachRight=ug,y.first=ng,Ac(y,function(){var r={};return sr(y,function(o,l){nt.call(y.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),y.VERSION=i,Cs(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){y[r].placeholder=y}),Cs(["drop","take"],function(r,o){je.prototype[r]=function(l){l=l===s?1:Mt(Ue(l),0);var d=this.__filtered__&&!o?new je(this):this.clone();return d.__filtered__?d.__takeCount__=$t(l,d.__takeCount__):d.__views__.push({size:$t(l,ct),type:r+(d.__dir__<0?"Right":"")}),d},je.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Cs(["filter","map","takeWhile"],function(r,o){var l=o+1,d=l==_t||l==ut;je.prototype[r]=function(m){var b=this.clone();return b.__iteratees__.push({iteratee:xe(m,3),type:l}),b.__filtered__=b.__filtered__||d,b}}),Cs(["head","last"],function(r,o){var l="take"+(o?"Right":"");je.prototype[r]=function(){return this[l](1).value()[0]}}),Cs(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");je.prototype[r]=function(){return this.__filtered__?new je(this):this[l](1)}}),je.prototype.compact=function(){return this.filter(is)},je.prototype.find=function(r){return this.filter(r).head()},je.prototype.findLast=function(r){return this.reverse().find(r)},je.prototype.invokeMap=He(function(r,o){return typeof r=="function"?new je(this):this.map(function(l){return ri(l,r,o)})}),je.prototype.reject=function(r){return this.filter(Ga(xe(r)))},je.prototype.slice=function(r,o){r=Ue(r);var l=this;return l.__filtered__&&(r>0||o<0)?new je(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=Ue(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},je.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},je.prototype.toArray=function(){return this.take(ct)},sr(je.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),d=/^(?:head|last)$/.test(o),m=y[d?"take"+(o=="last"?"Right":""):o],b=d||/^find/.test(o);m&&(y.prototype[o]=function(){var w=this.__wrapped__,S=d?[1]:arguments,N=w instanceof je,L=S[0],B=N||Re(w),j=function($e){var Ge=m.apply(y,Hr([$e],S));return d&&le?Ge[0]:Ge};B&&l&&typeof L=="function"&&L.length!=1&&(N=B=!1);var le=this.__chain__,ge=!!this.__actions__.length,De=b&&!le,Be=N&&!ge;if(!b&&B){w=Be?w:new je(this);var Ie=r.apply(w,S);return Ie.__actions__.push({func:$a,args:[j],thisArg:s}),new Es(Ie,le)}return De&&Be?r.apply(this,S):(Ie=this.thru(j),De?d?Ie.value()[0]:Ie.value():Ie)})}),Cs(["pop","push","shift","sort","splice","unshift"],function(r){var o=ma[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);y.prototype[r]=function(){var m=arguments;if(d&&!this.__chain__){var b=this.value();return o.apply(Re(b)?b:[],m)}return this[l](function(w){return o.apply(Re(w)?w:[],m)})}}),sr(je.prototype,function(r,o){var l=y[o];if(l){var d=l.name+"";nt.call(Yn,d)||(Yn[d]=[]),Yn[d].push({name:o,func:l})}}),Yn[Ra(s,J).name]=[{name:"wrapper",func:s}],je.prototype.clone=YT,je.prototype.reverse=XT,je.prototype.value=eA,y.prototype.at=N4,y.prototype.chain=T4,y.prototype.commit=A4,y.prototype.next=M4,y.prototype.plant=k4,y.prototype.reverse=V4,y.prototype.toJSON=y.prototype.valueOf=y.prototype.value=R4,y.prototype.first=y.prototype.head,Zo&&(y.prototype[Zo]=P4),y},Qn=AT();vn?((vn.exports=Qn)._=Qn,Tu._=Qn):Bt._=Qn}).call(Ho)}(sa,sa.exports);var Ys=sa.exports;const Le=async(e,t)=>{const s={methodname:e,args:Object.assign({},t)};try{return await Rg.call([s])[0]}catch(i){throw Fg.exception(i),i}};async function Zb(e={}){try{return await Le("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw t}}async function Jb(e){try{return await Le("local_offermanager_get",{id:e})}catch(t){throw t}}async function Xh(e){try{return await Le("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",status:e.status||0,audienceids:e.audiences||[]})}catch(t){throw t}}async function Yb(e){try{return await Le("local_offermanager_delete",{id:e})}catch(t){throw t}}async function Xb(){try{return await Le("local_offermanager_get_type_options",{})}catch(e){throw e}}async function e0(e,t){try{return await Le("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw s}}async function t0(e,t,s){try{return await Le("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw i}}async function s0(e){try{return(await Le("local_offermanager_get_audiences",{offerid:0})).all_audiences.filter(i=>i.name.toLowerCase().includes(e.toLowerCase())).map(i=>({id:i.id,name:i.name}))}catch(t){throw t}}async function r0(e,t){try{return await Le("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw s}}async function pu(e="",t=0){try{return await Le("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw s}}async function n0(e,t,s="",i=1,n=20){try{return await Le("local_offermanager_fetch_potential_courses",{offerid:e,categoryid:t,search_string:s||"",page:i,per_page:n,exclude_courseids:[]})}catch(a){throw a}}async function o0(e,t,s="",i=[],n=!1){try{return await Le("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:s,exclude_courseids:i||[],only_active:n})}catch(a){throw a}}async function i0(e,t){try{return await Le("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw s}}async function ep(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="courseClassCount"&&(t.sortBy="class_counter"),await Le("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw s}}async function a0(e){try{return await Le("local_offermanager_add_class",e)}catch(t){throw console.error("Erro ao criar turma:",t),t}}async function mu(e){try{return await Le("local_offermanager_get_class",{offerclassid:e})}catch(t){throw t}}async function l0(e){try{return await Le("local_offermanager_get_course",{offercourseid:e})}catch(t){throw t}}async function u0(e){try{return await Le("local_offermanager_get_classes",{offercourseid:e})}catch(t){throw console.error("Error fetching:",t),t}}async function c0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","enablehirearchyrestriction","hirearchyrestrictiondivisions","hirearchyrestrictionsectors","hirearchyrestrictiongroups","hirearchyrestrictiondealerships","modality","maxusersdealership"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return t.forEach(n=>{n in e.optional_fields&&(s.optional_fields[n]=e.optional_fields[n])}),"enrol"in s&&delete s.enrol,await Le("local_offermanager_update_class",s)}catch(t){throw t}}async function d0(e){try{return await Le("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw t}}async function f0(e,t=0,s="",i=[]){try{return await Le("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw n}}async function h0(){try{return await Le("local_offermanager_get_situation_list",{})}catch(e){throw e}}async function p0(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Le("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw s}}async function m0(e){try{return await Le("local_offermanager_get_duplication_courses",{offerclassid:e})}catch(t){throw t}}async function gu(e){try{return await Le("local_offermanager_get_course_roles",{offercourseid:e})}catch(t){throw t}}async function g0(e=!0){try{return await Le("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw t}}async function _0(e,t){try{return await Le("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw s}}async function v0(){try{return await Le("local_offermanager_get_hierarchy_divisions",{})}catch(e){throw e}}async function y0(e){try{return await Le("local_offermanager_get_hierarchy_sectors",{divisionids:e})}catch(t){throw t}}async function b0(e){try{return await Le("local_offermanager_get_hierarchy_groups",{sectorids:e})}catch(t){throw t}}async function C0(e){try{return await Le("local_offermanager_get_hierarchy_dealerships",{groupids:e})}catch(t){throw t}}const $P="",w0={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},tableClass:String,theadClass:String,tbodyClass:String},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},E0={class:"table-responsive"},O0=["data-value"],x0=["onClick"],S0=["data-column"];function D0(e,t,s,i,n,a){return x(),D("div",E0,[f("table",{class:ce(["table",s.tableClass])},[f("thead",{class:ce(s.theadClass)},[f("tr",null,[(x(!0),D(Ae,null,lt(s.headers,u=>(x(),D("th",{key:u.value,class:ce([u.value,{"text-right":u.align==="right"}]),style:as(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Pt(e.$slots,"header-select",{key:0},()=>[We(q(u.text),1)],!0):(x(),D(Ae,{key:1},[We(q(u.text)+" ",1),u.sortable?(x(),D("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[f("i",{class:ce(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,x0)):G("",!0)],64))],14,O0))),128))])],2),f("tbody",{class:ce(s.tbodyClass)},[(x(!0),D(Ae,null,lt(s.items,u=>(x(),D("tr",{key:u.id},[(x(!0),D(Ae,null,lt(s.headers,c=>(x(),D("td",{key:c.value,class:ce({"text-right":c.align==="right"}),"data-column":c.value},[Pt(e.$slots,"item-"+c.value,{item:u},()=>[We(q(u[c.value]),1)],!0)],10,S0))),128))]))),128))],2)],2)])}const mn=Pe(w0,[["render",D0],["__scopeId","data-v-80e67201"]]),WP="",I0={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},N0={class:"select-wrapper"},T0=["value","disabled"],A0=["value"],M0={key:"loading",value:""},P0={key:1,class:"error-message"};function k0(e,t,s,i,n,a){return x(),D("div",{ref:"selectContainer",class:"custom-select-container",style:as(a.customWidth)},[s.label?(x(),D("div",{key:0,class:ce(["select-label",{disabled:s.disabled}])},q(s.label),3)):G("",!0),f("div",N0,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:ce(["form-control",{error:s.hasError}]),disabled:s.disabled},[s.isLoading?G("",!0):(x(!0),D(Ae,{key:0},lt(s.options,u=>(x(),D("option",{key:u.value,value:u.value},q(u.label),9,A0))),128)),s.isLoading?(x(),D("option",M0,"Carregando...")):G("",!0)],42,T0)]),s.hasError&&s.errorMessage?(x(),D("div",P0,q(s.errorMessage),1)):G("",!0)],4)}const Xs=Pe(I0,[["render",k0],["__scopeId","data-v-42a8eff7"]]),jP="",V0={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},id:{type:String,required:!1,default:"custom-input-"+Math.random().toString(36).substring(2,9)},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},R0={key:0,class:"input-label"},F0=["type","placeholder","value","disabled","min","max","id"],U0={key:0,class:"search-icon"},L0={key:2,class:"error-message"};function B0(e,t,s,i,n,a){return x(),D("div",{class:"custom-input-container",style:as(a.customWidth)},[s.label?(x(),D("div",R0,q(s.label),1)):G("",!0),f("div",{class:ce(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[f("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:ce(["form-control custom-input",{"is-invalid":s.hasError}]),min:a.isNumberType?0:null,max:s.max,id:s.id},null,42,F0),s.hasSearchIcon?(x(),D("div",U0,t[2]||(t[2]=[f("i",{class:"fas fa-search"},null,-1)]))):G("",!0),a.isDateType?(x(),D("div",{key:1,class:ce(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[f("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):G("",!0),s.hasError&&s.errorMessage?(x(),D("div",L0,q(s.errorMessage),1)):G("",!0)],2)],4)}const Hn=Pe(V0,[["render",B0],["__scopeId","data-v-6f10137a"]]),zP="",q0={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},H0=["data-content","aria-label"],$0=["title","aria-label"];function W0(e,t,s,i,n,a){return x(),D("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[f("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,$0)],8,H0)}const ra=Pe(q0,[["render",W0],["__scopeId","data-v-6eb219ea"]]),GP="",j0={name:"CustomCheckbox",components:{HelpIcon:ra},props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},help:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1},confirmBeforeChange:{type:Boolean,default:!1}},emits:["update:modelValue","request-change"],methods:{handleClick(e){const t=!this.modelValue;this.confirmBeforeChange?this.$emit("request-change",t):this.$emit("update:modelValue",t)}}},z0=["id","checked","disabled"],G0=["for"];function K0(e,t,s,i,n,a){const u=$("HelpIcon");return x(),D("div",{class:ce(["checkbox-container",{disabled:s.disabled}])},[(x(),D("input",{type:"checkbox",id:s.id,key:s.modelValue,checked:s.modelValue,onClick:t[0]||(t[0]=kt((...c)=>a.handleClick&&a.handleClick(...c),["prevent"])),class:"custom-checkbox",disabled:s.disabled},null,8,z0)),f("label",{for:s.id,class:ce(["checkbox-label",{disabled:s.disabled}])},[Pt(e.$slots,"default",{},()=>[We(q(s.label),1)],!0)],10,G0),s.help?(x(),ht(u,{key:0,title:`Ajuda com ${s.label.toLowerCase()}`,text:s.help},null,8,["title","text"])):G("",!0)],2)}const $o=Pe(j0,[["render",K0],["__scopeId","data-v-3ece3f9b"]]),KP="",Q0={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1}},emits:["click"]},Z0=["disabled"],J0={key:1,class:"spinner-border spinner-border-sm"},Y0={key:2};function X0(e,t,s,i,n,a){return x(),D("button",{class:ce(["btn custom-button",[`btn-${s.variant}`]]),disabled:s.disabled||s.isLoading,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(x(),D("i",{key:0,class:ce(s.icon)},null,2)):G("",!0),s.isLoading?(x(),D("i",J0)):G("",!0),s.label?(x(),D("span",Y0,q(s.label),1)):G("",!0),Pt(e.$slots,"default",{},void 0,!0)],10,Z0)}const er=Pe(Q0,[["render",X0],["__scopeId","data-v-482c6327"]]),QP="",eC={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},tC={class:"filter-section"},sC={key:0},rC={class:"filter-content"},nC={key:1,class:"filter-tags"};function oC(e,t,s,i,n,a){return x(),D("div",tC,[s.title?(x(),D("h2",sC,q(s.title),1)):G("",!0),f("div",rC,[Pt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(x(),D("div",nC,[Pt(e.$slots,"tags",{},void 0,!0)])):G("",!0)])}const tp=Pe(eC,[["render",oC],["__scopeId","data-v-1ece8e84"]]),ZP="",iC={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function aC(e,t,s,i,n,a){return x(),D("div",{class:ce(["filter-row",{"filter-row-inline":s.inline}])},[Pt(e.$slots,"default",{},void 0,!0)],2)}const na=Pe(iC,[["render",aC],["__scopeId","data-v-83bdb425"]]),JP="",lC={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},uC={key:0,class:"filter-label"},cC={class:"filter-input"};function dC(e,t,s,i,n,a){return x(),D("div",{class:ce(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(x(),D("div",uC,q(s.label),1)):G("",!0),f("div",cC,[Pt(e.$slots,"default",{},void 0,!0)])],2)}const oa=Pe(lC,[["render",dC],["__scopeId","data-v-d7bf1926"]]),YP="",fC={name:"FilterActions"},hC={class:"filter-actions"};function pC(e,t,s,i,n,a){return x(),D("div",hC,[Pt(e.$slots,"default",{},void 0,!0)])}const sp=Pe(fC,[["render",pC],["__scopeId","data-v-68346c90"]]),XP="",mC={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},gC={key:0};function _C(e,t,s,i,n,a){return x(),ht(Ff,null,{default:Ce(()=>[s.isLoading?(x(),D("div",gC,t[0]||(t[0]=[f("div",{class:"modal-overlay"},null,-1),f("div",{class:"loader-wrapper"},[f("span",{class:"loader",role:"status"},[f("span",{class:"sr-only"},"Carregando...")])],-1)]))):G("",!0)]),_:1})}const Wo=Pe(mC,[["render",_C],["__scopeId","data-v-b3cb5b4c"]]),e8="",vC={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},yC={class:"toast-content"};function bC(e,t,s,i,n,a){return x(),ht(Y_,{to:"body"},[T(Ff,{name:"toast"},{default:Ce(()=>[s.show?(x(),D("div",{key:0,class:ce(["toast",s.type])},[f("div",yC,[f("i",{class:ce(a.icon)},null,2),f("span",null,q(s.message),1)]),f("div",{class:"toast-progress",style:as(a.progressStyle)},null,4)],2)):G("",!0)]),_:1})])}const Br=Pe(vC,[["render",bC],["__scopeId","data-v-4440998c"]]),t8="",CC={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,25,50,100]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},wC={class:"pagination-container mt-3"},EC={class:"pagination-info"},OC=["value"],xC={class:"pagination-text"},SC={class:"pagination-controls"},DC=["disabled"],IC=["onClick"],NC=["disabled"];function TC(e,t,s,i,n,a){return x(),D("div",wC,[f("div",EC,[gt(f("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(x(!0),D(Ae,null,lt(s.perPageOptions,u=>(x(),D("option",{key:u,value:u},q(u),9,OC))),128))],544),[[Yl,a.perPageModel]]),f("span",xC," Mostrando de "+q(a.from)+" até "+q(a.to)+" de "+q(s.total)+" resultados ",1)]),f("div",SC,[f("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[f("i",{class:"fas fa-chevron-left"},null,-1)]),8,DC),(x(!0),D(Ae,null,lt(a.visiblePages,u=>(x(),D("button",{key:u,class:ce(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},q(u),11,IC))),128)),f("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[f("i",{class:"fas fa-chevron-right"},null,-1)]),8,NC)])])}const gn=Pe(CC,[["render",TC],["__scopeId","data-v-b3aa038d"]]),s8="",AC={name:"PageHeader",props:{title:{type:String,required:!0}}},MC={class:"page-header"},PC={class:"header-actions"};function kC(e,t,s,i,n,a){return x(),D("div",MC,[f("h2",null,q(s.title),1),f("div",PC,[Pt(e.$slots,"actions",{},void 0,!0)])])}const $n=Pe(AC,[["render",kC],["__scopeId","data-v-5d6d687f"]]),r8="",VC={name:"Modal",components:{CustomButton:er},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},RC={class:"modal-body"},FC={key:0,class:"modal-footer"},UC={key:1,class:"modal-footer"};function LC(e,t,s,i,n,a){const u=$("custom-button");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:ce(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=kt(()=>{},["stop"]))},[f("div",RC,[Pt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(x(),D("div",FC,[Pt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(x(),D("div",UC,[T(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),T(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):G("",!0)],2)])):G("",!0)}const BC=Pe(VC,[["render",LC],["__scopeId","data-v-784205f2"]]),n8="",qC={name:"ConfirmationModal",components:{Modal:BC},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)},size:{type:String,default:"sm"}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},HC={key:0,class:"icon-container"},$C={class:"modal-custom-title"},WC={key:1,class:"message-list"},jC={key:0,class:"list-title"},zC={key:2,class:"message"},GC={class:"modal-custom-footer"},KC=["disabled"];function QC(e,t,s,i,n,a){const u=$("modal");return x(),ht(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:s.size,"show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Ce(()=>[f("div",{class:ce(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(x(),D("div",HC,[f("i",{class:ce(a.iconClass)},null,2)])):G("",!0),f("h3",$C,q(s.title),1),a.hasListContent?(x(),D("div",WC,[s.listTitle?(x(),D("p",jC,q(s.listTitle),1)):G("",!0),f("ul",null,[(x(!0),D(Ae,null,lt(s.listItems,(c,h)=>(x(),D("li",{key:h},q(c),1))),128))])])):(x(),D("div",zC,q(s.message),1)),f("div",GC,[f("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},q(s.cancelButtonText),1),f("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},q(s.confirmButtonText),9,KC)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled","size"])}const jo=Pe(qC,[["render",QC],["__scopeId","data-v-2e1eb4fd"]]),_u="data:image/svg+xml;base64,PHN2Zw0KICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9IjIwIg0KICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PSIyMCINCiAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9IjEwIDEwIDE2IDIwIg0KICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD0ibm9uZSINCiAgICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyINCiAgICAgICAgICAgICAgICAgICAgICA+DQogICAgICAgICAgICAgICAgICAgICAgICA8cGF0aA0KICAgICAgICAgICAgICAgICAgICAgICAgICBkPSJNMTguMTg3NSAyNS41ODk3QzIwLjA0IDI1LjU4OTcgMjEuNTQxNyAyNC4wNjI5IDIxLjU0MTcgMjIuMTc5NUMyMS41NDE3IDIwLjI5NiAyMC4wNCAxOC43NjkyIDE4LjE4NzUgMTguNzY5MkMxNi4zMzUxIDE4Ljc2OTIgMTQuODMzNCAyMC4yOTYgMTQuODMzNCAyMi4xNzk1QzE0LjgzMzQgMjQuMDYyOSAxNi4zMzUxIDI1LjU4OTcgMTguMTg3NSAyNS41ODk3WiINCiAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPSJ3aGl0ZSINCiAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlLXdpZHRoPSIyIg0KICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2UtbGluZWNhcD0icm91bmQiDQogICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZS1saW5lam9pbj0icm91bmQiDQogICAgICAgICAgICAgICAgICAgICAgICAvPg0KICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGgNCiAgICAgICAgICAgICAgICAgICAgICAgICAgZD0iTTExIDI5LjQ4NzJMMTUuNzkxNyAyNC42MTU0TTExIDIyLjY2NjdWMTEuOTQ4N0MxMSAxMS40MzE5IDExLjIwMTkgMTAuOTM2MiAxMS41NjE0IDEwLjU3MDhDMTEuOTIwOCAxMC4yMDUzIDEyLjQwODMgMTAgMTIuOTE2NyAxMEgyMC41ODMzTDI2LjMzMzMgMTUuODQ2MlYyNy41Mzg1QzI2LjMzMzMgMjguMDU1MyAyNi4xMzE0IDI4LjU1MSAyNS43NzIgMjguOTE2NEMyNS40MTI1IDI5LjI4MTkgMjQuOTI1IDI5LjQ4NzIgMjQuNDE2NyAyOS40ODcySDE3LjcwODMiDQogICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT0id2hpdGUiDQogICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZS13aWR0aD0iMiINCiAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIg0KICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2UtbGluZWpvaW49InJvdW5kIg0KICAgICAgICAgICAgICAgICAgICAgICAgLz4NCiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz4=",o8="",i8="",ZC={name:"OfferList",components:{CustomTable:mn,CustomSelect:Xs,CustomInput:Hn,CustomCheckbox:$o,CustomButton:er,FilterSection:tp,FilterRow:na,FilterGroup:oa,FilterActions:sp,Pagination:gn,PageHeader:$n,ConfirmationModal:jo,LFLoading:Wo,Toast:Br},data(){return{inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"OFERTA",value:"name",sortable:!0},{text:"STATUS",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Ys.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await Xb();e.types&&(this.typeOptionsEnabled=e.enabled,e.default&&(this.inputFilters.type=e.default),this.typeOptions=e.types.map(t=>({value:t,label:t})))},async loadOffers(){try{this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Zb(e);this.offers=t.offers||[],this.totalOffers=t.total_items||0}catch(e){this.error=e.message}finally{this.loading=!1}},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.$router.push({name:"offer.create"})},navigateToEditOffer(e){this.$router.push({name:"offer.edit",params:{id:e.id.toString()}})},navigateToShowOffer(e){this.$router.push({name:"offer.show",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Yb(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await r0(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},JC={id:"offer-manager-view",class:"offer-manager"},YC={class:"new-offer-container"},XC={key:0,class:"alert alert-danger"},ew={class:"table-container"},tw=["title"],sw={key:0},rw={key:1},nw={class:"action-buttons"},ow=["onClick"],iw=["onClick"],aw=["onClick","disabled","title"],lw={key:0,class:"fas fa-eye text-white"},uw={key:1,class:"fas fa-eye-slash"},cw=["onClick","disabled","title"];function dw(e,t,s,i,n,a){var Ee,X,fe,_e,Ne,ae;const u=$("CustomButton"),c=$("PageHeader"),h=$("CustomInput"),_=$("FilterGroup"),p=$("CustomSelect"),g=$("CustomCheckbox"),v=$("FilterActions"),O=$("FilterRow"),k=$("FilterSection"),V=$("CustomTable"),re=$("Pagination"),J=$("ConfirmationModal"),oe=$("LFLoading"),Y=$("Toast");return x(),D("div",JC,[T(c,{title:"Gerenciamento de ofertas"},{actions:Ce(()=>[f("div",YC,[T(u,{variant:"primary",icon:"fa-solid fa-plus",label:"Adicionar",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),T(k,{title:"FILTRO"},{default:Ce(()=>[T(O,{inline:!0},{default:Ce(()=>[T(_,{label:"Oferta"},{default:Ce(()=>[T(h,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=A=>n.inputFilters.search=A),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),n.typeOptionsEnabled?(x(),ht(_,{key:0,label:"Tipo"},{default:Ce(()=>[T(p,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=A=>n.inputFilters.type=A),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):G("",!0),T(_,{"is-checkbox":!0},{default:Ce(()=>[T(g,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=A=>n.inputFilters.hideInactive=A),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),T(v,null,{default:Ce(()=>[T(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(x(),D("div",XC,[t[7]||(t[7]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),We(" "+q(n.error),1)])):G("",!0),f("div",ew,[T(V,{headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":Ce(({item:A})=>[f("span",{title:A.description},q(A.description.length>50?A.description.slice(0,50)+"...":A.description),9,tw)]),"item-type":Ce(({item:A})=>[We(q(A.type.charAt(0).toUpperCase()+A.type.slice(1)),1)]),"item-status":Ce(({item:A})=>[A.status===1?(x(),D("span",sw,t[8]||(t[8]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--success)","stroke-width":"2"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z",fill:"var(--success)"})],-1),We(" Ativa ")]))):(x(),D("span",rw,t[9]||(t[9]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_572_6021)"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z",fill:"var(--danger)"})]),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--danger)","stroke-width":"2"}),f("defs",null,[f("clipPath",{id:"clip0_572_6021"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"})])])],-1),We(" Inativo ")])))]),"item-actions":Ce(({item:A})=>[f("div",nw,[f("button",{class:"btn-action btn-edit",onClick:we=>a.navigateToShowOffer(A),title:"Visualizar"},t[10]||(t[10]=[f("img",{src:_u},null,-1)]),8,ow),f("button",{class:"btn-action btn-edit",onClick:we=>a.navigateToEditOffer(A),title:"Editar"},t[11]||(t[11]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,iw),f("button",{class:ce(["btn-action",A.status===1?"btn-deactivate":"btn-activate"]),onClick:we=>a.toggleOfferStatus(A),disabled:A.status===0&&!A.can_activate,title:a.getStatusButtonTitle(A)},[A.status===1?(x(),D("i",lw)):(x(),D("i",uw))],10,aw),f("button",{class:"btn-action btn-delete",onClick:we=>a.deleteOffer(A),disabled:!A.can_delete,title:A.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[12]||(t[12]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,cw)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),T(re,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=A=>n.currentPage=A),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=A=>n.perPage=A),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),T(J,{show:n.showDeleteModal,size:"md",title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=A=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),T(J,{show:n.showStatusModal,size:"md",title:((Ee=n.selectedOffer)==null?void 0:Ee.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((X=n.selectedOffer)==null?void 0:X.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((fe=n.selectedOffer)==null?void 0:fe.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((_e=n.selectedOffer)==null?void 0:_e.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ne=n.selectedOffer)==null?void 0:Ne.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ae=n.selectedOffer)==null?void 0:ae.status)===1?"warning":"question",onClose:t[6]||(t[6]=A=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),T(oe,{"is-loading":n.loading},null,8,["is-loading"]),T(Y,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const fw=Pe(ZC,[["render",dw],["__scopeId","data-v-f4a80eb6"]]);async function hw(e={}){try{return await Le("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"})}catch(t){throw t}}async function vu(e={}){try{return await Le("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]})}catch(t){throw t}}async function pw(e={}){try{return await Le("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5})}catch(t){throw t}}async function mw(e,t="",s){try{return await Le("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s})}catch(i){throw i}}async function gw(e={}){try{return await Le("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid})}catch(t){throw t}}async function _w(e={}){try{return await Le("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend})}catch(t){throw t}}async function vw(e){try{return await Le("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e})===!0?e.map(i=>({id:i,operation_status:!0})):[]}catch(t){throw t}}async function yw(e){try{return await Le("local_offermanager_get_roles",{offeruserenrolid:e})}catch(t){throw t}}async function bw(e,t){try{return await Le("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]})}catch(s){throw s}}const a8="",Cw={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},ww={class:"select-wrapper"},Ew=["value","disabled"],Ow=["label"],xw=["value"],Sw={key:1,class:"error-message"};function Dw(e,t,s,i,n,a){return x(),D("div",{ref:"selectContainer",class:"hierarchical-select-container",style:as(a.customWidth)},[s.label?(x(),D("div",{key:0,class:ce(["select-label",{disabled:s.disabled}])},q(s.label),3)):G("",!0),f("div",ww,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:ce(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(x(!0),D(Ae,null,lt(s.options,u=>(x(),D("optgroup",{key:u.value,label:u.label},[(x(!0),D(Ae,null,lt(u.children,c=>(x(),D("option",{key:c.value,value:c.value,class:"child-option"},q(c.label),9,xw))),128))],8,Ow))),128))],42,Ew),f("div",{class:ce(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(x(),D("div",Sw,q(s.errorMessage),1)):G("",!0)],4)}const Iw=Pe(Cw,[["render",Dw],["__scopeId","data-v-b5d38077"]]),l8="",Nw={name:"FilterTag",props:{readonly:{type:Boolean,default:!1}},emits:["remove"]},Tw={key:0,class:"fas fa-times"};function Aw(e,t,s,i,n,a){return x(),D("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=()=>!s.readonly&&e.$emit("remove"))},[s.readonly?G("",!0):(x(),D("i",Tw)),Pt(e.$slots,"default",{},void 0,!0)])}const zo=Pe(Nw,[["render",Aw],["__scopeId","data-v-1d857df7"]]),u8="",Mw={name:"FilterTags"},Pw={class:"filter-tags"};function kw(e,t,s,i,n,a){return x(),D("div",Pw,[Pt(e.$slots,"default",{},void 0,!0)])}const ia=Pe(Mw,[["render",kw],["__scopeId","data-v-d8e54e5f"]]),c8="",Vw={name:"Autocomplete",components:{FilterTag:zo,FilterTags:ia},props:{modelValue:{type:[Array,Object,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){return this.modelValue?this.modelValue.label:""},selectedItems(){return Array.isArray(this.modelValue)?this.modelValue.map(e=>{if(e.value&&e.label!=="")return e;const t=this.items.find(i=>i.value===(e.value||e)),s=(t==null?void 0:t.label)||"";return{value:e.value||e,label:s}}):[]}},created(){this.debouncedSearch=Ys.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&this.keepOpenOnSelect&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){if(Array.isArray(this.modelValue)){if(this.modelValue.length===this.items.length){this.$emit("update:modelValue",[]);return}this.$emit("update:modelValue",this.items),this.$emit("select-all")}this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},Rw={class:"autocomplete-container"},Fw=["id"],Uw={class:"autocomplete-wrapper"},Lw=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],Bw={key:0,class:"selected-item"},qw=["title"],Hw=["id"],$w=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],Ww={class:"item-label"},jw={key:0,class:"fas fa-check"},zw={key:0,class:"dropdown-item loading-item"},Gw={key:1,class:"dropdown-item no-results"},Kw={key:0,class:"form-control-feedback invalid-feedback d-block"},Qw={key:1,class:"tags-container"};function Zw(e,t,s,i,n,a){const u=$("FilterTag"),c=$("FilterTags");return x(),D("div",Rw,[s.label?(x(),D("label",{key:0,class:ce(["filter-label",{required:s.required}]),id:`${n.uniqueId}-label`},q(s.label),11,Fw)):G("",!0),f("div",Uw,[f("div",{class:"input-container",style:as({maxWidth:a.inputMaxWidthStyle})},[f("div",{class:ce(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[gt(f("input",{type:"text",class:ce(["form-control",{"is-invalid":s.hasError}]),placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>n.searchQuery=h),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,42,Lw),[[ts,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(x(),D("div",Bw,[f("span",{class:"selected-text",title:a.getSelectedItemLabel},q(a.truncateLabel(a.getSelectedItemLabel)),9,qw),f("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=kt((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):G("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(x(),D("i",{key:1,class:ce(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):G("",!0)],2),n.isOpen?(x(),D("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(x(),D(Ae,{key:0},[(x(!0),D(Ae,null,lt(a.displayItems,(h,_)=>(x(),D("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:ce(["dropdown-item",{active:n.selectedIndex===_,selected:h.value!=="__ALL__"&&(Array.isArray(a.selectedItems)?a.selectedItems.some(p=>p.value===h.value):a.selectedItems===h.value)}]),id:`${n.uniqueId}-option-${_}`,role:"option","data-index":_,"aria-selected":n.selectedIndex===_,tabindex:n.selectedIndex===_?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,_),ref_for:!0,ref:"optionElements",title:h.label},[f("span",Ww,q(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(a.selectedItems)&&a.selectedItems.some(p=>p.value===h.value)?(x(),D("i",jw)):G("",!0)],42,$w))),128)),s.loading?(x(),D("div",zw,t[8]||(t[8]=[f("span",null,"Carregando mais itens...",-1)]))):G("",!0)],64)):(x(),D("div",Gw,q(s.noResultsText||"Nenhum item disponível"),1))],40,Hw)):G("",!0)],4),s.hasError&&s.errorMessage?(x(),D("div",Kw,q(s.errorMessage),1)):G("",!0),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(x(),D("div",Qw,[T(c,null,{default:Ce(()=>[(x(!0),D(Ae,null,lt(a.selectedItems,h=>(x(),ht(u,{key:h.value,readonly:s.disabled,onRemove:_=>a.removeItem(h)},{default:Ce(()=>[We(q(h.label),1)]),_:2},1032,["readonly","onRemove"]))),128))]),_:1})])):G("",!0)])])}const _n=Pe(Vw,[["render",Zw],["__scopeId","data-v-0920c39f"]]),d8="",Jw={name:"EnrolmentModalNew",components:{Toast:Br,CustomSelect:Xs},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:Number,required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,showResultAlerts:!1,batchMessage:"",batchMessageType:"success",failedMessages:[],reenrolMessages:[]}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8",this.showResultAlerts=!1,this.batchMessage="",this.batchMessageType="success",this.failedMessages=[],this.reenrolMessages=[]},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await mw(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(_,p)=>{if(p==="\\t")return _.split("	");if(p===" ")return _.split(/\s+/);{const g=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return _.split(new RegExp(g))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(_=>_.includes("userid"))||!u.some(_=>_.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(_=>_.includes("userid")),h=u.findIndex(_=>_.includes("firstname"));for(let _=1;_<i.length;_++){const p=i[_].trim();if(!p)continue;const g=a(p,t);if(g.length>Math.max(c,h)){const v=g[c].trim(),O=g[h].trim();if(v&&O){if(!/^\d+$/.test(v)){console.warn(`Linha ${_+1}: ID inválido '${v}'. Deve ser um número.`);continue}n.push({id:v,name:O})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(s=>s.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(s=>parseInt(s.id))),e||this.showErrorMessage("Nenhum usuário selecionado para efetuar a matrícula");const t=await pw({offerclassid:this.offerclassid,userids:e,roleid:parseInt(this.selectedRoleId)});if(t.data){this.showResultAlerts=!0;const s=t.data.filter(u=>u.success),i=s.length,n=i>0?s.filter(u=>u.reenrol):[],a=t.data.filter(u=>u.success==!1);this.batchMessage=i>0?`${i} de ${e.length} usuário(s) matriculado(s) com sucesso.`:"Nenhuma inscrição foi realizada",this.batchMessageType=i>0?"success":"danger",this.reenrolMessages=n.length>0?n.map(u=>u.message):[],this.failedMessages=a.length>0?a.map(u=>u.message):[],i>0&&this.$emit("success",{count:i,total:e.length})}}catch(e){this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},Yw={class:"modal-header"},Xw={class:"modal-title"},eE={class:"modal-body"},tE={key:0,class:"loading-overlay"},sE={key:1,class:"result-alerts"},rE={key:1,class:"failed-messages"},nE={key:2,class:"reenrol-messages"},oE={key:2,class:"enrolment-modal"},iE={class:"form-row"},aE={class:"form-group"},lE={class:"limited-width-input"},uE={class:"form-group"},cE={class:"limited-width-input"},dE={key:0,class:"error-message"},fE={key:0,class:"form-group"},hE={class:"user-select-container"},pE={class:"custom-autocomplete-wrapper"},mE={key:0,class:"dropdown-menu show"},gE=["onClick"],_E={key:0,class:"fas fa-check"},vE={key:0,class:"selected-users-container"},yE={class:"filter-tags"},bE=["onClick"],CE={key:1,class:"form-group"},wE={class:"file-name"},EE={class:"file-size"},OE={key:0,class:"csv-users-preview"},xE={class:"preview-header"},SE={class:"selected-users-container"},DE={class:"filter-tags"},IE={key:0,class:"more-users"},NE={class:"csv-info"},TE={class:"csv-example"},AE=["href"],ME={class:"csv-options-row"},PE={class:"csv-option"},kE={class:"csv-option"},VE={key:0,class:"modal-footer"},RE=["disabled"];function FE(e,t,s,i,n,a){const u=$("CustomSelect"),c=$("Toast");return x(),D(Ae,null,[s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:ce(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=kt(()=>{},["stop"]))},[f("div",Yw,[f("h3",Xw,q(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",eE,[n.isSubmitting?(x(),D("div",tE,t[17]||(t[17]=[f("div",{class:"loading-content"},[f("div",{class:"spinner-border text-primary",role:"status"},[f("span",{class:"sr-only"},"Carregando...")]),f("p",{class:"loading-text mt-3"},"Processando matrículas...")],-1)]))):G("",!0),n.showResultAlerts?(x(),D("div",sE,[n.batchMessage?(x(),D("div",{key:0,class:ce(["alert",n.batchMessageType==="success"?"alert-success":"alert-danger"])},[f("i",{class:ce(n.batchMessageType==="success"?"fas fa-check-circle":"fas fa-exclamation-triangle")},null,2),We(" "+q(n.batchMessage),1)],2)):G("",!0),n.failedMessages.length>0?(x(),D("div",rE,[(x(!0),D(Ae,null,lt(n.failedMessages,(h,_)=>(x(),D("div",{key:_,class:"alert alert-warning"},[t[18]||(t[18]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),We(" "+q(h),1)]))),128))])):G("",!0),n.reenrolMessages.length>0?(x(),D("div",nE,[(x(!0),D(Ae,null,lt(n.reenrolMessages,(h,_)=>(x(),D("div",{key:_,class:"alert alert-info"},[t[19]||(t[19]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),We(" "+q(h),1)]))),128))])):G("",!0)])):(x(),D("div",oE,[t[34]||(t[34]=f("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),f("div",iE,[f("div",aE,[t[20]||(t[20]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Forma de matrícula"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",lE,[T(u,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>n.enrolmentMethod=h),options:n.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),f("div",uE,[t[21]||(t[21]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Papel para atribuir"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",cE,[T(u,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(x(),D("div",dE," Não foi possível carregar os papéis disponíveis para esta turma. ")):G("",!0)])])]),n.enrolmentMethod==="manual"?(x(),D("div",fE,[t[24]||(t[24]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Selecionar usuários"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",hE,[f("div",pE,[gt(f("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>n.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[ts,n.searchQuery]]),t[22]||(t[22]=f("div",{class:"select-arrow"},null,-1)),n.isOpen?(x(),D("div",mE,[(x(!0),D(Ae,null,lt(n.userOptions,(h,_)=>(x(),D("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[We(q(h.label)+" ",1),n.selectedUsers.some(p=>p.value===h.value)?(x(),D("i",_E)):G("",!0)],8,gE))),128))])):G("",!0)])]),n.selectedUsers.length>0?(x(),D("div",vE,[f("div",yE,[(x(!0),D(Ae,null,lt(n.selectedUsers,h=>(x(),D("div",{key:h.value,class:"tag badge badge-primary",onClick:_=>a.removeUser(h)},[t[23]||(t[23]=f("i",{class:"fas fa-times"},null,-1)),We(" "+q(h.label),1)],8,bE))),128))])])):G("",!0)])):G("",!0),n.enrolmentMethod==="batch"?(x(),D("div",CE,[t[33]||(t[33]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",{class:ce(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[6]||(t[6]=kt((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=kt((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=kt((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[f("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),n.selectedFile?(x(),D(Ae,{key:1},[t[27]||(t[27]=f("div",{class:"file-icon"},[f("i",{class:"fas fa-file-alt"})],-1)),f("p",wE,q(n.selectedFile.name),1),f("p",EE," ("+q(a.formatFileSize(n.selectedFile.size))+") ",1),t[28]||(t[28]=f("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(x(),D(Ae,{key:0},[t[25]||(t[25]=f("div",{class:"upload-icon"},[f("i",{class:"fas fa-arrow-down"})],-1)),t[26]||(t[26]=f("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(x(),D("div",OE,[f("div",xE,[f("span",null,"Usuários encontrados no arquivo ("+q(n.csvUsers.length)+"):",1)]),f("div",SE,[f("div",DE,[(x(!0),D(Ae,null,lt(n.csvUsers.slice(0,5),h=>(x(),D("div",{key:h.id,class:"tag badge badge-primary"},q(h.name),1))),128)),n.csvUsers.length>5?(x(),D("span",IE,"+"+q(n.csvUsers.length-5)+" mais",1)):G("",!0)])])])):G("",!0),f("div",NE,[t[32]||(t[32]=f("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),f("div",TE,[t[29]||(t[29]=f("span",{class:"example-label"},"Exemplo CSV",-1)),f("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,AE)]),f("div",ME,[f("div",PE,[t[30]||(t[30]=f("label",null,"Delimitador do CSV",-1)),T(u,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>n.csvDelimiter=h),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),f("div",kE,[t[31]||(t[31]=f("label",null,"Codificação",-1)),T(u,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>n.csvEncoding=h),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):G("",!0),t[35]||(t[35]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))]))]),n.showResultAlerts?G("",!0):(x(),D("div",VE,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:n.isSubmitting||!a.isFormValid},q(s.confirmButtonText),9,RE),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},q(s.cancelButtonText),1)]))],2)])):G("",!0),T(c,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],64)}const UE=Pe(Jw,[["render",FE],["__scopeId","data-v-cb610ebd"]]),f8="",LE={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},BE={class:"modal-header"},qE={key:0,class:"modal-body"},HE={class:"details-container"},$E={class:"detail-row"},WE={class:"detail-value"},jE={class:"detail-row"},zE={class:"detail-value"},GE={class:"detail-row"},KE={class:"detail-value"},QE={class:"detail-row"},ZE={class:"detail-value"},JE={class:"detail-row"},YE={class:"detail-value"},XE={key:1,class:"modal-body no-data"},eO={class:"modal-footer"};function tO(e,t,s,i,n,a){return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[2]||(t[2]=kt(()=>{},["stop"]))},[f("div",BE,[t[5]||(t[5]=f("h3",{class:"modal-title"},"Informações da matrícula",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[f("i",{class:"fas fa-times"},null,-1)]))]),s.user?(x(),D("div",qE,[f("div",HE,[f("div",$E,[t[6]||(t[6]=f("div",{class:"detail-label"},"Nome completo",-1)),f("div",WE,q(s.user.fullName),1)]),f("div",jE,[t[7]||(t[7]=f("div",{class:"detail-label"},"Curso",-1)),f("div",zE,q(s.courseName),1)]),f("div",GE,[t[8]||(t[8]=f("div",{class:"detail-label"},"Método de inscrição",-1)),f("div",KE,q(a.getEnrolmentMethod(s.user.enrol)),1)]),f("div",QE,[t[9]||(t[9]=f("div",{class:"detail-label"},"Estado",-1)),f("div",ZE,[f("span",{class:ce(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},q(s.user.statusName),3)])]),f("div",JE,[t[10]||(t[10]=f("div",{class:"detail-label"},"Matrícula criada",-1)),f("div",YE,q(s.user.createdDate),1)])])])):(x(),D("div",XE,"Nenhum dado disponível")),f("div",eO,[f("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):G("",!0)}const sO=Pe(LE,[["render",tO],["__scopeId","data-v-030365c3"]]),h8="",rO={name:"EditEnrollmentModal",components:{CustomSelect:Xs},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await gw({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},nO={class:"modal-header"},oO={class:"modal-title"},iO={class:"modal-body"},aO={class:"enrollment-form"},lO={class:"form-row"},uO={class:"form-value"},cO={class:"form-row"},dO={class:"form-field"},fO={class:"select-wrapper"},hO={class:"form-row"},pO={class:"form-field date-time-field"},mO={class:"date-field"},gO={class:"time-field"},_O={class:"enable-checkbox"},vO={class:"form-row"},yO={class:"form-field"},bO={class:"select-wrapper"},CO={class:"form-row"},wO={class:"date-field"},EO=["disabled"],OO={class:"time-field"},xO=["disabled"],SO={class:"enable-checkbox"},DO={class:"form-row"},IO={class:"form-value"},NO={class:"modal-footer"},TO={class:"footer-buttons"},AO=["disabled"];function MO(e,t,s,i,n,a){const u=$("CustomSelect");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[14]||(t[14]=kt(()=>{},["stop"]))},[f("div",nO,[f("h3",oO," Editar matrícula de "+q(s.user?s.user.fullName:""),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",iO,[f("div",aO,[f("div",lO,[t[17]||(t[17]=f("div",{class:"form-label"},"Método de inscrição",-1)),f("div",uO,q(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),f("div",cO,[t[18]||(t[18]=f("div",{class:"form-label"},"Estado",-1)),f("div",dO,[f("div",fO,[T(u,{modelValue:n.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>n.formData.status=c),options:n.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",hO,[t[20]||(t[20]=f("div",{class:"form-label"},"Matrícula começa",-1)),f("div",pO,[f("div",mO,[gt(f("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>n.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[ts,n.formData.startDateStr]])]),f("div",gO,[gt(f("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>n.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[ts,n.formData.startTimeStr]])]),f("div",_O,[gt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>n.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[zi,n.formData.enableStartDate]]),t[19]||(t[19]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",vO,[t[21]||(t[21]=f("div",{class:"form-label"},"Período de validade da matrícula",-1)),f("div",yO,[f("div",bO,[T(u,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>n.formData.validityPeriod=c),options:n.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),f("div",CO,[t[23]||(t[23]=f("div",{class:"form-label"},"Matrícula termina",-1)),f("div",{class:ce(["form-field date-time-field",{"disabled-inputs-only":!n.formData.enableEndDate}])},[f("div",wO,[gt(f("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>n.formData.endDateStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,EO),[[ts,n.formData.endDateStr]])]),f("div",OO,[gt(f("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>n.formData.endTimeStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,xO),[[ts,n.formData.endTimeStr]])]),f("div",SO,[gt(f("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>n.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[zi,n.formData.enableEndDate]]),t[22]||(t[22]=f("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),f("div",DO,[t[24]||(t[24]=f("div",{class:"form-label"},"Matrícula criada",-1)),f("div",IO,q(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),f("div",NO,[t[25]||(t[25]=f("div",{class:"footer-spacer"},null,-1)),f("div",TO,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,AO),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):G("",!0)}const PO=Pe(rO,[["render",MO],["__scopeId","data-v-24ba0708"]]),p8="",m8="",kO={name:"BulkEditEnrollmentModal",components:{Pagination:gn,CustomTable:mn,CustomSelect:Xs},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,_]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,_,0,0);t=Math.floor(p.getTime()/1e3);const g=p.getTimezoneOffset()*60;t+=g}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,_]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,_,0,0);s=Math.floor(p.getTime()/1e3);const g=p.getTimezoneOffset()*60;s+=g}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await _w({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(h=>h.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},VO={class:"modal-header"},RO={class:"modal-body"},FO={class:"enrollment-form"},UO={class:"table-container"},LO={class:"form-row"},BO={class:"form-field"},qO={class:"select-wrapper"},HO={class:"form-row"},$O={class:"form-field date-time-field"},WO={class:"date-field"},jO=["disabled"],zO={class:"time-field"},GO=["disabled"],KO={class:"enable-checkbox"},QO={class:"form-row"},ZO={class:"form-field date-time-field"},JO={class:"date-field"},YO=["disabled"],XO={class:"time-field"},ex=["disabled"],tx={class:"enable-checkbox"},sx={class:"modal-footer"},rx={class:"footer-buttons"},nx=["disabled"];function ox(e,t,s,i,n,a){const u=$("CustomTable"),c=$("Pagination"),h=$("CustomSelect");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=_=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[16]||(t[16]=kt(()=>{},["stop"]))},[f("div",VO,[t[19]||(t[19]=f("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=_=>e.$emit("close"))},t[18]||(t[18]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",RO,[f("div",FO,[f("div",null,[f("div",UO,[T(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?gt((x(),ht(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=_=>n.currentPage=_),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=_=>n.perPage=_),total:s.users.length},null,8,["current-page","per-page","total"])),[[Ql,s.users.length>n.perPage]]):G("",!0),t[20]||(t[20]=f("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),f("div",LO,[t[21]||(t[21]=f("div",{class:"form-label"},"Alterar o status",-1)),f("div",BO,[f("div",qO,[T(h,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=_=>n.formData.status=_),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",HO,[t[23]||(t[23]=f("div",{class:"form-label"},"Alterar data de início",-1)),f("div",$O,[f("div",WO,[gt(f("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=_=>n.formData.startDateStr=_),class:"form-control",onChange:t[5]||(t[5]=(..._)=>a.handleStartDateChange&&a.handleStartDateChange(..._)),disabled:!n.formData.enableStartDate},null,40,jO),[[ts,n.formData.startDateStr]])]),f("div",zO,[gt(f("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=_=>n.formData.startTimeStr=_),class:"form-control",onChange:t[7]||(t[7]=(..._)=>a.handleStartTimeChange&&a.handleStartTimeChange(..._)),disabled:!n.formData.enableStartDate},null,40,GO),[[ts,n.formData.startTimeStr]])]),f("div",KO,[gt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=_=>n.formData.enableStartDate=_),class:"custom-checkbox"},null,512),[[zi,n.formData.enableStartDate]]),t[22]||(t[22]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",QO,[t[25]||(t[25]=f("div",{class:"form-label"},"Alterar data de fim",-1)),f("div",ZO,[f("div",JO,[gt(f("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=_=>n.formData.endDateStr=_),class:"form-control",onChange:t[10]||(t[10]=(..._)=>a.handleEndDateChange&&a.handleEndDateChange(..._)),disabled:!n.formData.enableEndDate},null,40,YO),[[ts,n.formData.endDateStr]])]),f("div",XO,[gt(f("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=_=>n.formData.endTimeStr=_),class:"form-control",onChange:t[12]||(t[12]=(..._)=>a.handleEndTimeChange&&a.handleEndTimeChange(..._)),disabled:!n.formData.enableEndDate},null,40,ex),[[ts,n.formData.endTimeStr]])]),f("div",tx,[gt(f("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=_=>n.formData.enableEndDate=_),class:"custom-checkbox"},null,512),[[zi,n.formData.enableEndDate]]),t[24]||(t[24]=f("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),f("div",sx,[t[26]||(t[26]=f("div",{class:"footer-spacer"},null,-1)),f("div",rx,[f("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(..._)=>a.saveChanges&&a.saveChanges(..._)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,nx),f("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=_=>e.$emit("close"))}," Cancelar ")])])])])):G("",!0)}const ix=Pe(kO,[["render",ox],["__scopeId","data-v-92e8899f"]]),g8="",ax={name:"BulkDeleteEnrollmentModal",components:{Pagination:gn,CustomSelect:Xs,CustomTable:mn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},lx={class:"modal-header"},ux={class:"modal-body"},cx={class:"enrollment-form"},dx={class:"table-container"},fx={class:"modal-footer"},hx={class:"footer-buttons"},px=["disabled"];function mx(e,t,s,i,n,a){const u=$("CustomTable"),c=$("Pagination");return s.show?(x(),D("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[5]||(t[5]=kt(()=>{},["stop"]))},[f("div",lx,[t[8]||(t[8]=f("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",ux,[f("div",cx,[f("div",dx,[T(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?gt((x(),ht(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>n.currentPage=h),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>n.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[Ql,s.users.length>n.perPage]]):G("",!0)]),t[9]||(t[9]=f("div",{class:"text-center mt-5"},[f("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),f("div",fx,[f("div",hx,[f("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:n.isSubmitting},q(n.isSubmitting?"Removendo...":"Remover matrículas"),9,px),f("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):G("",!0)}const gx=Pe(ax,[["render",mx],["__scopeId","data-v-37ea04c6"]]),_8="",_x={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{navigateToBack(){this.$emit("click")}}};function vx(e,t,s,i,n,a){return x(),D("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.navigateToBack&&a.navigateToBack(...u))},[t[1]||(t[1]=f("i",{class:"fas fa-angle-left"},null,-1)),We(" "+q(s.label),1)])}const Go=Pe(_x,[["render",vx],["__scopeId","data-v-774dfbf5"]]),v8="",yx={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},bx=["src"];function Cx(e,t,s,i,n,a){return x(),D("div",{class:"user-avatar",style:as(a.avatarStyle)},[a.hasImage?(x(),D("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,bx)):(x(),D("div",{key:1,class:"avatar-initials",style:as({backgroundColor:a.backgroundColor})},q(a.initials),5))],4)}const wx=Pe(yx,[["render",Cx],["__scopeId","data-v-eed19d8a"]]),y8="",Ex={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await mu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await gu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const n=await yw(this.offeruserenrolid);if(Array.isArray(n)&&n.length)this.selectedRoles=n.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await bw(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},Ox={class:"role-selector"},xx={key:1,class:"role-edit-wrapper"},Sx={class:"role-edit-container"},Dx={class:"select-wrapper"},Ix=["value"],Nx={class:"role-actions"},Tx={key:2,class:"loading-overlay"};function Ax(e,t,s,i,n,a){return x(),D("div",Ox,[n.isEditing?(x(),D("div",xx,[f("div",Sx,[f("div",Dx,[gt(f("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>n.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=kt(()=>{},["stop"])),style:as({height:Math.max(4,n.roles.length)*25+"px"})},[(x(!0),D(Ae,null,lt(n.roles,u=>(x(),D("option",{key:u.id,value:u.id},q(u.name),9,Ix))),128))],4),[[Yl,n.selectedRoles]])]),f("div",Nx,[f("button",{class:"btn-save",onClick:t[3]||(t[3]=kt((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[f("i",{class:"fas fa-check"},null,-1)])),f("button",{class:"btn-cancel",onClick:t[4]||(t[4]=kt((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))])])])):(x(),D("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=kt((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[f("span",null,q(a.displayRoleNames),1),t[5]||(t[5]=f("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),n.loading&&n.isEditing?(x(),D("div",Tx,t[8]||(t[8]=[f("div",{class:"spinner"},null,-1)]))):G("",!0)])}const Mx=Pe(Ex,[["render",Ax],["__scopeId","data-v-217c6284"]]),b8="",Px={name:"Enrollments",components:{CustomTable:mn,CustomSelect:Xs,HierarchicalSelect:Iw,CustomInput:Hn,CustomCheckbox:$o,CustomButton:er,FilterSection:tp,FilterRow:na,FilterGroup:oa,FilterActions:sp,FilterTag:zo,FilterTags:ia,Pagination:gn,PageHeader:$n,ConfirmationModal:jo,Autocomplete:_n,EnrolmentModalNew:UE,EnrollmentDetailsModal:sO,Toast:Br,EditEnrollmentModal:PO,BulkEditEnrollmentModal:ix,BulkDeleteEnrollmentModal:gx,BackButton:Go,UserAvatar:wx,RoleSelector:Mx,LFLoading:Wo},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Qb()}},async created(){var t,s,i,n;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await mu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(n=this.classDetails)==null?void 0:n.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},beforeUnmount(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await hw(t);if(s.data){const i=s.data;Array.isArray(i.enrolments)&&(this.enrolments=i.enrolments.map(n=>({id:n.userid,offeruserenrolid:n.offeruserenrolid,fullName:n.fullname,email:n.email,cpf:n.cpf,enrol:n.enrol,roles:this.formatRoles(n.roles),groups:n.groups,timecreated:n.timecreated,createdDate:this.formatDateTime(n.timecreated),timestart:n.timestart,timeend:n.timeend,startDate:this.formatDate(n.timestart),endDate:this.formatDate(n.timeend),deadline:n.enrolperiod,progress:this.formatProgress(n.progress),situation:n.situation,situationName:n.situation_name,grade:n.grade||"-",status:n.status,statusName:n.status!==void 0?n.status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=i.total||this.enrolments.length)}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.nameOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0):(this.nameOptions=[],this.showNameDropdown=!1)}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.cpfOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0):(this.cpfOptions=[],this.showCpfDropdown=!1)}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});!t.error&&t.data?(this.emailOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0):(this.emailOptions=[],this.showEmailDropdown=!1)}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.showNameDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.showCpfDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.showEmailDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},clearOptions(e){setTimeout(()=>{switch(e){case"name":this.nameOptions=[];break;case"cpf":this.cpfOptions=[];break;case"email":this.emailOptions=[];break;default:this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[];break}},500)},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.loadRegisteredUsers()},clearFilteredUsers(){this.filteredUsers=[],this.loadRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async navigateToBack(){this.router.push({name:"offer.edit",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const n={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};n[e]&&(window.location.href=n[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await gu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(n=>n.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&n.offeruserenrolid&&e.push(n.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await vw(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),n)).catch(n=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,_=>_.toUpperCase()).trim()),n=t+[i.join(","),...e.map(h=>s.map(_=>{const p=h[_]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const _=c[h]||"";return`"${String(_).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let J=0;J<t.length;J++){const oe=t[J].replace(/([A-Z])/g," $1").replace(/^./,Y=>Y.toUpperCase()).trim();s.push(oe)}let i="";for(let J=0;J<s.length;J++)i+="<th>"+s[J]+"</th>";let n="";for(let J=0;J<e.length;J++){let oe="<tr>";for(let Y=0;Y<t.length;Y++)oe+="<td>"+(e[J][t[Y]]||"")+"</td>";oe+="</tr>",n+=oe}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",_="</tr></thead><tbody>",p="</tbody></table>",g='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",v="</body></html>",O=a+u+c+h+i+_+n+p+g+v,k=new Blob([O],{type:"text/html;charset=utf-8;"}),V=URL.createObjectURL(k),re=document.createElement("a");re.setAttribute("href",V),re.setAttribute("download","usuarios_matriculados.html"),re.style.visibility="hidden",document.body.appendChild(re),re.click(),document.body.removeChild(re),URL.revokeObjectURL(V),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const _=s.map(p=>{const g=h[p]||"";return'"'+String(g).replace(/"/g,'""')+'"'});i.push(_.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},kx={id:"offer-manager-component",class:"offer-manager"},Vx={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},Rx={style:{width:"240px"}},Fx={class:"filters-section mb-3"},Ux={class:"row"},Lx={class:"col-md-3"},Bx={class:"filter-input-container position-relative"},qx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Hx=["onClick"],$x={class:"col-md-3"},Wx={class:"filter-input-container position-relative"},jx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},zx=["onClick"],Gx={class:"col-md-3"},Kx={class:"filter-input-container position-relative"},Qx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Zx=["onClick"],Jx={key:0,class:"my-4"},Yx={key:1,class:"alert alert-danger"},Xx={class:"table-container"},eS={class:"checkbox-container"},tS=["checked","indeterminate"],sS={class:"checkbox-container"},rS=["checked","onChange"],nS=["href","title"],oS={class:"user-name-link"},iS={class:"progress-container"},aS={class:"progress-text"},lS={class:"status-container"},uS={class:"status-actions"},cS=["onClick"],dS=["onClick"],fS={class:"selected-users-actions"},hS={class:"bulk-actions-container"},pS={key:2,class:"bottom-enroll-button"};function mS(e,t,s,i,n,a){var _e,Ne,ae;const u=$("BackButton"),c=$("PageHeader"),h=$("HierarchicalSelect"),_=$("CustomButton"),p=$("FilterTag"),g=$("FilterTags"),v=$("UserAvatar"),O=$("RoleSelector"),k=$("CustomTable"),V=$("Pagination"),re=$("EnrollmentDetailsModal"),J=$("EnrolmentModalNew"),oe=$("EditEnrollmentModal"),Y=$("BulkEditEnrollmentModal"),Ee=$("BulkDeleteEnrollmentModal"),X=$("LFLoading"),fe=$("Toast");return x(),D("div",kx,[T(c,{title:"Usuários matriculados"},{actions:Ce(()=>[T(u,{onClick:a.navigateToBack},null,8,["onClick"])]),_:1}),f("div",Vx,[f("div",Rx,[T(h,{modelValue:n.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=A=>n.selectedPageView=A),options:n.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!n.classDetails||((_e=n.classDetails)==null?void 0:_e.operational_cycle)!==2?(x(),ht(_,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):G("",!0)]),f("div",Fx,[f("div",Ux,[f("div",Lx,[f("div",Bx,[t[21]||(t[21]=f("label",{for:"name-filter",class:"form-label text-muted small"},"Filtrar por nome",-1)),gt(f("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[1]||(t[1]=A=>n.nameSearchInput=A),onInput:t[2]||(t[2]=(...A)=>a.handleNameInput&&a.handleNameInput(...A)),onFocus:t[3]||(t[3]=A=>n.showNameDropdown=n.nameOptions.length>0),onBlur:t[4]||(t[4]=A=>a.clearOptions("name"))},null,544),[[ts,n.nameSearchInput]]),n.showNameDropdown&&n.nameOptions.length>0?(x(),D("div",qx,[(x(!0),D(Ae,null,lt(n.nameOptions,A=>(x(),D("button",{key:A.id,type:"button",class:"dropdown-item",onClick:we=>a.selectNameOption(A)},q(A.label),9,Hx))),128))])):G("",!0)])]),f("div",$x,[f("div",Wx,[t[22]||(t[22]=f("label",{for:"cpf-filter",class:"form-label text-muted small"},"Filtrar por CPF",-1)),gt(f("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[5]||(t[5]=A=>n.cpfSearchInput=A),onInput:t[6]||(t[6]=(...A)=>a.handleCpfInput&&a.handleCpfInput(...A)),onFocus:t[7]||(t[7]=A=>n.showCpfDropdown=n.cpfOptions.length>0),onBlur:t[8]||(t[8]=A=>a.clearOptions("cpf"))},null,544),[[ts,n.cpfSearchInput]]),n.showCpfDropdown&&n.cpfOptions.length>0?(x(),D("div",jx,[(x(!0),D(Ae,null,lt(n.cpfOptions,A=>(x(),D("button",{key:A.id,type:"button",class:"dropdown-item",onClick:we=>a.selectCpfOption(A)},q(A.label),9,zx))),128))])):G("",!0)])]),f("div",Gx,[f("div",Kx,[t[23]||(t[23]=f("label",{for:"email-filter",class:"form-label text-muted small"},"Filtrar por E-mail",-1)),gt(f("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[9]||(t[9]=A=>n.emailSearchInput=A),onInput:t[10]||(t[10]=(...A)=>a.handleEmailInput&&a.handleEmailInput(...A)),onFocus:t[11]||(t[11]=A=>n.showEmailDropdown=n.emailOptions.length>0),onBlur:t[12]||(t[12]=A=>a.clearOptions("email"))},null,544),[[ts,n.emailSearchInput]]),n.showEmailDropdown&&n.emailOptions.length>0?(x(),D("div",Qx,[(x(!0),D(Ae,null,lt(n.emailOptions,A=>(x(),D("button",{key:A.id,type:"button",class:"dropdown-item",onClick:we=>a.selectEmailOption(A)},q(A.label),9,Zx))),128))])):G("",!0)])])])]),T(g,null,{default:Ce(()=>[(x(!0),D(Ae,null,lt(n.filteredUsers,A=>(x(),ht(p,{key:A.id,onRemove:we=>a.removeFilter(A.id||A.value)},{default:Ce(()=>[We(q(A.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),n.filteredUsers.length>0?(x(),D("div",Jx,[f("button",{type:"button",class:"btn btn-secondary",onClick:t[13]||(t[13]=(...A)=>a.clearFilteredUsers&&a.clearFilteredUsers(...A))}," Limpar ")])):G("",!0),n.error?(x(),D("div",Yx,[t[24]||(t[24]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),We(" "+q(n.error),1)])):G("",!0),f("div",Xx,[T(k,{headers:n.tableHeaders,items:n.enrolments,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"header-select":Ce(()=>[f("div",eS,[f("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[14]||(t[14]=(...A)=>a.toggleSelectAll&&a.toggleSelectAll(...A)),class:"custom-checkbox"},null,40,tS)])]),"item-select":Ce(({item:A})=>[f("div",sS,[f("input",{type:"checkbox",checked:a.isSelected(A.id),onChange:we=>a.toggleSelectUser(A.id),class:"custom-checkbox"},null,40,rS)])]),"item-fullName":Ce(({item:A})=>[f("a",{class:"user-name-container",href:`/user/view.php?id=${A.id}`,title:"Ver perfil de "+A.fullName},[T(v,{"full-name":A.fullName,size:36},null,8,["full-name"]),f("span",oS,q(A.fullName),1)],8,nS)]),"item-email":Ce(({item:A})=>[We(q(A.email),1)]),"item-cpf":Ce(({item:A})=>[We(q(A.cpf),1)]),"item-roles":Ce(({item:A})=>[T(O,{userId:A.id,offeruserenrolid:A.offeruserenrolid,currentRole:A.roles,offerclassid:n.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Ce(({item:A})=>[We(q(A.groups),1)]),"item-startDate":Ce(({item:A})=>[We(q(A.startDate),1)]),"item-endDate":Ce(({item:A})=>[We(q(A.endDate),1)]),"item-deadline":Ce(({item:A})=>[We(q(A.deadline),1)]),"item-progress":Ce(({item:A})=>[f("div",iS,[f("div",{class:"progress-bar",style:as({width:A.progress})},null,4),f("span",aS,q(A.progress),1)])]),"item-situation":Ce(({item:A})=>[We(q(A.situationName),1)]),"item-grade":Ce(({item:A})=>[We(q(A.grade),1)]),"item-status":Ce(({item:A})=>[f("div",lS,[f("span",{class:ce(["status-tag badge",A.status===0?"badge-success":"badge-danger"])},q(A.statusName),3),f("div",uS,[f("button",{class:"btn-information",onClick:we=>a.showEnrollmentDetails(A),title:"Informações da matrícula"},t[25]||(t[25]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,cS),f("button",{class:"btn-settings",onClick:we=>a.editUser(A),title:"Editar matrícula"},t[26]||(t[26]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"3"}),f("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,dS)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),T(V,{"current-page":n.currentPage,"onUpdate:currentPage":t[15]||(t[15]=A=>n.currentPage=A),"per-page":n.perPage,"onUpdate:perPage":t[16]||(t[16]=A=>n.perPage=A),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),f("div",fS,[f("div",hS,[t[28]||(t[28]=f("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),gt(f("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[17]||(t[17]=A=>n.selectedBulkAction=A),onChange:t[18]||(t[18]=(...A)=>a.handleBulkAction&&a.handleBulkAction(...A))},t[27]||(t[27]=[n1('<option value="" data-v-3ad51b72>Escolher...</option><optgroup label="Comunicação" data-v-3ad51b72><option value="message" data-v-3ad51b72>Enviar uma mensagem</option><option value="note" data-v-3ad51b72>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-3ad51b72><option value="download_csv" data-v-3ad51b72> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-3ad51b72>Microsoft excel (.xlsx)</option><option value="download_html" data-v-3ad51b72>Tabela HTML</option><option value="download_json" data-v-3ad51b72> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-3ad51b72>OpenDocument (.ods)</option><option value="download_pdf" data-v-3ad51b72> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-3ad51b72><option value="edit_enrolment" data-v-3ad51b72> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-3ad51b72> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Yl,n.selectedBulkAction]])])]),!n.classDetails||((Ne=n.classDetails)==null?void 0:Ne.operational_cycle)!==2?(x(),D("div",pS,[T(_,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):G("",!0),T(re,{show:n.showEnrollmentModal,user:n.selectedUser,"course-name":((ae=n.classDetails)==null?void 0:ae.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),T(J,{show:n.showEnrolmentModal,offerclassid:n.offerclassid,roles:n.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),T(oe,{show:n.showEditEnrollmentModal,user:n.selectedUser,offerclassid:n.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),T(Y,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(A=>n.enrolments.find(we=>we.id===A)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[19]||(t[19]=A=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),T(Ee,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(A=>n.enrolments.find(we=>we.id===A)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[20]||(t[20]=A=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),T(X,{"is-loading":n.loading},null,8,["is-loading"]),T(fe,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const gS=Pe(Px,[["render",mS],["__scopeId","data-v-3ad51b72"]]),C8="",_S={name:"Alert",props:{type:{type:String,default:"info"},text:{type:String,required:!0},icon:{type:String,required:!1}}};function vS(e,t,s,i,n,a){return x(),D("div",{class:ce(["alert",`alert-${s.type}`])},[s.icon?(x(),D("i",{key:0,class:ce(s.icon)},null,2)):G("",!0),We(" "+q(s.text.replace("-","‑")),1)],2)}const rp=Pe(_S,[["render",vS],["__scopeId","data-v-03af0515"]]),Ko={data(){return{showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},methods:{showToastMessage(e,t="success"){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),typeof e=="object"&&e.message&&(e=e.message),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType=t,this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showInfoMessage(e){this.showToastMessage(e,"info")},showSuccessMessage(e){this.showToastMessage(e,"success")},showWarningMessage(e){this.showToastMessage(e,"warning")},showErrorMessage(e){this.showToastMessage(e,"error")},showDangerMessage(e){this.showToastMessage(e,"error")}}},w8="",yS={name:"CustomLabel",components:{HelpIcon:ra},props:{text:{type:String,default:""},help:{type:String,default:""},id:{type:String,required:!1},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}}},bS={class:"label-with-help"},CS=["for"],wS={key:0,class:"icon fa fa-exclamation-circle text-danger fa-fw mr-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"};function ES(e,t,s,i,n,a){const u=$("HelpIcon");return x(),D("div",bS,[f("label",{for:s.id,class:ce(["form-label",{disabled:s.disabled}])},[Pt(e.$slots,"default",{},()=>[We(q(s.text),1)],!0)],10,CS),s.required?(x(),D("i",wS)):G("",!0),s.help?(x(),ht(u,{key:1,title:`Ajuda com ${s.text.toLowerCase()}`,text:s.help},null,8,["title","text"])):G("",!0)])}const yu=Pe(yS,[["render",ES],["__scopeId","data-v-04d80c6b"]]),E8="",OS={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},xS={class:"text-editor-container"},SS={class:"editor-toolbar"},DS={class:"toolbar-group"},IS=["disabled"],NS=["disabled"],TS=["disabled"],AS=["disabled"],MS={class:"toolbar-group"},PS=["disabled"],kS=["disabled"],VS=["contenteditable"],RS=["rows","placeholder","disabled"];function FS(e,t,s,i,n,a){return x(),D("div",xS,[s.label?(x(),D("label",{key:0,class:ce(["filter-label",{disabled:s.disabled}])},q(s.label),3)):G("",!0),f("div",{class:ce(["editor-container",{disabled:s.disabled}])},[f("div",SS,[f("div",DS,[f("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[f("i",{class:"fas fa-bold"},null,-1)]),8,IS),f("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[f("i",{class:"fas fa-italic"},null,-1)]),8,NS),f("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[f("i",{class:"fas fa-underline"},null,-1)]),8,TS),f("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[f("i",{class:"fas fa-strikethrough"},null,-1)]),8,AS)]),t[16]||(t[16]=f("div",{class:"toolbar-divider"},null,-1)),f("div",MS,[f("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[f("i",{class:"fas fa-list-ul"},null,-1)]),8,PS),f("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[f("i",{class:"fas fa-list-ol"},null,-1)]),8,kS)])]),n.showHtmlSource?gt((x(),D("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,RS)),[[ts,n.htmlContent]]):(x(),D("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,VS))],2)])}const bu=Pe(OS,[["render",FS],["__scopeId","data-v-6578517d"]]),O8="",US={name:"OfferForm",mixins:[Ko],components:{Toast:Br,HelpIcon:ra,TextEditor:bu,CustomInput:Hn,CustomLabel:yu,CustomSelect:Xs,Autocomplete:_n,CustomCheckbox:$o,ConfirmationModal:jo},props:{offer:{type:Object,required:!0},isEditing:{type:Boolean,required:!0},isReadonly:{type:Boolean,default:!1}},emits:["update:offer","validate"],data(){return{localOffer:{...this.offer},showOfferStatusModal:!1,typeOptions:[],typeEnabled:!1,audienceOptions:[],formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}}}},async created(){this.typeEnabled&&await this.getTypes(),await this.getAudiences()},watch:{offer:{handler(e){var t;if(JSON.stringify(e)!==JSON.stringify(this.localOffer)){const s=(t=e==null?void 0:e.audiences)==null?void 0:t.map(i=>({value:i,label:""}));this.localOffer={...e,audiences:s}}},deep:!0,immediate:!0},localOffer:{handler(e){var s;const t=(s=e==null?void 0:e.audiences)==null?void 0:s.map(i=>i.value);e={...e,audiences:t},JSON.stringify(e)!==JSON.stringify(this.offer)&&this.$emit("update:offer",{...e,audiences:t})},deep:!0}},methods:{async getTypes(){try{const e=await getTypeOptions(),{enabled:t,types:s,default:i}=e;this.typeEnabled=!!t,t&&Array.isArray(s)&&(this.typeOptions=s.map(n=>({value:n,label:n.charAt(0).toUpperCase()+n.slice(1)})))}catch(e){this.showErrorMessage(e.message||"Erro ao carregar opções de tipos.")}},async getAudiences(){this.loading=!0;try{const e=await s0("");this.audienceOptions=e.map(t=>({value:t.id,label:t.name.toUpperCase()}))}catch(e){console.log(e),this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},handleStatusChange(e){if(console.log(e),!e){this.showOfferStatusModal=!0;return}this.localOffer.status=!0,this.validateForm()},confirmInactivateStatus(){this.showOfferStatusModal=!1,this.localOffer.status=!1,this.validateForm()},validateForm(){let e=!0;return Object.keys(this.formErrors).forEach(t=>{this.isValidField(t)||(e=!1)}),this.$emit("validate",e),e},isValidField(e){var t;switch(e){case"name":this.formErrors.name.hasError=!this.localOffer.name;break;case"audiences":this.formErrors.audiences.hasError=!((t=this.localOffer)!=null&&t.audiences)||this.localOffer.audiences.length===0;break}return!this.formErrors[e].hasError}}},LS={class:"form-row mb-3"},BS={class:"form-group"},qS={class:"input-container"},HS={key:0,class:"form-group"},$S={class:"input-container"},WS={key:1,class:"form-group"},jS={class:"input-container pt-4"},zS={class:"form-row mb-3"},GS={class:"form-group"},KS={class:"label-container"},QS={class:"label-with-help"},ZS={class:"input-container"},JS={class:"form-group text-editor-container"};function YS(e,t,s,i,n,a){const u=$("CustomInput"),c=$("CustomLabel"),h=$("CustomSelect"),_=$("CustomCheckbox"),p=$("HelpIcon"),g=$("Autocomplete"),v=$("TextEditor"),O=$("ConfirmationModal"),k=$("Toast");return x(),D("div",null,[f("div",LS,[f("div",BS,[t[9]||(t[9]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Nome da oferta"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),f("div",qS,[T(u,{modelValue:n.localOffer.name,"onUpdate:modelValue":t[0]||(t[0]=V=>n.localOffer.name=V),placeholder:"Oferta 0001",width:280,required:"",disabled:s.isReadonly,"has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=V=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])])]),n.typeEnabled?(x(),D("div",HS,[T(c,{text:"Tipo da oferta"}),f("div",$S,[T(h,{modelValue:n.localOffer.type,"onUpdate:modelValue":t[2]||(t[2]=V=>n.localOffer.type=V),options:n.typeOptions,disabled:s.isReadonly,width:280},null,8,["modelValue","options","disabled"])])])):G("",!0),s.isEditing?(x(),D("div",WS,[f("div",jS,[T(_,{modelValue:n.localOffer.status,"onUpdate:modelValue":t[3]||(t[3]=V=>n.localOffer.status=V),id:"status",label:"Ativar oferta",confirmBeforeChange:!0,disabled:s.isReadonly,onRequestChange:a.handleStatusChange},null,8,["modelValue","disabled","onRequestChange"])])])):G("",!0)]),f("div",zS,[f("div",GS,[f("div",KS,[f("div",QS,[t[10]||(t[10]=f("label",{class:"form-label"},"Público-alvo",-1)),t[11]||(t[11]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),T(p,{title:"Ajuda com público-alvo",text:`Para atribuir o público-alvo é necessário que o mesmo seja previamente criado no 'Gerenciador de Público-Alvo'.<br><br>
                      Após a exclusão do ‘atributo’ de um público-alvo, os usuários que já estiverem inscritos em um curso permanecerão matriculados, 
                      mantendo acesso ao conteúdo normalmente. <br><br>
                      A exclusão impactará apenas a exibição do curso para novos usuários dentro desse público-alvo.`})])]),f("div",ZS,[T(g,{class:"autocomplete-audiences",modelValue:n.localOffer.audiences,"onUpdate:modelValue":[t[4]||(t[4]=V=>n.localOffer.audiences=V),t[5]||(t[5]=V=>a.validateForm())],items:n.audienceOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,disabled:s.isReadonly,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message},null,8,["modelValue","items","disabled","has-error","error-message"])])])]),f("div",JS,[T(c,{text:"Descrição da oferta"}),T(v,{modelValue:n.localOffer.description,"onUpdate:modelValue":[t[6]||(t[6]=V=>n.localOffer.description=V),t[7]||(t[7]=V=>a.validateForm())],placeholder:"Digite a descrição da oferta aqui...",rows:5,disabled:s.isReadonly},null,8,["modelValue","disabled"])]),T(O,{show:n.showOfferStatusModal,size:"md",title:"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma: ","list-title":"Comportamento para os cursos, turmas e matrículas:","list-items":["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."],"confirm-button-text":"Inativar oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[8]||(t[8]=V=>n.showOfferStatusModal=!1),onConfirm:a.confirmInactivateStatus},null,8,["show","onConfirm"]),T(k,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const np=Pe(US,[["render",YS],["__scopeId","data-v-b86ed4cf"]]),x8="",XS={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},e2={class:"table-responsive"},t2={class:"table"},s2={key:0,class:"expand-column"},r2=["onClick","data-value"],n2={key:0,class:"sort-icon"},o2={key:0},i2={key:0,class:"expand-column"},a2=["onClick","title"],l2=["colspan"],u2={class:"expanded-content"},c2={key:1},d2=["colspan"];function f2(e,t,s,i,n,a){return x(),D("div",e2,[f("table",t2,[f("thead",null,[f("tr",null,[s.expandable?(x(),D("th",s2)):G("",!0),(x(!0),D(Ae,null,lt(s.headers,u=>(x(),D("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:ce({sortable:u.sortable}),"data-value":u.value},[We(q(u.text)+" ",1),u.sortable?(x(),D("span",n2,[f("i",{class:ce(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):G("",!0)],10,r2))),128))])]),s.items.length>0?(x(),D("tbody",o2,[(x(!0),D(Ae,null,lt(s.items,(u,c)=>(x(),D(Ae,{key:u.id},[f("tr",{class:ce({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(x(),D("td",i2,[f("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[f("div",{class:ce(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[f("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[f("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),f("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,a2)])):G("",!0),(x(!0),D(Ae,null,lt(s.headers,h=>(x(),D("td",{key:`${u.id}-${h.value}`},[Pt(e.$slots,"item-"+h.value,{item:u},()=>[We(q(u[h.value]),1)],!0)]))),128))],2),s.expandable?(x(),D("tr",{key:0,class:ce(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[f("td",{colspan:s.headers.length+1},[f("div",u2,[Pt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,l2)],2)):G("",!0)],64))),128))])):(x(),D("tbody",c2,[f("tr",null,[f("td",{colspan:s.headers.length+(s.expandable?1:0)},[Pt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=f("div",{class:"empty-state"},[f("span",null,"Não existem registros")],-1))],!0)],8,d2)])]))])])}const h2=Pe(XS,[["render",f2],["__scopeId","data-v-05038124"]]),S8="",D8="",p2={name:"AddOfferCourseModal",components:{CustomInput:Hn,CustomButton:er,CustomTable:mn,Pagination:gn,Autocomplete:_n,FilterTag:zo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loading:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentOfferCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],currentOfferCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e,t){e?(this.getCurrentOfferCourses(),this.getCategories()):(this.selectedCategory=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){if(this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,!e){this.getCurrentOfferCourses();return}this.getCoursesForCategory(e.value)},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{async getCurrentOfferCourses(){try{this.loadingCurrentOfferCourses=!0;const e=await ep(this.offerId);this.currentOfferCourses=e.courses.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id}))}catch{}finally{this.loadingCurrentOfferCourses=!1}},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await pu("");this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch{}finally{this.loadingCategories=!1}},async addOfferCourses(){try{if(this.loading=!0,this.selectedCoursesPreview.length===0){this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await i0(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch{}finally{this.loading=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(e)try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const n=await n0(this.offerId,e,i,t,this.coursesPerPage);let a=null,u=[];if(u=n.courses,a={page:n.page||1,total_pages:n.total_pages||1},a){if(this.coursesPage=a.page||1,this.coursesTotalPages=a.total_pages||1,this.hasMoreCourses=(a.page||1)<(a.total_pages||1),u&&u.length>0){const h=u.filter(_=>!this.currentOfferCourses.some(p=>p.id===_.id)&&!this.selectedCoursesPreview.some(p=>p.id===_.id)).map(_=>({value:_.id,label:_.fullname}));s?this.courseOptions=[...this.courseOptions,...h]:this.courseOptions=h}}else console.warn("Formato de resposta inesperado")}catch(n){console.error("Erro ao carregar cursos da categoria:",n),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?this.getCoursesForCategory(this.selectedCategory.value,this.currentPage,!1):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCourse=null,this.selectedCoursesPreview=[]}}},m2={class:"modal-header"},g2={class:"modal-body"},_2={class:"search-section"},v2={class:"search-group"},y2={class:"search-group"},b2={class:"table-container"},C2={key:0,class:"empty-preview-message"},w2={class:"action-buttons"},E2=["onClick"],O2={class:"modal-footer"};function x2(e,t,s,i,n,a){const u=$("Autocomplete"),c=$("CustomTable"),h=$("Pagination"),_=$("CustomButton");return s.modelValue?(x(),D("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[f("div",{class:"modal-content",onClick:t[5]||(t[5]=kt(()=>{},["stop"]))},[f("div",m2,[t[8]||(t[8]=f("h2",null,"Adicionar curso",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",g2,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",_2,[f("div",v2,[T(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),f("div",y2,[T(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",b2,[n.selectedCoursesPreview.length===0?(x(),D("div",C2,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(x(),ht(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ce(({item:p})=>[f("div",w2,[f("button",{class:"btn-action btn-delete",onClick:g=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,E2)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(x(),ht(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>n.currentPage=p),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>n.perPage=p),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):G("",!0)]),f("div",O2,[T(_,{variant:"primary",label:"Confirmar","is-loading":n.loading,disabled:n.selectedCoursesPreview.length===0,onClick:a.addOfferCourses},null,8,["is-loading","disabled","onClick"]),T(_,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):G("",!0)}const S2=Pe(p2,[["render",x2],["__scopeId","data-v-82ab9251"]]),I8="",D2={name:"DuplicateOfferClassModal",components:{Autocomplete:_n,CustomTable:mn,Pagination:gn,CustomButton:er},props:{offerClass:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loading:!1,loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return this.getSortedCourses().slice(e,t)}},watch:{offerClass(){this.resetForm(),this.getCategories()},parentCourse(){this.resetForm(),this.getCategories()},selectedCategory(e){this.resetCourseSelection(),e!=null&&e.value&&this.getCoursesForCategory(e.value)}},async created(){this.initializeComponent()},methods:{async initializeComponent(){this.resetForm(),await this.getCategories()},resetForm(){this.resetCategorySelection(),this.resetCourseSelection(),this.resetPagination(),this.resetDuplicationState(),this.existingCourses=[]},resetCategorySelection(){this.selectedCategory=null,this.categoryOptions=[],this.loadingCategories=!1},resetCourseSelection(){this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.resetCoursePagination()},resetCoursePagination(){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},resetPagination(){this.currentPage=1},resetDuplicationState(){this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0},getSortedCourses(){return[...this.selectedCoursesPreview].sort((e,t)=>{const s=e[this.sortBy],i=t[this.sortBy];return s<i?this.sortDesc?1:-1:s>i?this.sortDesc?-1:1:0})},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await pu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.handleError("Erro ao carregar categorias:",e)}finally{this.loadingCategories=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(!(!e||!this.offerClass))try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const u=(await m0(this.offerClass.id)).filter(c=>{const h=!i||c.name&&c.name.toLowerCase().includes(i.toLowerCase())||c.fullname&&c.fullname.toLowerCase().includes(i.toLowerCase()),_=!this.selectedCoursesPreview.some(v=>parseInt(v.value)===parseInt(c.id)),p=parseInt(c.categoryid)===parseInt(e),g=parseInt(c.id)!==parseInt(this.parentCourse.courseId);return p&&g&&h&&_}).map(c=>({value:c.id,label:c.name||c.fullname||c.coursename||`Curso ${c.id}`,categoryid:c.categoryid,category_name:c.category_name})).filter(c=>c!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...u]:this.targetCourseOptions=u,this.hasMoreCourses=u.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(n){this.handleError("Erro ao carregar cursos da categoria:",n),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.resetCoursePagination(),await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value),this.selectedCourse=null)},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handleError(e,t){this.$emit("error","Erro ao carregar dados. Por favor, tente novamente.")},async handleConfirm(){if(!(!this.offerClass||this.selectedCoursesPreview.length===0))try{this.loading=!0,this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0,this.$emit("loading",!0);const e=this.offerClass.name,t=parseInt(this.offerClass.id,10),s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value,10);try{const a=await p0(t,n);s.push({offerClassName:e,targetCourseName:i.label,offerClassId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(s.length===0)throw new Error("Nenhuma turma foi duplicada com sucesso.");this.$emit("success",{offerClassName:e,totalSelected:this.totalToDuplicate,totalDuplicates:s.length,duplicates:s}),this.resetForm(),this.$emit("close")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.loading=!1,this.$emit("loading",!1)}}}},I2={class:"modal-header"},N2={class:"modal-title"},T2={class:"modal-body"},A2={class:"search-section"},M2={class:"search-group"},P2={class:"search-group"},k2={class:"table-container"},V2={key:0,class:"empty-preview-message"},R2={class:"action-buttons"},F2=["onClick"],U2={class:"modal-footer"};function L2(e,t,s,i,n,a){var p;const u=$("Autocomplete"),c=$("CustomTable"),h=$("Pagination"),_=$("CustomButton");return x(),D("div",{class:"modal-backdrop",onClick:t[7]||(t[7]=g=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[6]||(t[6]=kt(()=>{},["stop"]))},[f("div",I2,[f("h3",N2,'Duplicar Turma "'+q((p=s.offerClass)==null?void 0:p.name)+'"',1),f("button",{class:"close-button",onClick:t[0]||(t[0]=g=>e.$emit("close"))},t[8]||(t[8]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",T2,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",A2,[f("div",M2,[T(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=g=>n.selectedCategory=g),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),f("div",P2,[T(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=g=>n.selectedCourse=g),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",k2,[n.selectedCoursesPreview.length===0?(x(),D("div",V2,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(x(),ht(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ce(({item:g})=>[f("div",R2,[f("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(g),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,F2)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(x(),ht(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=g=>n.currentPage=g),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=g=>n.perPage=g),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):G("",!0)]),f("div",U2,[T(_,{variant:"primary",label:"Duplicar","is-loading":n.loading,disabled:n.selectedCoursesPreview.length===0,onClick:a.handleConfirm},null,8,["is-loading","disabled","onClick"]),T(_,{variant:"secondary",label:"Cancelar",onClick:t[5]||(t[5]=g=>e.$emit("close"))})])])])}const B2=Pe(D2,[["render",L2],["__scopeId","data-v-12115006"]]),N8="",q2={name:"AddOfferClassModal",components:{Alert:rp,CustomLabel:yu,CustomButton:er,CustomSelect:Xs},props:{offerCourseId:{type:[Number,String],required:!0},offerId:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},created(){this.getEnrolmentMethods()},methods:{async getEnrolmentMethods(){this.loading=!0;const e=await g0(!0);this.enrolmentMethods=e.map(t=>({value:t.enrol,label:t.name})),this.enrolmentMethods.sort((t,s)=>t.label.localeCompare(s.label)),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offerCourseId:this.offerCourseId,offerId:this.offerId})}}},H2={class:"modal-header"},$2={class:"modal-body"},W2={class:"enrol-type-modal"},j2={class:"form-group mb-3"},z2={class:"limited-width-input",style:{"max-width":"280px"}},G2={class:"modal-footer"};function K2(e,t,s,i,n,a){const u=$("Alert"),c=$("CustomLabel"),h=$("CustomSelect"),_=$("CustomButton");return x(),D("div",{class:"modal-overlay",onClick:t[4]||(t[4]=p=>e.$emit("close"))},[f("div",{class:"modal-content",onClick:t[3]||(t[3]=kt(()=>{},["stop"]))},[f("div",H2,[t[6]||(t[6]=f("h2",null,"Adicionar turma",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[5]||(t[5]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",$2,[f("div",W2,[t[7]||(t[7]=f("p",{class:"modal-description"},null,-1)),T(u,{type:"primary",icon:"fas fa-exclamation-triangle",text:"Esta configuração não poderá ser alterada posteriormente."}),f("div",j2,[T(c,{required:"",text:"Tipo de Inscrição",help:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.<br><br>\r
                    Auto inscrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no botão de auto inscrever (recomentado para cursos livres). <br><br>\r
                    Inscrição pelo Gestor Concessionária em ofertas: Nesta opção, o Gestor Concessionária realizará a inscrição manual dos usuários nas turmas correspondentes aos cursos disponíveis na oferta.<br><br>\r
                    Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.\r
              `}),f("div",z2,[T(h,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedEnrolType=p),options:[{value:"",label:"Selecione um método de inscrição..."},...n.enrolmentMethods],width:320,"is-loading":n.loading,required:""},null,8,["modelValue","options","is-loading"])])])])]),f("div",G2,[T(_,{variant:"primary",label:"Continuar",disabled:!n.selectedEnrolType,onClick:a.handleConfirm},null,8,["disabled","onClick"]),T(_,{variant:"secondary",label:"Cancelar",onClick:t[2]||(t[2]=p=>e.$emit("close"))})])])])}const Q2=Pe(q2,[["render",K2],["__scopeId","data-v-6527c0e4"]]),op="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBkPSJNNyAxNHMtMSAwLTEtMSAxLTQgNS00IDUgMyA1IDQtMSAxLTEgMUg3em00LTZhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2eiIgZmlsbD0iI2ZmZiIvPg0KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNS4yMTYgMTRBMi4yMzggMi4yMzggMCAwIDEgNSAxM2MwLTEuMzU1LjY4LTIuNzUgMS45MzYtMy43MkE2LjMyNSA2LjMyNSAwIDAgMCA1IDljLTQgMC01IDMtNSA0czEgMSAxIDFoNC4yMTZ6IiBmaWxsPSIjZmZmIi8+DQogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPg0KPC9zdmc+DQo=",T8="",A8="",Z2={name:"TableList",mixins:[Ko],components:{OfferForm:np,CustomTable:mn,CustomSelect:Xs,CustomInput:Hn,CustomButton:er,Pagination:gn,CollapsibleTable:h2,PageHeader:$n,BackButton:Go,Autocomplete:_n,TextEditor:bu,CustomCheckbox:$o,FilterRow:na,FilterGroup:oa,FilterTag:zo,FilterTags:ia,AddOfferCourseModal:S2,ConfirmationModal:jo,Toast:Br,DuplicateOfferClassModal:B2,AddOfferClassModal:Q2,LFLoading:Wo},props:{offerId:{type:Number,required:!0},isReadonly:{type:Boolean,default:!1}},data(){return{showAddCourseModalVisible:!1,showCourseStatusModal:!1,showDeleteOfferCourseModal:!1,offerCourseToDelete:null,showDeleteOfferClassModal:!1,offerClassToDelete:null,classParentCourse:null,showOfferClassStatusModal:!1,showDuplicateOfferClassModal:!1,showEnrolTypeModal:!1,selectedOfferClass:null,offerClassToDuplicate:null,classToDuplicateParentOfferCourse:null,selectedOfferCourseForClass:null,categoryOptions:[],courseOptions:[],selectedOfferCourse:null,loading:!1,inputFilters:{course:null,category:null,onlyActive:!1},offerCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,filterCoursesPage:1,filterCoursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,filterCourseNoResultsText:"Nenhum curso encontrado",offerCourseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"courseClassCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offerClassTableHeaders:[{text:"NOME DA TURMA",value:"name",sortable:!1},{text:"TIPO DE INSCRIÇÃO",value:"enrolName",sortable:!1},{text:"Nº DE VAGAS",value:"vacancies",sortable:!1},{text:"Nº DE INSCRITOS",value:"totalEnrolled",sortable:!1},{text:"DATA INÍCIO",value:"startDate",sortable:!1},{text:"DATA FIM",value:"endDate",sortable:!1},{text:"STATUS",value:"status",sortable:!1},{text:"AÇÕES",value:"actions",sortable:!1}]}},watch:{async"inputFilters.course"(e,t){this.currentPage=1,e!==null&&(this.inputFilters.category=null),await this.getCourses()},async"inputFilters.category"(e,t){e===""&&(this.inputFilters.category=null),e!==null&&(this.inputFilters.course=null),this.currentPage=1,await this.getCourses(),await this.getCourseOptions()},async"inputFilters.onlyActive"(e,t){this.currentPage=1,this.inputFilters.course=null,await this.getCourses(),await this.getCourseOptions()},currentPage(){this.getCourses()},perPage(){this.currentPage=1,this.getCourses()}},async created(){await this.getCourses(),await this.getCategoryOptions(),await this.getCourseOptions()},methods:{async getCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.inputFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.inputFilters.course&&(e.courseIds=[this.inputFilters.course.value]),this.inputFilters.category&&(e.categorySearch=this.inputFilters.category.label);const t=await ep(this.offerId,e),{page:s,total_pages:i,total_items:n,courses:a}=t;this.currentPage=s;const u=[];for(const c of a)try{const _=(await u0(c.id)).map(p=>({...p,enrol:p.enrol||"-",enrolName:p.enrol_name||"-",vacancies:p.max_users?p.max_users:"Ilimitado",totalEnrolled:p.enrolled_users||0,startDate:this.formatDate(p.startdate),endDate:this.formatDate(p.enddate),status:!!parseInt(p.status),statusName:parseInt(p.status)?"Ativa":"Inativo",canActivate:p.can_activate,canDelete:p.can_delete}));u.push({id:c.id,courseId:c.courseid,name:c.fullname,category:c.category_name||"-",courseClassCount:_.length,status:!!parseInt(c.status),statusName:parseInt(c.status)?"Ativo":"Inativo",canDelete:c.can_delete,canActivate:c.can_activate,offerClasses:_})}catch(h){console.log(h)}this.offerCourses=u,this.totalItems=n}catch{this.offerCourses=[],this.totalItems=0}finally{this.loading=!1}},async getCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await pu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getCourseOptions(e="",t=!0){var s,i;if(this.offerId){this.loading=!0;try{t?(this.filterCoursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const a=(await o0(this.offerId,(s=this.inputFilters.category)==null?void 0:s.value,e,(i=this.inputFilters.course)!=null&&i.value?[this.inputFilters.course.value]:[],this.inputFilters.onlyActive)).map(u=>({value:u.id||u.courseid,label:u.fullname}));t?this.courseOptions=a:this.courseOptions=[...this.courseOptions,...a],this.hasMoreCourses=!1,this.courseOptions.length===0&&(this.filterCourseNoResultsText="Nenhum curso disponível nesta categoria")}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},getOperationalCycleClassName(e){switch(e){case 0:return"badge-secondary";case 1:return"badge-primary";case 2:return"badge-success";default:return""}},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},clearFilters(){this.inputFilters={course:null,category:null,onlyActive:!1},this.getCourses(),this.getCourseOptions()},async removeFilter(e){this.inputFilters[e]=null},async loadMoreCourses(){var e;this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.filterCoursesPage+=1,(e=this.inputFilters.category)!=null&&e.value&&await this.getCourseOptions("",!1))},handleOnlyActiveChange(){this.currentPage=1,this.getCourses()},async handleAddCourseConfirm(e){try{this.loading=!0,await this.getCourses(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.getCourses()},handlePageChange(e){this.currentPage=e,this.getCourses()},addOfferClass(e){this.selectedOfferCourseForClass=e,this.showEnrolTypeModal=!0},handleAddClassConfirm(e){this.showEnrolTypeModal=!1,this.$router.push({name:"offer.class.create",query:{enrolMethod:e.enrolType,offerCourseId:e.offerCourseId}})},navigateToShowOfferClass(e){this.$router.push({name:"offer.class.show",params:{offerClassId:e.id}})},navigateToEditOfferClass(e){this.$router.push({name:"offer.class.edit",params:{offerClassId:e.id}})},requestToggleOfferClassStatus(e){this.selectedOfferClass={...e},this.showOfferClassStatusModal=!0},async toggleOfferClassStatus(){if(this.selectedOfferClass)try{this.loading=!0;const e=this.selectedOfferClass.name,t=!this.selectedOfferClass.status;await _0(this.selectedOfferClass.id,t);const s=this.offerCourses.findIndex(i=>i.offerClasses.some(n=>n.id===this.selectedOfferClass.id));if(s!==-1){const i=this.offerCourses[s],n=i.offerClasses.findIndex(a=>a.id===this.selectedOfferClass.id);if(n!==-1){const a=i.offerClasses[n];a.status=t,a.statusName=t?"Ativo":"Inativo"}}await this.getCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedOfferClass=null,this.showOfferClassStatusModal=!1}catch{}finally{this.loading=!1}},removeOfferClass(e,t){const s=e.offerClasses[t];s.canDelete&&(this.offerClassToDelete=s,this.classParentCourse=e,this.showDeleteOfferClassModal=!0)},navigateToEnrollments(e){this.$router.push({name:"Enrollments",params:{classId:parseInt(e.id)}})},async handleDuplicateSuccess(e){await this.getCourses(),e.totalDuplicates&&this.showSuccessMessage(`Turma "${e.offerClassName}" duplicada com sucesso para ${e.totalDuplicates} curso(s).`)},duplicateOfferClass(e,t){this.offerClassToDuplicate=e,this.classToDuplicateParentOfferCourse=t,this.showDuplicateOfferClassModal=!0},async deleteOfferClass(){if(!(!this.offerClassToDelete||!this.classParentCourse)){this.loading=!0;try{const e=this.offerClassToDelete.name;await d0(this.offerClassToDelete.id);const t=this.classParentCourse.offerClasses.findIndex(s=>s.id===this.offerClassToDelete.id);t!==-1&&(this.classParentCourse.offerClasses.splice(t,1),this.classParentCourse.courseClassCount=this.classParentCourse.offerClasses.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.offerClassToDelete=null,this.classParentCourse=null,this.showDeleteOfferClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}}},requestToggleOfferCourseStatus(e){e.canActivate&&(this.selectedOfferCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status?e.canActivate?"Inativar":"Não é possível inativar este curso":e.canActivate?"Ativar":"Não é possível ativar este curso"},async toggleOfferCourseStatus(){if(this.selectedOfferCourse)try{this.loading=!0;const e=!this.selectedOfferCourse.status,t=this.selectedOfferCourse.name,s=this.selectedOfferCourse.id;await t0(this.offerId,s,e);const i=this.offerCourses.findIndex(n=>n.id===this.selectedOfferCourse.id);if(i!==-1){const n=this.offerCourses[i];n.status=e,n.statusName=e?"Ativo":"Inativo"}this.showCourseStatusModal=!1,this.selectedOfferCourse=null,await this.getCourses(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch{}finally{this.loading=!1}},requestDeleteOfferCourse(e){e.canDelete&&(this.offerCourseToDelete=e,this.showDeleteOfferCourseModal=!0)},async deleteOfferCourse(){if(this.offerCourseToDelete)try{this.loading=!0;const e=this.offerCourseToDelete.name,t=this.offerCourseToDelete.id;await e0(this.offerId,t),this.offerCourses=this.offerCourses.filter(i=>i.id!==this.offerCourseToDelete.id),this.offerCourseToDelete=null,this.showDeleteOfferCourseModal=!1,await this.getCourses();const s=`Curso "${e}" excluído com sucesso.`;this.showSuccessMessage(s)}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}}}},J2={class:"filters-left-group"},Y2={key:0,class:"filters-right-group"},X2={class:"empty-state"},eD={class:"no-results"},tD=["title"],sD={key:0},rD={key:1},nD={class:"action-buttons"},oD=["disabled","onClick"],iD=["onClick","disabled","title"],aD=["onClick","disabled","title"],lD={class:"action-buttons"},uD=["onClick"],cD=["onClick"],dD=["disabled","onClick"],fD=["disabled","onClick"],hD=["title","disabled","onClick"],pD=["onClick","disabled","title"],mD={class:"course-class-container"},gD={class:"course-class-content"},_D={key:0},vD={class:"course-class-col"},yD=["title"],bD={class:"course-class-col"},CD={class:"course-class-col"},wD={class:"course-class-col"},ED={class:"course-class-col"},OD={class:"course-class-col"},xD={class:"course-class-col operational-cycle"},SD={class:"course-class-col"},DD={class:"action-buttons"},ID=["onClick"],ND=["onClick"],TD=["disabled","onClick"],AD=["disabled","onClick"],MD=["title","disabled","onClick"],PD=["onClick","disabled","title"],kD={key:1,class:"empty-course-class"};function VD(e,t,s,i,n,a){var fe,_e,Ne,ae,A,we,ue,ze,_t,pt,ut;const u=$("Autocomplete"),c=$("FilterGroup"),h=$("CustomCheckbox"),_=$("FilterTag"),p=$("FilterTags"),g=$("CustomButton"),v=$("FilterRow"),O=$("CustomTable"),k=$("CollapsibleTable"),V=$("Pagination"),re=$("AddOfferCourseModal"),J=$("ConfirmationModal"),oe=$("DuplicateOfferClassModal"),Y=$("AddOfferClassModal"),Ee=$("LFLoading"),X=$("Toast");return x(),D("div",null,[T(v,{inline:"",class:"courses-filter-row"},{default:Ce(()=>[f("div",J2,[T(c,null,{default:Ce(()=>[T(u,{modelValue:n.inputFilters.category,"onUpdate:modelValue":t[0]||(t[0]=K=>n.inputFilters.category=K),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","no-results-text"])]),_:1}),T(c,null,{default:Ce(()=>[T(u,{modelValue:n.inputFilters.course,"onUpdate:modelValue":t[1]||(t[1]=K=>n.inputFilters.course=K),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.filterCourseNoResultsText,onLoadMore:a.loadMoreCourses,onSearch:t[2]||(t[2]=K=>a.getCourseOptions(K)),ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onLoadMore"])]),_:1}),T(c,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Ce(()=>[T(h,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[3]||(t[3]=K=>n.inputFilters.onlyActive=K),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),n.inputFilters.course?(x(),ht(p,{key:0,class:"mt-3"},{default:Ce(()=>[T(_,{onRemove:t[4]||(t[4]=K=>a.removeFilter("course"))},{default:Ce(()=>[We(" Curso: "+q(n.inputFilters.course.label),1)]),_:1})]),_:1})):G("",!0)]),s.isReadonly?G("",!0):(x(),D("div",Y2,[T(g,{variant:"primary",icon:"fa-solid fa-plus",label:"Adicionar Curso",onClick:t[5]||(t[5]=K=>n.showAddCourseModalVisible=!0)})]))]),_:1}),T(k,{headers:n.offerCourseTableHeaders,items:n.offerCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Ce(()=>[f("div",X2,[f("span",eD,q(n.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Ce(({item:K})=>[f("span",{title:K.name},q(K.name.length>50?K.name.slice(0,50)+"...":K.name),9,tD)]),"item-status":Ce(({item:K})=>[K.status?(x(),D("span",sD,t[15]||(t[15]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--success)","stroke-width":"2"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z",fill:"var(--success)"})],-1),We(" Ativo ")]))):(x(),D("span",rD,t[16]||(t[16]=[f("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_572_6021)"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),f("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z",fill:"var(--danger)"})]),f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--danger)","stroke-width":"2"}),f("defs",null,[f("clipPath",{id:"clip0_572_6021"},[f("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"})])])],-1),We(" Inativo ")])))]),"item-actions":Ce(({item:K})=>[f("div",nD,[f("button",{class:"btn-action btn-add",disabled:s.isReadonly,onClick:ye=>a.addOfferClass(K),title:"Adicionar turma"},t[17]||(t[17]=[f("i",{class:"fa-solid fa-plus"},null,-1)]),8,oD),f("button",{class:ce(["btn-action",K.status?"btn-deactivate":"btn-activate"]),onClick:ye=>a.requestToggleOfferCourseStatus(K),disabled:!K.status&&!K.canActivate||!K.canActivate||s.isReadonly,title:a.getStatusButtonTitle(K)},[f("i",{class:ce(K.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,iD),f("button",{class:"btn-action btn-delete",onClick:ye=>a.requestDeleteOfferCourse(K),disabled:!K.canDelete||s.isReadonly,title:K.canDelete?"Excluir":"Não é possível excluir este curso"},t[18]||(t[18]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,aD)])]),"expanded-content":Ce(({item:K})=>{var ye;return[T(O,{headers:n.offerClassTableHeaders,items:K.offerClasses,tableClass:"",theadClass:"thead-dark",tbodyClass:"tbody-light"},{"item-name":Ce(({item:be})=>[We(q(be.name.length>20?e.courseClass.name.slice(0,20)+"...":be.name),1)]),"item-status":Ce(({item:be})=>[f("span",{class:ce(["operational-cycle badge",a.getOperationalCycleClassName(be.operational_cycle)])},q(be.operational_cycle_name),3)]),"item-actions":Ce(({item:be})=>[f("div",lD,[f("button",{class:"btn-action btn-edit",onClick:wt=>a.navigateToShowOfferClass(be),title:"Visualizar"},t[19]||(t[19]=[f("img",{src:_u},null,-1)]),8,uD),f("button",{class:"btn-action btn-users",onClick:wt=>a.navigateToEnrollments(be),title:"Usuários Matriculados"},t[20]||(t[20]=[f("img",{src:op},null,-1)]),8,cD),f("button",{class:"btn-action btn-edit",disabled:s.isReadonly,onClick:wt=>a.navigateToEditOfferClass(be),title:"Editar"},t[21]||(t[21]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,dD),f("button",{class:"btn-action btn-duplicate",disabled:s.isReadonly,onClick:wt=>a.duplicateOfferClass(e.courseClass,be),title:"Duplicar Turma"},t[22]||(t[22]=[f("i",{class:"fas fa-copy"},null,-1)]),8,fD),f("button",{class:ce(["btn-action",be.status?"btn-deactivate":"btn-activate"]),title:be.status?"Inativar":"Ativar",disabled:s.isReadonly,onClick:wt=>a.requestToggleOfferClassStatus(be)},[f("i",{class:ce(be.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,hD),f("button",{class:"btn-action btn-delete",onClick:wt=>a.removeOfferClass(be,e.index),disabled:!be.canDelete||s.isReadonly,title:be.canDelete?"Excluir":"Não é possível excluir esta turma"},t[23]||(t[23]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,pD)])]),_:2},1032,["headers","items"]),f("div",mD,[t[30]||(t[30]=f("div",{class:"course-class-header"},[f("div",{class:"course-class-col"},"NOME DA TURMA"),f("div",{class:"course-class-col"},"TIPO DE INSCRIÇÃO"),f("div",{class:"course-class-col"},"Nº DE VAGAS"),f("div",{class:"course-class-col"},"Nº DE INSCRITOS"),f("div",{class:"course-class-col"},"DATA INÍCIO"),f("div",{class:"course-class-col"},"DATA FIM"),f("div",{class:"course-class-col"},"STATUS"),f("div",{class:"course-class-col"},"AÇÕES")],-1)),f("div",gD,[((ye=K==null?void 0:K.offerClasses)==null?void 0:ye.length)>0?(x(),D("div",_D,[(x(!0),D(Ae,null,lt(K.offerClasses,(be,wt)=>(x(),D("div",{class:"course-class-row",key:wt},[f("div",vD,[f("span",{title:be.name},q(be.name.length>20?be.name.slice(0,20)+"...":be.name),9,yD)]),f("div",bD,q(be.enrolName),1),f("div",CD,q(be.vacancies),1),f("div",wD,q(be.totalEnrolled),1),f("div",ED,q(be.startDate),1),f("div",OD,q(be.endDate),1),f("div",xD,[f("span",{class:ce(["badge",a.getOperationalCycleClassName(be.operational_cycle)])},q(be.operational_cycle_name),3)]),f("div",SD,[f("div",DD,[f("button",{class:"btn-action btn-edit",onClick:ct=>a.navigateToShowOfferClass(K),title:"Visualizar"},t[24]||(t[24]=[f("img",{src:_u},null,-1)]),8,ID),f("button",{class:"btn-action btn-users",onClick:ct=>a.navigateToEnrollments(be),title:"Usuários Matriculados"},t[25]||(t[25]=[f("img",{src:op},null,-1)]),8,ND),f("button",{class:"btn-action btn-edit",disabled:s.isReadonly,onClick:ct=>a.navigateToEditOfferClass(be),title:"Editar"},t[26]||(t[26]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,TD),f("button",{class:"btn-action btn-duplicate",disabled:s.isReadonly,onClick:ct=>a.duplicateOfferClass(be,K),title:"Duplicar Turma"},t[27]||(t[27]=[f("i",{class:"fas fa-copy"},null,-1)]),8,AD),f("button",{class:ce(["btn-action",be.status?"btn-deactivate":"btn-activate"]),title:be.status?"Inativar":"Ativar",disabled:s.isReadonly,onClick:ct=>a.requestToggleOfferClassStatus(be)},[f("i",{class:ce(be.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,MD),f("button",{class:"btn-action btn-delete",onClick:ct=>a.removeOfferClass(K,wt),disabled:!be.canDelete||s.isReadonly,title:be.canDelete?"Excluir":"Não é possível excluir esta turma"},t[28]||(t[28]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,PD)])])]))),128))])):(x(),D("div",kD,t[29]||(t[29]=[f("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]}),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),T(V,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[6]||(t[6]=K=>n.currentPage=K),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[7]||(t[7]=K=>n.perPage=K),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"]),s.offerId?(x(),ht(re,{key:0,modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[8]||(t[8]=K=>n.showAddCourseModalVisible=K),offerId:s.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offerId","onConfirm"])):G("",!0),T(J,{size:"md",show:n.showCourseStatusModal,title:(fe=n.selectedOfferCourse)!=null&&fe.showOfferClassStatusModal?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:(_e=n.selectedOfferCourse)!=null&&_e.status?"":"Tem certeza que deseja ativar este curso?","list-items":(Ne=n.selectedOfferCourse)!=null&&Ne.status?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":(ae=n.selectedOfferCourse)!=null&&ae.status?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:(A=n.selectedOfferCourse)!=null&&A.status?"warning":"question",onClose:t[9]||(t[9]=K=>n.showCourseStatusModal=!1),onConfirm:a.toggleOfferCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),T(J,{size:"md",show:n.showDeleteOfferCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[10]||(t[10]=K=>n.showDeleteOfferCourseModal=!1),onConfirm:a.deleteOfferCourse},null,8,["show","onConfirm"]),T(J,{size:"md",show:n.showDeleteOfferClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[11]||(t[11]=K=>n.showDeleteOfferClassModal=!1),onConfirm:a.deleteOfferClass},null,8,["show","onConfirm"]),T(J,{show:n.showOfferClassStatusModal,size:"md",title:(we=n.selectedOfferClass)!=null&&we.status?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:(ue=n.selectedOfferClass)!=null&&ue.status?"":"Tem certeza que deseja ativar esta turma?","list-items":(ze=n.selectedOfferClass)!=null&&ze.status?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":(_t=n.selectedOfferClass)!=null&&_t.status?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:(pt=n.selectedOfferClass)!=null&&pt.status?"warning":"question",onClose:t[12]||(t[12]=K=>n.showOfferClassStatusModal=!1),onConfirm:a.toggleOfferClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),n.showDuplicateOfferClassModal?(x(),ht(oe,{key:1,offerClass:n.offerClassToDuplicate,parentCourse:n.classToDuplicateParentOfferCourse,offerId:s.offerId,onClose:t[13]||(t[13]=K=>n.showDuplicateOfferClassModal=!1),onSuccess:a.handleDuplicateSuccess,onError:e.showErrorMessage},null,8,["offerClass","parentCourse","offerId","onSuccess","onError"])):G("",!0),n.showEnrolTypeModal?(x(),ht(Y,{key:2,offerCourseId:(ut=n.selectedOfferCourseForClass)==null?void 0:ut.id,offerId:s.offerId,onClose:t[14]||(t[14]=K=>n.showEnrolTypeModal=!1),onConfirm:a.handleAddClassConfirm},null,8,["offerCourseId","offerId","onConfirm"])):G("",!0),T(Ee,{"is-loading":n.loading},null,8,["is-loading"]),T(X,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const RD=Pe(Z2,[["render",VD],["__scopeId","data-v-3e190104"]]),M8="",FD={name:"CreateEdit",mixins:[Ko],components:{Toast:Br,Alert:rp,LFLoading:Wo,OfferForm:np,PageHeader:$n,BackButton:Go,CustomButton:er,ConfirmationModal:jo,OfferCourseTableList:RD},props:{isReadonly:{type:Boolean,default:!1}},data(){return{loading:!1,offer:{id:null,name:"",type:"",description:"",status:!1,audiences:[],creatorname:"",createddate:"",modifieddate:"",modifiername:""},isEditing:!1,isValidForm:!1,showRequestSaveModal:!1}},async created(){const e=parseInt(this.$route.params.id);e&&(await this.getOffer(e),this.isEditing=!0)},methods:{async getOffer(e){try{this.loading=!0;const t=await Jb(e);this.offer={...t,status:!!parseInt(t.status)}}catch(t){this.showErrorMessage(t)}finally{this.loading=!1}},async createOffer(){if(this.loading=!0,this.showRequestSaveModal=!1,!this.isValidForm){this.loading=!1;return}try{const e=await Xh(this.offer);if(e.id){const t=e.id;this.showSuccessMessage("Oferta criada com sucesso!"),this.isEditing=!0,this.$router.push({name:"offer.edit",params:{id:t}})}}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async updateOffer(){if(this.loading=!0,this.showRequestSaveModal=!1,!this.isValidForm){this.loading=!1;return}try{await Xh(this.offer),this.showSuccessMessage("Oferta atualizada com sucesso!")}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},navigateToBack(){this.$router.push({name:"offer.index"})}}},UD={class:"section-container mt-3"},LD={key:0,class:"section-title"},BD={key:1,class:"message-container"},qD={class:"d-flex flex-column mt-3"},HD={key:0,class:"text-muted"},$D={key:1,class:"text-muted"},WD={class:"actions-container"};function jD(e,t,s,i,n,a){const u=$("BackButton"),c=$("PageHeader"),h=$("Alert"),_=$("OfferForm"),p=$("OfferCourseTableList"),g=$("CustomButton"),v=$("ConfirmationModal"),O=$("LFLoading"),k=$("Toast");return x(),D("div",{id:"create-edit-component",class:ce({"edit-offer":n.isEditing&&!s.isReadonly,"view-offer":s.isReadonly,"create-offer":!n.isEditing})},[T(c,{title:n.isEditing?s.isReadonly?"Visualizar oferta":`Editar oferta: ${n.offer.name}`:"Adicionar oferta"},{actions:Ce(()=>[T(u,{onClick:a.navigateToBack},null,8,["onClick"])]),_:1},8,["title"]),T(h,{type:"primary",icon:"fas fa-exclamation-triangle",text:`Para que uma instância de oferta seja ativada e disponibilize os cursos\r
      para os públicos-alvo configurados, é necessário garantir que pelo menos\r
      um curso, um grupo de público-alvo, e uma turma estejam configurados à\r
      instância de oferta.`}),f("div",UD,[t[5]||(t[5]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),T(_,{offer:n.offer,"onUpdate:offer":t[0]||(t[0]=V=>n.offer=V),isEditing:n.isEditing,isReadonly:s.isReadonly,onValidate:t[1]||(t[1]=V=>n.isValidForm=V)},null,8,["offer","isEditing","isReadonly"])]),f("div",{class:ce(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(x(),D("h2",LD,"CURSOS")):G("",!0),n.isEditing?G("",!0):(x(),D("div",BD,t[6]||(t[6]=[f("div",{class:"lock-message"},[f("i",{class:"fas fa-lock lock-icon"}),f("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))),n.isEditing?(x(),ht(p,{key:2,offerId:n.offer.id,isReadonly:s.isReadonly},null,8,["offerId","isReadonly"])):G("",!0)],2),f("div",qD,[n.offer.creatorname?(x(),D("small",HD," Criado por "+q(n.offer.creatorname)+" em "+q(n.offer.createddate),1)):G("",!0),n.offer.modifiername?(x(),D("small",$D," Atualizado por "+q(n.offer.modifiername)+" em "+q(n.offer.modifieddate),1)):G("",!0)]),f("div",WD,[T(g,{variant:"primary",label:"Salvar",onClick:t[2]||(t[2]=V=>n.showRequestSaveModal=!0),disabled:!n.isValidForm||s.isReadonly},null,8,["disabled"]),T(g,{variant:"secondary",label:"Cancelar",onClick:a.navigateToBack},null,8,["onClick"])]),t[7]||(t[7]=f("hr",null,null,-1)),t[8]||(t[8]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[We(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),T(v,{show:n.showRequestSaveModal,size:"md",title:"Você tem certeza que deseja salvar as alterações feitas?","confirm-button-text":"Salvar","cancel-button-text":"Cancelar",onClose:t[3]||(t[3]=V=>n.showRequestSaveModal=!1),onConfirm:t[4]||(t[4]=V=>n.isEditing?a.updateOffer():a.createOffer())},null,8,["show"]),T(O,{"is-loading":n.loading},null,8,["is-loading"]),T(k,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],2)}const Cu=Pe(FD,[["render",jD],["__scopeId","data-v-a566cd8c"]]),P8="",zD={name:"Form",mixins:[Ko],components:{CustomLabel:yu,CustomInput:Hn,CustomSelect:Xs,CustomButton:er,PageHeader:$n,BackButton:Go,Autocomplete:_n,TextEditor:bu,CustomCheckbox:$o,FilterRow:na,FilterGroup:oa,Toast:Br,HelpIcon:ra,FilterTag:zo,FilterTags:ia},props:{offerCourse:{type:Object,required:!0},offerClass:{type:Object,required:!0},isEditing:{type:Boolean,required:!0},isReadonly:{type:Boolean,default:!1}},emits:["update:offerClass","validate"],data(){return{loading:!1,loadingTeachers:!1,localOfferClass:{},divisionsOptions:[],sectorsOptions:[],groupsOptions:[],dealershipsOptions:[],roleOptions:[],teacherOptions:[],modalityOptions:[],situationOptions:[],formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Perfil padrão é obrigatório"},modality:{hasError:!1,message:"Modalidade é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"},reenrolmentsituations:{hasError:!1,message:"É necessário selecionar um status quando a rematrícula estiver habilitada"},minusers:{hasError:!1,message:"Mínimo de usuários deve ser maior ou igual a zero"},maxusers:{hasError:!1,message:"Máximo de usuários deve ser maior ou igual a zero"},maxusersdealership:{hasError:!1,message:"Número máximo de inscrições por concessionária deve ser maior ou igual a zero"}}}},async created(){await this.getInitialData(),this.debouncedSearchTeachers=Ys.debounce(e=>{this.fetchPotentialTeachers(e)},300)},computed:{maxEnrolPeriod(){if(this.localOfferClass.startdate&&this.localOfferClass.optional_fields.enddate&&this.localOfferClass.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.localOfferClass.startdate,this.localOfferClass.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.localOfferClass.startdate&&this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&this.localOfferClass.startdate===this.localOfferClass.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{offerClass:{handler(e){Ys.isEqual(e,this.localOfferClass)||(this.localOfferClass={...e})},deep:!0,immediate:!0},localOfferClass:{handler(e){Ys.isEqual(e,this.offerClass)||this.$emit("update:offerClass",e)},deep:!0},"localOfferClass.optional_fields.enablehirearchyrestriction":function(e,t){Ys.isEqual(e,t)||e||(this.localOfferClass.optional_fields={...this.localOfferClass.optional_fields,hirearchyrestrictiondivisions:[],hirearchyrestrictionsectors:[],hirearchyrestrictiongroups:[],hirearchyrestrictiondealerships:[]})},"localOfferClass.optional_fields.hirearchyrestrictiondivisions":function(e,t){if(Ys.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictionsectors=[],this.localOfferClass.optional_fields.hirearchyrestrictiongroups=[],this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getSectors(s)},"localOfferClass.optional_fields.hirearchyrestrictionsectors":function(e,t){if(Ys.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictiongroups=[],this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getGroups(s)},"localOfferClass.optional_fields.hirearchyrestrictiongroups":function(e,t){if(Ys.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getDealerships(s)},"localOfferClass.optional_fields.enableenrolperiod":function(e){!e&&this.localOfferClass.optional_fields.enableextension&&(this.localOfferClass.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."))},"localOfferClass.startdate":function(){this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&this.isValidField("enddate"),this.localOfferClass.optional_fields.enableenrolperiod&&this.localOfferClass.optional_fields.enrolperiod&&this.isValidField("enrolperiod"),this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enddate":function(){this.localOfferClass.optional_fields.enableenddate&&this.isValidField("enddate"),this.localOfferClass.optional_fields.enableenrolperiod&&this.localOfferClass.optional_fields.enrolperiod&&this.isValidField("enrolperiod"),this.localOfferClass.optional_fields.enablepreenrolment&&this.localOfferClass.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enableenddate":function(e){e&&this.localOfferClass.optional_fields.enddate&&this.isValidField("enddate"),!e&&this.localOfferClass.optional_fields.enableenrolperiod&&this.localOfferClass.optional_fields.enrolperiod&&this.isValidField("enrolperiod"),this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enrolperiod":function(e){this.localOfferClass.optional_fields.enableenrolperiod&&e&&this.isValidField("enrolperiod")},"localOfferClass.optional_fields.preenrolmentstartdate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"localOfferClass.optional_fields.preenrolmentenddate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"localOfferClass.optional_fields.enablereenrol":function(){this.isValidField("reenrolmentsituations")},"localOfferClass.optional_fields.reenrolmentsituations":function(){this.isValidField("reenrolmentsituations")},"localOfferClass.optional_fields.minusers":function(){this.isValidField("minusers"),this.localOfferClass.optional_fields.maxusers!==null&&this.localOfferClass.optional_fields.maxusers!==void 0&&this.isValidField("maxusers")},"localOfferClass.optional_fields.maxusers":function(){this.isValidField("maxusers"),this.localOfferClass.optional_fields.minusers!==null&&this.localOfferClass.optional_fields.minusers!==void 0&&this.isValidField("minusers")},"localOfferClass.optional_fields.maxusersdealership":function(){this.isValidField("maxusersdealership")}},methods:{async getInitialData(){try{this.loading=!0,await this.setEnrolmentMethod(),await this.getRoles(),await this.getModalities(),await this.getSituations(),await this.getHierarchyRestrictionData()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async getRoles(){const e=await gu(this.offerCourse.id);if(this.roleOptions=e.map(t=>({value:t.id,label:t.name})).sort((t,s)=>t.label.localeCompare(s.label)),!this.localOfferClass.optional_fields.roleid){const t=this.roleOptions.find(s=>s.value===5);this.localOfferClass.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async getModalities(){this.modalityOptions=[{value:"presencial",label:"Presencial"},{value:"web",label:"WEB"},{value:"virtual",label:"Virtual"},{value:"blended",label:"Blended"}],this.localOfferClass.optional_fields.modality||(this.localOfferClass.optional_fields.modality=this.modalityOptions[0].value)},async getSituations(){const e=await h0();this.situationOptions=e.map(t=>({value:t.id,label:t.name}))},async setEnrolmentMethod(){try{if(this.isEditing)return;const e=this.$route.query.enrolMethod;if(!e)throw new Error("Método de inscrição não informado");this.localOfferClass.enrol=e}catch(e){this.showErrorMessage(e)}},async getHierarchyRestrictionData(){await this.getDivisions(),this.localOfferClass.optional_fields.enablehirearchyrestriction&&(this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.length&&await this.getSectors(),this.localOfferClass.optional_fields.hirearchyrestrictionsectors.length&&await this.getGroups(),this.localOfferClass.optional_fields.hirearchyrestrictiongroups.length&&await this.getDealerships())},async getDivisions(){try{const e=await v0();this.divisionsOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}},async getSectors(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.map(s=>s.value));const t=await y0(e);this.sectorsOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){this.showErrorMessage(t)}},async getGroups(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictionsectors.map(s=>s.value));const t=await b0(e);this.groupsOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){this.showErrorMessage(t)}},async getDealerships(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictiongroups.map(s=>s.value));const t=await C0(e);this.dealershipsOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){this.showErrorMessage(t)}},async fetchPotentialTeachers(e){try{if(e.length<3)return;this.loadingTeachers=!0;let t=this.localOfferClass.teachers.map(i=>i.value)??[];const s=await f0(this.offerCourse.id,this.classId,e,t);this.teacherOptions=s.map(i=>({value:i.id,label:i.fullname}))}catch(t){this.showErrorMessage(t)}finally{this.loadingTeachers=!1}},isValidForm(){return!Object.values(this.formErrors).some(e=>e.hasError)},isValidField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.localOfferClass.classname;break;case"startdate":const t=this.localOfferClass.startdate,s=t&&this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&new Date(this.localOfferClass.startdate)>new Date(this.localOfferClass.optional_fields.enddate);t?s?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.localOfferClass.optional_fields.roleid;break;case"enddate":const i=this.localOfferClass.optional_fields.enableenddate,n=this.localOfferClass.optional_fields.enddate,a=i&&n&&this.localOfferClass.startdate&&new Date(this.localOfferClass.optional_fields.enddate)<new Date(this.localOfferClass.startdate);i&&!n?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):a?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.localOfferClass.optional_fields.enablepreenrolment&&!this.localOfferClass.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.localOfferClass.optional_fields.enablepreenrolment&&!this.localOfferClass.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const u=this.localOfferClass.optional_fields.enableenrolperiod,c=this.localOfferClass.optional_fields.enrolperiod!==null&&this.localOfferClass.optional_fields.enrolperiod!==void 0&&this.localOfferClass.optional_fields.enrolperiod!=="",h=this.maxEnrolPeriod!==null&&c&&parseInt(this.localOfferClass.optional_fields.enrolperiod)>this.maxEnrolPeriod;u&&!c?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):u&&h?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensionmaxrequests;break;case"reenrolmentsituations":this.localOfferClass.optional_fields.enablereenrol&&!this.localOfferClass.optional_fields.reenrolmentsituations.length?this.formErrors.reenrolmentsituations.hasError=!0:this.formErrors.reenrolmentsituations.hasError=!1;break;case"minusers":this.validateMinUsers();break;case"maxusers":this.validateMaxUsers();break;case"maxusersdealership":this.validateMaxUsersByDealership();break}return this.$emit("validate",this.isValidForm()),!this.formErrors[e].hasError},mapToOptions(e){return e.map(t=>({value:t,label:""}))},mapToValues(e){return e.map(t=>t.value)},calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.localOfferClass.optional_fields.enableenrolperiod&&(this.localOfferClass.optional_fields.enableenrolperiod=!1,this.localOfferClass.optional_fields.enrolperiod=null,this.localOfferClass.optional_fields.enableextension&&(this.localOfferClass.optional_fields.enableextension=!1,this.localOfferClass.optional_fields.extensionperiod=null,this.localOfferClass.optional_fields.extensiondaysavailable=null,this.localOfferClass.optional_fields.extensionmaxrequests=null),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.localOfferClass.optional_fields.enablepreenrolment){const t=this.localOfferClass.startdate,s=this.localOfferClass.optional_fields.enableenddate?this.localOfferClass.optional_fields.enddate:null,i=this.localOfferClass.optional_fields.preenrolmentstartdate,n=this.localOfferClass.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},validateMinUsers(){const e=this.localOfferClass.optional_fields.minusers,t=this.localOfferClass.optional_fields.maxusers,s=parseInt(e),i=parseInt(t);return this.formErrors.minusers.hasError=!1,s===0?!0:i>0&&s>i?(this.formErrors.minusers.message="Mínimo de usuários inscritos deve ser menor que o máximo de usuários inscritos",this.formErrors.minusers.hasError=!0,!1):!0},validateMaxUsers(){const e=this.localOfferClass.optional_fields.minusers,t=this.localOfferClass.optional_fields.maxusers,s=parseInt(t),i=parseInt(e);return this.formErrors.maxusers.hasError=!1,s===0?!0:i>0&&s<i?(this.formErrors.maxusers.message="Máximo de usuários inscritos deve ser maior que o mínimo de usuários inscritos",this.formErrors.maxusers.hasError=!0,!1):!0},validateMaxUsersByDealership(){const e=parseInt(this.localOfferClass.optional_fields.maxusersdealership);if(this.formErrors.maxusersdealership.hasError=!1,e===0)return!0;if(e>this.localOfferClass.optional_fields.maxusers)return this.formErrors.maxusersdealership.message="Número máximo de inscrições por concessionária deve ser menor ou igual ao máximo de inscrições",this.formErrors.maxusersdealership.hasError=!0,!1},navigateToBack(){this.offerCourse.offerid?this.router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})}}},GD={class:"row mb-3"},KD={class:"col-md-3"},QD={class:"form-group"},ZD={class:"col-md-3"},JD={class:"form-group"},YD={class:"col-md-3"},XD={class:"col-md-3 ml-md-n3"},eI={class:"form-group"},tI={class:"row mb-3"},sI={class:"col-12"},rI={class:"form-group"},nI={class:"row mb-3"},oI={class:"col-md-3"},iI={class:"col-md-3"},aI={class:"col-md-3 ml-md-n3"},lI={class:"form-group"},uI={key:0,class:"row mb-3"},cI={key:0,class:"col-md-3"},dI={class:"form-group"},fI={key:1,class:"col-md-3"},hI={class:"form-group"},pI={class:"col-md-4"},mI={class:"form-group"},gI={class:"row mb-n3"},_I={class:"col-md-12"},vI={class:"row mb-3"},yI={class:"col-md-4 col-lg-3"},bI={class:"form-group"},CI={class:"col-md-4 col-lg-3"},wI={class:"form-group"},EI={class:"col-md-4 col-lg-3"},OI={class:"form-group"},xI={class:"col-md-4 col-lg-3"},SI={class:"form-group"},DI={class:"row mb-3"},II={class:"col-md-3"},NI={class:"form-group"},TI={class:"col-md-3"},AI={class:"form-group"},MI={key:0,class:"col-md-3"},PI={key:1,class:"col-md-3 ml-md-n3"},kI={class:"form-group"},VI={key:1,class:"row mb-n3"},RI={class:"col-md-12"},FI={key:2,class:"row mb-3"},UI={class:"col-md-4"},LI={class:"col-md-4"},BI={class:"col-md-4"},qI={key:3,class:"row mb-n3"},HI={class:"col-md-4"},$I={class:"form-group"},WI={key:4,class:"row mb-3"},jI={class:"col-md-4"},zI={class:"row mb-3"},GI={class:"col-md-4"},KI={class:"form-group mb-3"};function QI(e,t,s,i,n,a){const u=$("CustomLabel"),c=$("CustomInput"),h=$("CustomCheckbox"),_=$("TextEditor"),p=$("Autocomplete"),g=$("CustomSelect");return x(),D("div",null,[f("div",GD,[f("div",KD,[f("div",QD,[T(u,{required:"",text:"Nome da turma",help:"Insira um nome para a turma. Exemplo: Turma ADM 2025."}),T(c,{modelValue:n.localOfferClass.classname,"onUpdate:modelValue":t[0]||(t[0]=v=>n.localOfferClass.classname=v),placeholder:"Digite o nome da turma",required:"",ref:"classnameInput",disabled:s.isReadonly,"has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=v=>a.isValidField("classname"))},null,8,["modelValue","disabled","has-error","error-message"])])]),f("div",ZD,[f("div",JD,[T(u,{required:"",text:"Data de início",help:"Insira uma data de início para a turma. Exemplo: 07/04/2025."}),T(c,{modelValue:n.localOfferClass.startdate,"onUpdate:modelValue":t[2]||(t[2]=v=>n.localOfferClass.startdate=v),type:"date",required:"",class:"date-input",ref:"startdateInput",disabled:s.isReadonly,"has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=v=>a.isValidField("startdate"))},null,8,["modelValue","disabled","has-error","error-message"])])]),f("div",YD,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenddate}])},[T(u,{text:"Data de término",help:"Insira uma data de término para a turma, caso haja. Exemplo: 07/12/2025."}),T(c,{modelValue:n.localOfferClass.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=v=>n.localOfferClass.optional_fields.enddate=v),type:"date",disabled:!n.localOfferClass.optional_fields.enableenddate||s.isReadonly,required:"",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=v=>a.isValidField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",XD,[f("div",eI,[T(u,{text:" "}),T(h,{modelValue:n.localOfferClass.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=v=>n.localOfferClass.optional_fields.enableenddate=v),id:"enableEndDate",label:"Habilitar data de termínio",disabled:s.isReadonly},null,8,["modelValue","disabled"])])])]),f("div",tI,[f("div",sI,[f("div",rI,[T(u,{text:"Descrição da turma",help:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025"}),T(_,{modelValue:n.localOfferClass.optional_fields.description,"onUpdate:modelValue":t[7]||(t[7]=v=>n.localOfferClass.optional_fields.description=v),placeholder:"Digite a descrição da turma aqui...",rows:4,disabled:s.isReadonly},null,8,["modelValue","disabled"])])])]),f("div",nI,[f("div",oI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enablepreenrolment}])},[T(u,{text:"Data de início da inscrição",help:"Data de início do período de inscrição."}),T(c,{modelValue:n.localOfferClass.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[8]||(t[8]=v=>n.localOfferClass.optional_fields.preenrolmentstartdate=v),type:"date",disabled:!n.localOfferClass.optional_fields.enablepreenrolment||s.isReadonly,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[9]||(t[9]=v=>a.isValidField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",iI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enablepreenrolment}])},[T(u,{required:"",text:"Data de término da inscrição"}),T(c,{modelValue:n.localOfferClass.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[10]||(t[10]=v=>n.localOfferClass.optional_fields.preenrolmentenddate=v),type:"date",disabled:!n.localOfferClass.optional_fields.enablepreenrolment||s.isReadonly,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[11]||(t[11]=v=>a.isValidField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",aI,[f("div",lI,[T(u,{text:" "}),T(h,{modelValue:n.localOfferClass.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[12]||(t[12]=v=>n.localOfferClass.optional_fields.enablepreenrolment=v),id:"enablePreEnrolment",label:"Habilitar a inscrição",disabled:s.isReadonly},null,8,["modelValue","disabled"])])])]),n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",uI,[n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",cI,[f("div",dI,[T(u,{text:"Número mínimo de inscrições",help:"Insira um número mínimo de inscrições (vagas) para a turma, se houver. Exemplo: 20."}),T(c,{modelValue:n.localOfferClass.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=v=>n.localOfferClass.optional_fields.minusers=v),type:"number","has-error":n.formErrors.minusers.hasError,"error-message":n.formErrors.minusers.message,onValidate:t[14]||(t[14]=v=>a.isValidField("minusers")),min:0,disabled:s.isReadonly},null,8,["modelValue","has-error","error-message","disabled"])])])):G("",!0),n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",fI,[f("div",hI,[T(u,{text:"Número máximo de inscrições",help:"Insira um número máximo de inscrições (vagas) para a turma. Exemplo: 100."}),T(c,{modelValue:n.localOfferClass.optional_fields.maxusers,"onUpdate:modelValue":t[15]||(t[15]=v=>n.localOfferClass.optional_fields.maxusers=v),type:"number","has-error":n.formErrors.maxusers.hasError,"error-message":n.formErrors.maxusers.message,onValidate:t[16]||(t[16]=v=>a.isValidField("maxusers")),min:0,disabled:s.isReadonly},null,8,["modelValue","has-error","error-message","disabled"])])])):G("",!0),f("div",pI,[f("div",mI,[T(u,{text:"Número máximo de inscrições por concessionária",help:"Insira um número máximo de inscrições por concessionária para a turma, se houver. Exemplo: 5."}),T(c,{modelValue:n.localOfferClass.optional_fields.maxusersdealership,"onUpdate:modelValue":t[17]||(t[17]=v=>n.localOfferClass.optional_fields.maxusersdealership=v),type:"number","has-error":n.formErrors.maxusersdealership.hasError,"error-message":n.formErrors.maxusersdealership.message,onValidate:t[18]||(t[18]=v=>a.isValidField("maxusersdealership")),min:0,disabled:s.isReadonly},null,8,["modelValue","has-error","error-message","disabled"])])])])):G("",!0),f("div",gI,[f("div",_I,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction,"dependent-field":!0}])},[T(h,{modelValue:n.localOfferClass.optional_fields.enablehirearchyrestriction,"onUpdate:modelValue":t[19]||(t[19]=v=>n.localOfferClass.optional_fields.enablehirearchyrestriction=v),id:"enablehirearchyrestriction",label:"Habilitar restrição por estruturas",help:"Ao habilitar esta opção, os campos 'Divisão', 'Setor', 'Grupo' e 'Concessionária' serão automaticamente ajustados conforme as seleções realizadas nos respectivos filtros.",disabled:s.isReadonly},null,8,["modelValue","disabled"])],2)])]),f("div",vI,[f("div",yI,[f("div",bI,[T(u,{text:"Divisão"}),T(p,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiondivisions,"onUpdate:modelValue":t[20]||(t[20]=v=>n.localOfferClass.optional_fields.hirearchyrestrictiondivisions=v),items:n.divisionsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||s.isReadonly},null,8,["modelValue","items","disabled"])])]),f("div",CI,[f("div",wI,[T(u,{text:"Setor"}),T(p,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictionsectors,"onUpdate:modelValue":t[21]||(t[21]=v=>n.localOfferClass.optional_fields.hirearchyrestrictionsectors=v),items:n.sectorsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictiondivisions.length||s.isReadonly},null,8,["modelValue","items","disabled"])])]),f("div",EI,[f("div",OI,[T(u,{text:"Grupo"}),T(p,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiongroups,"onUpdate:modelValue":t[22]||(t[22]=v=>n.localOfferClass.optional_fields.hirearchyrestrictiongroups=v),items:n.groupsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictionsectors.length||s.isReadonly},null,8,["modelValue","items","disabled"])])]),f("div",xI,[f("div",SI,[T(u,{text:"Concessionária"}),T(p,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiondealerships,"onUpdate:modelValue":t[23]||(t[23]=v=>n.localOfferClass.optional_fields.hirearchyrestrictiondealerships=v),items:n.dealershipsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictiongroups.length||s.isReadonly},null,8,["modelValue","items","disabled"])])])]),f("div",DI,[f("div",II,[f("div",NI,[T(u,{required:"",text:"Perfil atribuído por padrão",help:`Caso a turma seja criada para perfis de 'Aluno Nissan' e ‘Aluno Concessionária’, o perfil padrão selecionado será ‘Estudante’.<br><br>
                    Caso a turma seja criada para perfis de ‘Gestores’, o perfil padrão selecionado será ‘Gestor’ ou outro perfil pertinente.`}),T(g,{modelValue:n.localOfferClass.optional_fields.roleid,"onUpdate:modelValue":t[24]||(t[24]=v=>n.localOfferClass.optional_fields.roleid=v),options:n.roleOptions,required:"",disabled:s.isReadonly,"has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[25]||(t[25]=v=>a.isValidField("roleid"))},null,8,["modelValue","options","disabled","has-error","error-message"])])]),f("div",TI,[f("div",AI,[T(u,{required:"",text:"Modalidade da turma",help:"Selecione a modalidade da turma."}),T(g,{modelValue:n.localOfferClass.optional_fields.modality,"onUpdate:modelValue":t[26]||(t[26]=v=>n.localOfferClass.optional_fields.modality=v),options:n.modalityOptions,required:"",disabled:s.isReadonly,"has-error":n.formErrors.modality.hasError,"error-message":n.formErrors.modality.message,onValidate:t[27]||(t[27]=v=>a.isValidField("roleid"))},null,8,["modelValue","options","disabled","has-error","error-message"])])]),n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",MI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly}])},[T(u,{required:n.localOfferClass.optional_fields.enableenrolperiod,id:"enrolperiod",text:"Duração da matrícula",help:"Insira um período em dias para a duração da matricula dos alunos, se houver, na turma. Exemplo: 15 <br><br>"+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["required","help"]),T(c,{modelValue:n.localOfferClass.optional_fields.enrolperiod,"onUpdate:modelValue":t[28]||(t[28]=v=>n.localOfferClass.optional_fields.enrolperiod=v),type:"number",id:"enrolperiod",disabled:!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[29]||(t[29]=v=>a.isValidField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])],2)])):G("",!0),n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",PI,[f("div",kI,[T(u,{text:" "}),T(h,{modelValue:n.localOfferClass.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[30]||(t[30]=v=>n.localOfferClass.optional_fields.enableenrolperiod=v),id:"enableEnrolPeriod",label:"Habilitar duração da matrícula",disabled:a.shouldDisableEnrolPeriod||s.isReadonly,title:a.shouldDisableEnrolPeriod?"Não é possível habilitar duração da matrícula para turmas de um dia (data início = data fim)":""},null,8,["modelValue","disabled","title"])])])):G("",!0)]),n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",VI,[f("div",RI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[T(h,{modelValue:n.localOfferClass.optional_fields.enableextension,"onUpdate:modelValue":t[31]||(t[31]=v=>n.localOfferClass.optional_fields.enableextension=v),id:"enableextension",label:"Habilitar prorrogação de matrícula",disabled:!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,help:"A prorrogação estende a duração da matrícula do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou.<br><br> Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado.",title:n.localOfferClass.optional_fields.enableenrolperiod?"":"É necessário habilitar o duração da matrícula primeiro"},null,8,["modelValue","disabled","title"])],2)])])):G("",!0),n.localOfferClass.enrol!=="offer_audience"?(x(),D("div",FI,[f("div",UI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[T(u,{required:n.localOfferClass.optional_fields.enableextension,text:"Dias adicionais na matrícula",help:"Insira um período em dias para prorrogar a matricula dos alunos da turma. Exemplo: 3."},null,8,["required"]),T(c,{modelValue:n.localOfferClass.optional_fields.extensionperiod,"onUpdate:modelValue":t[32]||(t[32]=v=>n.localOfferClass.optional_fields.extensionperiod=v),type:"number",disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[33]||(t[33]=v=>a.isValidField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",LI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[T(u,{required:n.localOfferClass.optional_fields.enableextension,text:"Dias antes do fim para exibir o botão de prorrogação"},null,8,["required"]),T(c,{modelValue:n.localOfferClass.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[34]||(t[34]=v=>n.localOfferClass.optional_fields.extensiondaysavailable=v),type:"number",disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[35]||(t[35]=v=>a.isValidField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])],2)]),f("div",BI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[T(u,{required:n.localOfferClass.optional_fields.enableextension,text:"Quantidade máxima de prorrogações permitidas"},null,8,["required"]),T(c,{modelValue:n.localOfferClass.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[36]||(t[36]=v=>n.localOfferClass.optional_fields.extensionmaxrequests=v),type:"number",disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[37]||(t[37]=v=>a.isValidField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])],2)])])):G("",!0),n.localOfferClass.enrol!=="offer_dealership"?(x(),D("div",qI,[f("div",HI,[f("div",$I,[T(h,{modelValue:n.localOfferClass.optional_fields.enablereenrol,"onUpdate:modelValue":t[38]||(t[38]=v=>n.localOfferClass.optional_fields.enablereenrol=v),id:"enableReenrol",label:"Habilitar rematrícula",text:"Habilitar rematrícula",help:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela.",disabled:s.isReadonly},null,8,["modelValue","disabled"])])])])):G("",!0),n.localOfferClass.enrol!=="offer_dealership"?(x(),D("div",WI,[f("div",jI,[f("div",{class:ce(["form-group",{disabled:!n.localOfferClass.optional_fields.enablereenrol}])},[T(u,{text:"Status que permite rematrículas",required:n.localOfferClass.optional_fields.enablereenrol},null,8,["required"]),T(p,{modelValue:n.localOfferClass.optional_fields.reenrolmentsituations,"onUpdate:modelValue":t[39]||(t[39]=v=>n.localOfferClass.optional_fields.reenrolmentsituations=v),items:n.situationOptions,placeholder:"Selecione o status...",required:!0,disabled:!n.localOfferClass.optional_fields.enablereenrol||s.isReadonly,"auto-open":!1,"has-error":n.formErrors.reenrolmentsituations.hasError,"error-message":n.formErrors.reenrolmentsituations.message},null,8,["modelValue","items","disabled","has-error","error-message"])],2)])])):G("",!0),f("div",zI,[f("div",GI,[f("div",KI,[T(u,{text:"Atribuir Instrutor",help:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."}),T(p,{class:"autocomplete-audiences",modelValue:n.localOfferClass.teachers,"onUpdate:modelValue":t[40]||(t[40]=v=>n.localOfferClass.teachers=v),items:n.teacherOptions,"has-search-icon":!0,placeholder:"Pesquisar...",autoOpen:!1,"show-all-option":!1,loading:n.loadingTeachers,disabled:s.isReadonly,onSearch:t[41]||(t[41]=v=>e.debouncedSearchTeachers(v))},null,8,["modelValue","items","loading","disabled"])])])])])}const ZI=Pe(zD,[["render",QI],["__scopeId","data-v-992a44df"]]),k8="",JI={name:"CreateEdit",mixins:[Ko],components:{Form:ZI,Toast:Br,LFLoading:Wo,PageHeader:$n,BackButton:Go,CustomButton:er,Autocomplete:_n},props:{isReadonly:{type:Boolean,default:!1}},data(){return{loading:!1,saving:!1,originalClassName:"",offerClass:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,modality:null,maxusersdealership:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,enablehirearchyrestriction:!1,hirearchyrestrictiondivisions:[],hirearchyrestrictionsectors:[],hirearchyrestrictiongroups:[],hirearchyrestrictiondealerships:[]},creatorname:"",createddate:"",modifieddate:"",modifiername:""},offerCourse:null,isValidForm:!1}},async created(){this.isEditing&&await this.getOfferClass(),this.offerCourseId&&await this.getOfferCourse()},computed:{isEditing(){return!!this.offerClassId},offerClassId(){return parseInt(this.$route.params.offerClassId)},offerCourseId(){var e;return this.$route.query.offerCourseId||((e=this.offerClass)==null?void 0:e.offercourseid)}},methods:{async getOfferClass(){try{this.loading=!0;const e=await mu(parseInt(this.offerClassId));this.offerClass=e,this.offerClass.offercourseid=parseInt(this.offerCourseId),this.offerClass.teachers=this.offerClass.teachers.map(t=>({value:t.id,label:t.fullname})),e.optional_fields&&this.processOptionalFields(e.optional_fields),this.originalClassName=this.offerClass.classname}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getOfferCourse(){try{this.loading=!0;const e=await l0(this.offerCourseId);this.offerCourse=e}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async createClass(){try{if(!this.$refs.form.isValidForm())return;this.saving=!0;const t=this.prepareClassData(!1);if(!t)return;let s=await a0(t);this.showSuccessMessage(s.message),this.$router.push({name:"offer.class.edit",params:{offerCourseId:this.offerCourse.id,offerClassId:s.offerclassid}})}catch(e){this.showErrorMessage(e)}finally{this.saving=!1}},async updateClass(){try{if(!this.$refs.form.isValidForm())return;this.saving=!0;const t=this.prepareClassData(!0);if(!t)return;t.offerclassid=this.offerClassId;let s=await c0(t);this.showSuccessMessage(s.message),this.getOfferClass()}catch(e){console.log(e),this.showErrorMessage(e)}finally{this.saving=!1}},prepareClassData(){const e=Ys.cloneDeep(this.offerClass);e.offercourseid=parseInt(this.offerCourseId),e.teachers=e.teachers.map(i=>i.value),(!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod)&&(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=e.optional_fields.reenrolmentsituations.map(i=>i.value):e.optional_fields.reenrolmentsituations=[],e.optional_fields.enablehirearchyrestriction?(e.optional_fields.hirearchyrestrictiondivisions=this.mapToValues(e.optional_fields.hirearchyrestrictiondivisions),e.optional_fields.hirearchyrestrictionsectors=this.mapToValues(e.optional_fields.hirearchyrestrictionsectors),e.optional_fields.hirearchyrestrictiongroups=this.mapToValues(e.optional_fields.hirearchyrestrictiongroups),e.optional_fields.hirearchyrestrictiondealerships=this.mapToValues(e.optional_fields.hirearchyrestrictiondealerships)):(e.optional_fields.hirearchyrestrictiondivisions=[],e.optional_fields.hirearchyrestrictionsectors=[],e.optional_fields.hirearchyrestrictiongroups=[],e.optional_fields.hirearchyrestrictiondealerships=[]),this.isEditing&&"enrol"in e&&delete e.enrol;const s=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(i=>!e[i]);return s.length>0?(this.showErrorMessage(`Campos obrigatórios ausentes: ${s.join(", ")}`),null):e},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processHirarchyRestrictionFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processHirarchyRestrictionFields(e){this.offerClass.optional_fields.hirearchyrestrictiondivisions=this.mapToOptions(e.hirearchyrestrictiondivisions),this.offerClass.optional_fields.hirearchyrestrictionsectors=this.mapToOptions(e.hirearchyrestrictionsectors),this.offerClass.optional_fields.hirearchyrestrictiongroups=this.mapToOptions(e.hirearchyrestrictiongroups),this.offerClass.optional_fields.hirearchyrestrictiondealerships=this.mapToOptions(e.hirearchyrestrictiondealerships)},processDateFields(e){e.enableenddate&&(this.offerClass.optional_fields.enableenddate=!0,this.offerClass.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.offerClass.optional_fields.enablepreenrolment=!0,this.offerClass.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.offerClass.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.offerClass.optional_fields.enableenrolperiod=!0,this.offerClass.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.offerClass.optional_fields.enrolperiod=null},processUserLimits(e){this.offerClass.optional_fields.minusers=e.minusers>0?e.minusers:null,this.offerClass.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.offerClass.optional_fields.roleid=e.roleid||null,this.offerClass.optional_fields.description=e.description||"",this.offerClass.optional_fields.modality=e.modality||null},processReenrolment(e){e.enablereenrol?(this.offerClass.optional_fields.enablereenrol=!0,this.offerClass.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.offerClass.optional_fields.reenrolmentsituations=e.reenrolmentsituations.map(t=>({label:"",value:t})))):this.offerClass.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.offerClass.optional_fields.enableextension=!0,this.processExtensionPeriods(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.offerClass.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.offerClass.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.offerClass.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},mapToOptions(e){return e.map(t=>({value:t,label:""}))},mapToValues(e){return e.map(t=>t.value)},resetExtensionFields(){this.offerClass.optional_fields.extensionperiod=null,this.offerClass.optional_fields.extensiondaysavailable=null,this.offerClass.optional_fields.extensionmaxrequests=null,this.extensionSituations=[]},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},navigateToBack(){this.offerCourse.offerid?this.$router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})}}},YI={class:"page-header-container"},XI={class:"d-flex flex-column mt-3"},eN={key:0,class:"text-muted"},tN={key:1,class:"text-muted"},sN={class:"actions-container"};function rN(e,t,s,i,n,a){const u=$("BackButton"),c=$("PageHeader"),h=$("Form"),_=$("CustomButton"),p=$("LFLoading"),g=$("Toast");return x(),D("div",{id:"create-edit-class",class:ce({"edit-class":a.isEditing&&!s.isReadonly,"view-class":s.isReadonly,"create-class":!a.isEditing})},[f("div",YI,[T(c,{title:a.isEditing?s.isReadonly?"Visualizar turma":`Editar turma: ${n.originalClassName??""}`:"Adicionar turma"},{actions:Ce(()=>[T(u,{onClick:a.navigateToBack},null,8,["onClick"])]),_:1},8,["title"])]),t[3]||(t[3]=f("div",{class:"section-container"},[f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS")],-1)),n.offerCourse?(x(),ht(h,{key:0,ref:"form",offerClass:n.offerClass,"onUpdate:offerClass":t[0]||(t[0]=v=>n.offerClass=v),offerCourse:n.offerCourse,isEditing:a.isEditing,isReadonly:s.isReadonly,onValidate:t[1]||(t[1]=v=>n.isValidForm=v)},null,8,["offerClass","offerCourse","isEditing","isReadonly"])):G("",!0),f("div",XI,[n.offerClass.creatorname?(x(),D("small",eN," Criado por "+q(n.offerClass.creatorname)+" em "+q(n.offerClass.createddate),1)):G("",!0),n.offerClass.modifiername?(x(),D("small",tN," Atualizado por "+q(n.offerClass.modifiername)+" em "+q(n.offerClass.modifieddate),1)):G("",!0)]),f("div",sN,[T(_,{variant:"primary",label:n.saving?"Salvando...":"Salvar",isLoading:n.saving,disabled:!n.isValidForm||s.isReadonly,onClick:t[2]||(t[2]=v=>a.isEditing?a.updateClass():a.createClass())},null,8,["label","isLoading","disabled"]),T(_,{variant:"secondary",label:"Cancelar",onClick:a.navigateToBack},null,8,["onClick"])]),t[4]||(t[4]=f("hr",null,null,-1)),t[5]||(t[5]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[We(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),T(p,{"is-loading":n.loading},null,8,["is-loading"]),T(g,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],2)}const wu=Pe(JI,[["render",rN],["__scopeId","data-v-4fb2f253"]]),nN="/local/offermanager/",oN=(()=>{const e=window.location.host,t=window.location.pathname,s=Lg.wwwroot.replace(/^https?\:\/\//i,"").replace(e,"").concat(nN);return t.includes("index.php")?s+"index.php":s})(),iN=[{path:"/",name:"offer.index",component:fw,meta:{title:"Gerenciar Ofertas"}},{path:"/offers/create",name:"offer.create",component:Cu,meta:{title:"Nova Oferta"}},{path:"/offers/:id/edit",name:"offer.edit",component:Cu,props:!0,meta:{title:"Editar Oferta"}},{path:"/offers/:id",name:"offer.show",component:Cu,props:{isReadonly:!0},meta:{title:"Visualizar Oferta"}},{path:"/offers/classes/create",name:"offer.class.create",component:wu,props:!0,meta:{title:"Nova Turma"}},{path:"/offers/classes/:offerClassId/edit",name:"offer.class.edit",component:wu,props:!0,meta:{title:"Editar Turma"}},{path:"/offers/classes/:offerClassId",name:"offer.class.show",component:wu,props:{isReadonly:!0},meta:{title:"Visualizar Turma"}},{path:"/enrollments/:classId",name:"Enrollments",component:gS,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],aa=Gb({history:nb(oN),routes:iN,scrollBehavior(){return{top:0}}});aa.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),aa.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&aa.push("/")});const V8="",aN=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{aN();const s=z1(Iy);return s.use(Oy()),s.use(aa),s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
