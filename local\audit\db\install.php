<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Post installation and migration code for local_audit plugin.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Function to run after the plugin has been installed.
 *
 * @return bool True on success
 */
function xmldb_local_audit_install()
{
    global $CFG;

    // Set default configuration values if not already set.
    if (get_config('local_audit', 'enableaudit') === false) {
        set_config('enableaudit', 1, 'local_audit');
    }

    if (get_config('local_audit', 'auditretention') === false) {
        set_config('auditretention', 365, 'local_audit');
    }

    return true;
}
