<template>
  <div
    id="create-edit-component"
    :class="{
      'edit-offer': isEditing && !isReadonly,
      'view-offer': isReadonly,
      'create-offer': !isEditing,
    }"
  >
    <PageHeader
      :title="
        isEditing
          ? isReadonly
            ? 'Visualizar oferta'
            : `Editar oferta: ${offer.name}`
          : 'Adicionar oferta'
      "
    >
      <template #actions>
        <BackButton @click="navigateToBack" />
      </template>
    </PageHeader>

    <Alert
      type="primary"
      icon="fas fa-exclamation-triangle"
      text="Para que uma instância de oferta seja ativada e disponibilize os cursos
      para os públicos-alvo configurados, é necessário garantir que pelo menos
      um curso, um grupo de público-alvo, e uma turma estejam configurados à
      instância de oferta."
    />

    <div class="section-container mt-3">
      <h2 class="section-title">CONFIGURAÇÕES GERAIS</h2>

      <OfferForm
        v-model:offer="offer"
        :offer="offer"
        :isEditing="isEditing"
        :isReadonly="isReadonly"
        @validate="isValidForm = $event"
      />
    </div>

    <div class="section-container" :class="{ 'no-title-section': !isEditing }">
      <h2 v-if="isEditing" class="section-title">CURSOS</h2>

      <div v-if="!isEditing" class="message-container">
        <div class="lock-message">
          <i class="fas fa-lock lock-icon"></i>
          <span>Salve a oferta primeiro para gerenciar os cursos</span>
        </div>
      </div>

      <OfferCourseTableList
        v-if="isEditing"
        :offerId="offer.id"
        :isReadonly="isReadonly"
      />
    </div>

    <div class="d-flex flex-column mt-3">
      <small class="text-muted" v-if="offer.creatorname">
        Criado por {{ offer.creatorname }} em {{ offer.createddate }}
      </small>
      <small class="text-muted" v-if="offer.modifiername">
        Atualizado por {{ offer.modifiername }} em
        {{ offer.modifieddate }}
      </small>
    </div>

    <div class="actions-container">
      <CustomButton
        variant="primary"
        label="Salvar"
        @click="showRequestSaveModal = true"
        :disabled="!isValidForm || isReadonly"
      />
      <CustomButton
        variant="secondary"
        label="Cancelar"
        @click="navigateToBack"
      />
    </div>

    <hr />
    <div class="required-fields-message">
      <div class="form-info">
        Este formulário contém campos obrigatórios marcados com
        <i class="fa fa-exclamation-circle text-danger"></i>
      </div>
    </div>

    <ConfirmationModal
      :show="showRequestSaveModal"
      size="md"
      title="Você tem certeza que deseja salvar as alterações feitas?"
      confirm-button-text="Salvar"
      cancel-button-text="Cancelar"
      @close="showRequestSaveModal = false"
      @confirm="isEditing ? updateOffer() : createOffer()"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import Toast from "@/components/Toast.vue";
import Alert from "@/components/Alert.vue";
import LFLoading from "@/components/LFLoading.vue";
import OfferForm from "@/components/offer/Form.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import CustomButton from "@/components/CustomButton.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import OfferCourseTableList from "@/components/offer-course/TableList.vue";

import ToastMessages from "@/mixins/toastMessages";
import { saveOffer, getOffer } from "@/services/offer";

export default {
  name: "CreateEdit",

  mixins: [ToastMessages],

  components: {
    Toast,
    Alert,
    LFLoading,
    OfferForm,
    PageHeader,
    BackButton,
    CustomButton,
    ConfirmationModal,
    OfferCourseTableList,
  },

  props: {
    isReadonly: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loading: false,

      offer: {
        id: null,
        name: "",
        type: "",
        description: "",
        status: false,
        audiences: [],
        creatorname: "",
        createddate: "",
        modifieddate: "",
        modifiername: "",
      },

      isEditing: false,

      isValidForm: false,

      showRequestSaveModal: false,
    };
  },

  /**
   * Lifecycle hook called when the component is created.
   */
  async created() {
    const id = parseInt(this.$route.params.id);

    if (id) {
      await this.getOffer(id);

      this.isEditing = true;
    }
  },

  methods: {
    /**
     * Retrieves and sets the offer details by ID.
     *
     * @param {number} id - The ID of the offer to retrieve.
     * @returns {Promise<void>}
     */
    async getOffer(id) {
      try {
        this.loading = true;

        const offer = await getOffer(id);

        this.offer = {
          ...offer,
          status: !!parseInt(offer.status),
        };
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Saves the offer by sending the offer data to the server.
     *
     * If the offer is created successfully, redirects to the offer edit page.
     *
     * @returns {Promise<void>}
     */
    async createOffer() {
      this.loading = true;
      this.showRequestSaveModal = false;

      if (!this.isValidForm) {
        this.loading = false;
        return;
      }

      try {
        const response = await saveOffer(this.offer);

        if (response.id) {
          const offerId = response.id;

          this.showSuccessMessage("Oferta criada com sucesso!");

          this.isEditing = true;

          this.$router.push({
            name: "offer.edit",
            params: {
              id: offerId,
            },
          });
        }
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Updates the offer by sending the offer data to the server.
     *
     * If the offer is updated successfully, shows a success message.
     *
     * @returns {Promise<void>}
     */
    async updateOffer() {
      this.loading = true;
      this.showRequestSaveModal = false;

      if (!this.isValidForm) {
        this.loading = false;
        return;
      }

      try {
        await saveOffer(this.offer);

        this.showSuccessMessage("Oferta atualizada com sucesso!");
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Redirects the user back to the list of offers.
     */
    navigateToBack() {
      this.$router.push({ name: "offer.index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.section-container {
  margin-bottom: 2rem;
  background-color: #212529;
  border-radius: 4px;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin: 2rem 0;
}

.message-container {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.lock-message {
  background: #2c3136;
  padding: 1rem 2rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #3a3f45;

  span {
    color: #f8f9fa;
    font-size: 0.95rem;
    font-weight: 500;
    letter-spacing: 0.2px;
  }
}

.lock-icon {
  font-size: 1.5rem;
  color: #ffc107; /* Amarelo mais vibrante para o ícone do cadeado */
  text-shadow: 0 0 5px rgba(255, 193, 7, 0.5); /* Efeito de brilho */
}

.no-title-section {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}
</style>
