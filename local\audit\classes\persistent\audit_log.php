<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Audit log persistent class.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_audit\persistent;

use core\persistent;

defined('MOODLE_INTERNAL') || die();

/**
 * Audit log persistent class.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class audit_log extends persistent
{

    /** The table name. */
    const TABLE = 'local_audit_log';

    /** Action constants. */
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';

    /**
     * Return the definition of the properties of this model.
     *
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'persistent' => [
                'type' => PARAM_TEXT,
                'description' => 'Name of the persistent class that was modified.',
            ],
            'recordid' => [
                'type' => PARAM_INT,
                'description' => 'ID of the record that was modified.',
            ],
            'action' => [
                'type' => PARAM_ALPHA,
                'description' => 'Action performed (create, update, delete).',
                'choices' => [self::ACTION_CREATE, self::ACTION_UPDATE, self::ACTION_DELETE],
            ],
            'newdata' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'description' => 'New data after the change (JSON format).',
            ],
            'olddata' => [
                'type' => PARAM_RAW,
                'null' => NULL_ALLOWED,
                'description' => 'Old data before the change (JSON format).',
            ],
            'userid' => [
                'type' => PARAM_INT,
                'description' => 'ID of the user who made the change.',
            ],
            'timecreated' => [
                'type' => PARAM_INT,
                'description' => 'Timestamp when the change was made.',
                'default' => function () {
                    return time();
                },
            ],
            'ipaddress' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'description' => 'IP address of the user who made the change.',
            ],
        ];
    }

    /**
     * Log an audit entry.
     *
     * @param string $persistent The persistent class name
     * @param int $recordid The record ID
     * @param string $action The action performed
     * @param array|object|null $newdata New data
     * @param array|object|null $olddata Old data
     * @param int|null $userid User ID (defaults to current user)
     * @param string|null $ipaddress IP address (defaults to current user's IP)
     * @return audit_log|null The created audit log entry or null if no changes
     */
    public static function log_action($persistent, $recordid, $action, $newdata = null, $olddata = null, $userid = null, $ipaddress = null)
    {
        global $USER;

        // Check if audit logging is enabled.
        if (!get_config('local_audit', 'enableaudit')) {
            return null;
        }

        // Set defaults.
        if ($userid === null) {
            $userid = isset($USER->id) ? $USER->id : 0;
        }
        if ($ipaddress === null) {
            $ipaddress = getremoteaddr();
        }

        // Get ignored fields (default + custom from persistent class).
        $ignored_fields = self::get_ignored_fields_for_comparison($persistent);

        // Process olddata first to establish key order.
        if ($olddata) {
            $olddata_array = (array) $olddata;

            // Remove ignored fields from comparison data.
            foreach ($ignored_fields as $field) {
                unset($olddata_array[$field]);
            }

            $olddata_array = array_map('strval', $olddata_array);
            $olddatajson = json_encode($olddata_array, JSON_UNESCAPED_UNICODE | JSON_FORCE_OBJECT);
        } else {
            $olddatajson = null;
            $olddata_array = [];
        }

        // Process newdata using olddata key order as reference.
        if ($newdata) {
            $newdata_array = (array) $newdata;

            // Remove ignored fields from comparison data.
            foreach ($ignored_fields as $field) {
                unset($newdata_array[$field]);
            }

            // Order newdata_array keys to match olddata_array order.
            if (!empty($olddata_array)) {
                $ordered_newdata = [];
                // First, add keys in the same order as olddata.
                foreach (array_keys($olddata_array) as $key) {
                    if (array_key_exists($key, $newdata_array)) {
                        $ordered_newdata[$key] = $newdata_array[$key];
                    }
                }
                // Then add any new keys that don't exist in olddata.
                foreach ($newdata_array as $key => $value) {
                    if (!array_key_exists($key, $ordered_newdata)) {
                        $ordered_newdata[$key] = $value;
                    }
                }
                $newdata_array = $ordered_newdata;
            } else {
                ksort($newdata_array);
            }

            $newdata_array = array_map('strval', $newdata_array);
            $newdatajson = json_encode($newdata_array, JSON_UNESCAPED_UNICODE | JSON_FORCE_OBJECT);
        } else {
            $newdatajson = null;
        }

        // For updates, check if data actually changed by comparing JSON.
        if ($action === self::ACTION_UPDATE && $newdatajson === $olddatajson) {
            return null; // No changes detected, don't create log entry.
        }

        // Create audit log entry.
        $auditlog = new self();
        $auditlog->set('persistent', $persistent);
        $auditlog->set('recordid', $recordid);
        $auditlog->set('action', $action);
        $auditlog->set('newdata', $newdatajson);
        $auditlog->set('olddata', $olddatajson);
        $auditlog->set('userid', $userid);
        $auditlog->set('ipaddress', $ipaddress);
        $auditlog->save();

        return $auditlog;
    }

    /**
     * Get audit logs for a specific persistent class and record.
     *
     * @param string $persistent The persistent class name
     * @param int $recordid The record ID
     * @param string|null $action Filter by action (optional)
     * @return audit_log[] Array of audit log entries
     */
    public static function get_logs_for_record($persistent, $recordid, $action = null)
    {
        $conditions = [
            'persistent' => $persistent,
            'recordid' => $recordid,
        ];

        if ($action !== null) {
            $conditions['action'] = $action;
        }

        return self::get_records($conditions, 'timecreated', 'DESC');
    }

    /**
     * Get audit logs for a specific user.
     *
     * @param int $userid The user ID
     * @param int $limit Limit number of results (optional)
     * @return audit_log[] Array of audit log entries
     */
    public static function get_logs_for_user($userid, $limit = 0)
    {
        return self::get_records(['userid' => $userid], 'timecreated', 'DESC', 0, $limit);
    }

    /**
     * Clean up old audit logs based on retention setting.
     *
     * @return int Number of records deleted
     */
    public static function cleanup_old_logs()
    {
        global $DB;

        $retention = get_config('local_audit', 'auditretention');
        if (!$retention || $retention <= 0) {
            return 0; // Keep logs indefinitely.
        }

        $cutoff = time() - ($retention * 24 * 60 * 60); // Convert days to seconds.
        return $DB->delete_records_select(self::TABLE, 'timecreated < ?', [$cutoff]);
    }

    /**
     * Get the new data as an array.
     *
     * @return array|null
     */
    public function get_new_data_array()
    {
        $newdata = $this->get('newdata');
        return $newdata ? json_decode($newdata, true) : null;
    }

    /**
     * Get the old data as an array.
     *
     * @return array|null
     */
    public function get_old_data_array()
    {
        $olddata = $this->get('olddata');
        return $olddata ? json_decode($olddata, true) : null;
    }

    /**
     * Get the user who made the change.
     *
     * @return \stdClass|null User record or null if not found
     */
    public function get_user()
    {
        global $DB;
        return $DB->get_record('user', ['id' => $this->get('userid')]);
    }

    /**
     * Get fields to ignore when comparing data changes.
     *
     * @param string $persistent The persistent class name
     * @return array Array of field names to ignore
     */
    private static function get_ignored_fields_for_comparison($persistent)
    {
        // Default fields to ignore.
        $ignored_fields = ['timecreated', 'usercreated', 'timemodified', 'usermodified'];

        // Check if the persistent class defines additional ignored fields.
        if (class_exists($persistent) && method_exists($persistent, 'define_audit_ignored_fields')) {
            $additional_fields = $persistent::define_audit_ignored_fields();
            if (is_array($additional_fields)) {
                $ignored_fields = array_merge($ignored_fields, $additional_fields);
            }
        }

        return array_unique($ignored_fields);
    }
}
