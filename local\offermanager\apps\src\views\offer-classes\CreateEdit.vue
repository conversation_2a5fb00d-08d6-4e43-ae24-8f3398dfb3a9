<template>
  <div
    id="create-edit-class"
    :class="{
      'edit-class': isEditing && !isReadonly,
      'view-class': isReadonly,
      'create-class': !isEditing,
    }"
  >
    <div class="page-header-container">
      <PageHeader
        :title="
          isEditing
            ? isReadonly
              ? 'Visualizar turma'
              : `Editar turma: ${originalClassName ?? ''}`
            : 'Adicionar turma'
        "
      >
        <template #actions>
          <BackButton @click="navigateToBack" />
        </template>
      </PageHeader>
    </div>

    <div class="section-container">
      <h2 class="section-title">CONFIGURAÇÕES GERAIS</h2>
    </div>

    <Form
      v-if="offerCourse"
      ref="form"
      v-model:offerClass="offerClass"
      :offerCourse="offerCourse"
      :isEditing="isEditing"
      :isReadonly="isReadonly"
      @validate="isValidForm = $event"
    />

    <div class="d-flex flex-column mt-3">
      <small class="text-muted" v-if="offerClass.creatorname">
        Criado por {{ offerClass.creatorname }} em {{ offerClass.createddate }}
      </small>
      <small class="text-muted" v-if="offerClass.modifiername">
        Atualizado por {{ offerClass.modifiername }} em
        {{ offerClass.modifieddate }}
      </small>
    </div>

    <div class="actions-container">
      <CustomButton
        variant="primary"
        :label="saving ? 'Salvando...' : 'Salvar'"
        :isLoading="saving"
        :disabled="!isValidForm || isReadonly"
        @click="showRequestSaveModal = true"
      />
      <CustomButton
        variant="secondary"
        label="Cancelar"
        @click="navigateToBack"
      />
    </div>

    <hr />
    <div class="required-fields-message">
      <div class="form-info">
        Este formulário contém campos obrigatórios marcados com
        <i class="fa fa-exclamation-circle text-danger"></i>
      </div>
    </div>

    <ConfirmationModal
      :show="showRequestSaveModal"
      size="md"
      title="Você tem certeza que deseja salvar as alterações feitas?"
      confirm-button-text="Salvar"
      cancel-button-text="Cancelar"
      @close="showRequestSaveModal = false"
      @confirm="isEditing ? updateClass() : createClass()"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { addClass, updateClass, getClass, getCourse } from "@/services/offer";

import Toast from "@/components/Toast.vue";
import LFLoading from "@/components/LFLoading.vue";
import Form from "@/components/offer-class/Form.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import CustomButton from "@/components/CustomButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";

import ToastMessages from "@/mixins/toastMessages";

export default {
  name: "CreateEdit",

  mixins: [ToastMessages],

  components: {
    Form,
    Toast,
    LFLoading,
    PageHeader,
    BackButton,
    CustomButton,
    Autocomplete,
    ConfirmationModal,
  },

  props: {
    isReadonly: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      saving: false,
      loading: false,

      originalClassName: "",

      offerClass: {
        enrol: "",
        offercourseid: null,
        classname: "",
        startdate: "",
        teachers: [],
        optional_fields: {
          enableenddate: false,
          enddate: "",
          enablepreenrolment: false,
          preenrolmentstartdate: "",
          preenrolmentenddate: "",
          description: "",
          enableenrolperiod: false,
          enrolperiod: null,
          minusers: null,
          maxusers: null,
          roleid: null,
          modality: null,
          maxusersdealership: null,
          enablereenrol: false,
          reenrolmentsituations: [],
          enableextension: false,
          extensionperiod: null,
          extensiondaysavailable: null,
          extensionmaxrequests: null,
          enablehirearchyrestriction: false,
          hirearchyrestrictiondivisions: [],
          hirearchyrestrictionsectors: [],
          hirearchyrestrictiongroups: [],
          hirearchyrestrictiondealerships: [],
        },
        creatorname: "",
        createddate: "",
        modifieddate: "",
        modifiername: "",
      },
      offerCourse: null,

      isValidForm: false,

      showRequestSaveModal: false,
    };
  },

  async created() {
    if (this.isEditing) {
      await this.getOfferClass();
    }

    if (this.offerCourseId) {
      await this.getOfferCourse();
    }
  },

  computed: {
    isEditing() {
      return !!this.offerClassId;
    },

    offerClassId() {
      return parseInt(this.$route.params.offerClassId);
    },

    offerCourseId() {
      return this.$route.query.offerCourseId || this.offerClass?.offercourseid;
    },
  },

  methods: {
    /**
     * Gets offer class data
     */
    async getOfferClass() {
      try {
        this.loading = true;

        const response = await getClass(parseInt(this.offerClassId));

        this.offerClass = response;

        this.offerClass.offercourseid = parseInt(this.offerCourseId);

        this.offerClass.teachers = this.offerClass.teachers.map((item) => ({
          value: item.id,
          label: item.fullname,
        }));

        if (response.optional_fields) {
          this.processOptionalFields(response.optional_fields);
        }

        this.originalClassName = this.offerClass.classname;
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Gets offer course data
     */
    async getOfferCourse() {
      try {
        this.loading = true;

        const response = await getCourse(this.offerCourseId);

        this.offerCourse = response;
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Creates a new offer class
     *
     * If the class is created successfully, redirects to the edit class page
     *
     * @returns {Promise<void>}
     */
    async createClass() {
      try {
        this.showRequestSaveModal = false;

        if (!this.isValidForm) {
          return;
        }

        this.saving = true;

        const classData = this.prepareClassData(false);

        if (!classData) {
          return;
        }

        let response = await addClass(classData);

        this.showSuccessMessage(response.message);

        this.$router.push({
          name: "offer.class.edit",
          params: {
            offerCourseId: this.offerCourse.id,
            offerClassId: response.offerclassid,
          },
        });
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.saving = false;
      }
    },

    /**
     * Updates an existing offer class
     *
     * If the class is updated successfully, reloads the class data
     *
     * @returns {Promise<void>}
     */
    async updateClass() {
      try {
        this.showRequestSaveModal = false;

        if (!this.isValidForm) {
          return;
        }

        this.saving = true;

        const classData = this.prepareClassData(true);

        if (!classData) {
          return;
        }

        classData.offerclassid = this.offerClassId;

        let response = await updateClass(classData);

        this.showSuccessMessage(response.message);

        this.isValidForm = false;

        this.getOfferClass();
      } catch (error) {
        console.log(error);
        this.showErrorMessage(error);
      } finally {
        this.saving = false;
      }
    },

    /**
     * Prepares the offer class data for creation or update
     *
     * @returns {Object|null} - Offer class data or null if validation fails
     */
    prepareClassData() {
      const classData = cloneDeep(this.offerClass);

      // Converte offercourseid para inteiro
      classData.offercourseid = parseInt(this.offerCourseId);

      // Mapeia os professores selecionados
      classData.teachers = classData.teachers.map((t) => t.value);

      // Processa campos de extensão
      if (
        !classData.optional_fields.enableextension ||
        !classData.optional_fields.enableenrolperiod
      ) {
        classData.optional_fields.extensionperiod = undefined;
        classData.optional_fields.extensiondaysavailable = undefined;
        classData.optional_fields.extensionmaxrequests = undefined;

        if (!classData.optional_fields.enableenrolperiod) {
          classData.optional_fields.enableextension = false;
        }
      }

      // Processa prazo de conclusão
      if (!classData.optional_fields.enableenrolperiod) {
        classData.optional_fields.enrolperiod = undefined;
      }

      // Processa rematrícula
      if (!classData.optional_fields.enablereenrol) {
        classData.optional_fields.reenrolmentsituations = [];
      } else {
        classData.optional_fields.reenrolmentsituations =
          classData.optional_fields.reenrolmentsituations.map(
            (item) => item.value
          );
      }

      // Processa restrições hierárquicas
      if (!classData.optional_fields.enablehirearchyrestriction) {
        classData.optional_fields.hirearchyrestrictiondivisions = [];
        classData.optional_fields.hirearchyrestrictionsectors = [];
        classData.optional_fields.hirearchyrestrictiongroups = [];
        classData.optional_fields.hirearchyrestrictiondealerships = [];
      } else {
        classData.optional_fields.hirearchyrestrictiondivisions =
          this.mapToValues(
            classData.optional_fields.hirearchyrestrictiondivisions
          );
        classData.optional_fields.hirearchyrestrictionsectors =
          this.mapToValues(
            classData.optional_fields.hirearchyrestrictionsectors
          );
        classData.optional_fields.hirearchyrestrictiongroups = this.mapToValues(
          classData.optional_fields.hirearchyrestrictiongroups
        );
        classData.optional_fields.hirearchyrestrictiondealerships =
          this.mapToValues(
            classData.optional_fields.hirearchyrestrictiondealerships
          );
      }

      // Remove campo enrol para atualizações
      if (this.isEditing && "enrol" in classData) {
        delete classData.enrol;
      }

      delete classData.createddate;
      delete classData.creatorname;
      delete classData.modifieddate;
      delete classData.modifiername;

      // Define campos obrigatórios baseado na operação
      const requiredFields = this.isEditing
        ? ["offercourseid", "classname", "startdate"]
        : ["offercourseid", "classname", "startdate", "enrol"];

      const missingFields = requiredFields.filter((field) => !classData[field]);

      if (missingFields.length > 0) {
        this.showErrorMessage(
          `Campos obrigatórios ausentes: ${missingFields.join(", ")}`
        );
        return null;
      }

      return classData;
    },

    /**
     * Processa os campos opcionais da turma
     */
    processOptionalFields(optionalFields) {
      this.processDateFields(optionalFields);
      this.processEnrolmentFields(optionalFields);
      this.processHirarchyRestrictionFields(optionalFields);
      this.processUserLimits(optionalFields);
      this.processDescriptionAndRole(optionalFields);
      this.processReenrolment(optionalFields);
      this.processExtensionFields(optionalFields);
    },

    processHirarchyRestrictionFields(fields) {
      this.offerClass.optional_fields.hirearchyrestrictiondivisions =
        this.mapToOptions(fields.hirearchyrestrictiondivisions);
      this.offerClass.optional_fields.hirearchyrestrictionsectors =
        this.mapToOptions(fields.hirearchyrestrictionsectors);
      this.offerClass.optional_fields.hirearchyrestrictiongroups =
        this.mapToOptions(fields.hirearchyrestrictiongroups);
      this.offerClass.optional_fields.hirearchyrestrictiondealerships =
        this.mapToOptions(fields.hirearchyrestrictiondealerships);
    },
    /**
     * Processa campos relacionados a datas
     */
    processDateFields(fields) {
      // Data de fim
      if (fields.enableenddate) {
        this.offerClass.optional_fields.enableenddate = true;
        this.offerClass.optional_fields.enddate = fields.enddate || null;
      }

      // Pré-inscrição
      if (fields.enablepreenrolment) {
        this.offerClass.optional_fields.enablepreenrolment = true;
        this.offerClass.optional_fields.preenrolmentstartdate =
          fields.preenrolmentstartdate || null;
        this.offerClass.optional_fields.preenrolmentenddate =
          fields.preenrolmentenddate || null;
      }
    },

    /**
     * Processa campos de matrícula
     */
    processEnrolmentFields(fields) {
      // Prazo de conclusão
      if (fields.enableenrolperiod) {
        this.offerClass.optional_fields.enableenrolperiod = true;
        this.offerClass.optional_fields.enrolperiod =
          fields.enrolperiod > 0 ? fields.enrolperiod : null;
      } else {
        this.offerClass.optional_fields.enrolperiod = null;
      }
    },

    /**
     * Processa limites de usuários
     */
    processUserLimits(fields) {
      // Vagas mínimas e máximas
      this.offerClass.optional_fields.minusers =
        fields.minusers > 0 ? fields.minusers : null;
      this.offerClass.optional_fields.maxusers =
        fields.maxusers > 0 ? fields.maxusers : null;
    },

    /**
     * Processa descrição e papel padrão
     */
    processDescriptionAndRole(fields) {
      this.offerClass.optional_fields.roleid = fields.roleid || null;
      this.offerClass.optional_fields.description = fields.description || "";
      this.offerClass.optional_fields.modality = fields.modality || null;
    },

    /**
     * Processa rematrícula
     */
    processReenrolment(fields) {
      if (fields.enablereenrol) {
        this.offerClass.optional_fields.enablereenrol = true;
        this.offerClass.optional_fields.reenrolmentsituations =
          fields.reenrolmentsituations || [];

        if (Array.isArray(fields.reenrolmentsituations)) {
          this.offerClass.optional_fields.reenrolmentsituations =
            fields.reenrolmentsituations.map((id) => {
              return {
                label: "",
                value: id,
              };
            });
        }
      } else {
        this.offerClass.optional_fields.reenrolmentsituations = [];
      }
    },

    /**
     * Processa campos de prorrogação
     */
    processExtensionFields(fields) {
      if (fields.enableextension && fields.enableenrolperiod) {
        this.offerClass.optional_fields.enableextension = true;
        this.processExtensionPeriods(fields);
      } else {
        this.resetExtensionFields();
      }
    },

    /**
     * Processa períodos de prorrogação
     */
    processExtensionPeriods(fields) {
      this.offerClass.optional_fields.extensionperiod =
        fields.extensionperiod > 0 ? fields.extensionperiod : null;
      this.offerClass.optional_fields.extensiondaysavailable =
        fields.extensiondaysavailable > 0
          ? fields.extensiondaysavailable
          : null;
      this.offerClass.optional_fields.extensionmaxrequests =
        fields.extensionmaxrequests > 0 ? fields.extensionmaxrequests : null;
    },

    mapToOptions(array) {
      return array.map((item) => ({ value: item, label: "" }));
    },

    mapToValues(array) {
      return array.map((item) => item.value);
    },
    /**
     * Reseta campos de prorrogação
     */
    resetExtensionFields() {
      this.offerClass.optional_fields.extensionperiod = null;
      this.offerClass.optional_fields.extensiondaysavailable = null;
      this.offerClass.optional_fields.extensionmaxrequests = null;
      this.extensionSituations = [];
    },

    /**
     * Atualiza um campo de seleção
     */
    updateSelectField(ref, value) {
      if (this.$refs[ref]) {
        this.$refs[ref].value = value;
        const event = new Event("change");
        this.$refs[ref].$el.dispatchEvent(event);
      }
    },

    /**
     * Atualiza um campo de input
     */
    updateInputField(ref, value) {
      if (this.$refs[ref] && value) {
        this.$refs[ref].value = value;
        const event = new Event("input");
        this.$refs[ref].$el.dispatchEvent(event);
      }
    },

    /**
     * Volta para tela da oferta
     */
    navigateToBack() {
      if (this.offerCourse.offerid) {
        this.$router.push({
          name: "offer.edit",
          params: { id: this.offerCourse.offerid },
        });
      } else {
        this.router.push({ name: "offer.index" });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.new-class {
  margin-bottom: 2rem;
}

.validation-alert {
  background-color: #332701;
  border: 1px solid #997404;
  color: #ffda6a;
  padding: 1rem;
  margin: 0 1rem 1rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  i {
    font-size: 1.25rem;
  }

  span {
    flex: 1;
  }
}

.section-container {
  margin: 0;
  background-color: #212529;
  border-radius: 4px;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
</style>
