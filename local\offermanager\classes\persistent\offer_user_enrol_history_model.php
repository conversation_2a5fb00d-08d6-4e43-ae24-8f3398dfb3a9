<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use moodle_exception;
use local_audit\traits\audit_trait;

defined('MOODLE_INTERNAL') || die();

/**
 * Represents a record in the local_offermanager_ueh table.
 *
 * This class models the historical copy of a user's enrolment (user_enrolments).
 *
 * @package    local_offermanager
 * @copyright  2025 Your Name/Organisation
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 *
 * @property int $id The original ID of the user enrolment record (user_enrolments).
 * @property int $status The status of the record (0=inactive, 1=active).
 * @property int $enrolid The ID of the enrolment instance (enrol).
 * @property int $userid The ID of the enrolled user (user).
 * @property int|null $timestart UNIX Timestamp of the enrolment start date.
 * @property int|null $timeend UNIX Timestamp of the enrolment end date.
 * @property int|null $modifierid The ID of the user who last modified the original enrolment.
 * @property int|null $timecreated UNIX Timestamp of the original creation time.
 * @property int|null $timemodified UNIX Timestamp of the original modification time.
 */
class offer_user_enrol_history_model extends persistent
{
    use audit_trait;

    /** @var string The name of the database table this persistent maps to. */
    const TABLE = 'local_offermanager_ueh';

    /**
     * Define the properties of this persistent class.
     *
     * @return array The property definitions.
     */
    protected static function define_properties()
    {
        return [
            'id' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID original da matrícula (mesmo da tabela user_enrolments).',
                'primary' => true, // Define 'id' as primary key, as it's not auto-incrementing.
            ],
            'status' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'default' => 1,
                'choices' => [0, 1],
                'description' => 'Status do registro (0=inativo, 1=ativo).',
            ],
            'enrolid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID da instância de inscrição (enrol).',
            ],
            'userid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID do usuário inscrito (usuário).',
            ],
            'timestart' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => 0,
                'description' => 'Data de início da matrícula.',
            ],
            'timeend' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => 0,
                'description' => 'Data de término da matrícula.',
            ],
            'modifierid' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'description' => 'ID do usuário que modificou.',
            ],
            'timecreated' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => 0,
                'description' => 'Quando foi criado.',
            ],
            'timemodified' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => 0,
                'description' => 'Quando foi modificado.',
            ],
        ];
    }

    /**
     * Validates the enrolid field.
     *
     * @param int $value The enrolment ID to validate.
     * @return bool Returns true if valid or throws an exception.
     */
    protected function validate_enrolid(int $value)
    {
        global $DB;
        if (!$value || !$DB->record_exists('enrol', ['id' => $value])) {
            throw new moodle_exception('error:enrol_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Validates the userid field.
     *
     * @param int $value The user ID to validate.
     * @return bool Returns true if valid or throws an exception.
     */
    protected function validate_userid(int $value)
    {
        if (!$value || !\core_user::get_user($value)) {
            throw new moodle_exception('error:user_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Validates the modifierid field.
     *
     * @param int|null $value The modifier user ID to validate.
     * @return bool Returns true if valid or throws an exception.
     */
    protected function validate_modifierid(?int $value)
    {
        if ($value !== null && !\core_user::get_user($value)) {
            throw new moodle_exception('error:user_not_found', 'local_offermanager');
        }
        return true;
    }
}