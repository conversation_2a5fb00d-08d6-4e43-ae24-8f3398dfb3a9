<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language strings for local_audit plugin.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'Local Audit';
$string['audit'] = 'Audit';
$string['auditlog'] = 'Audit Log';
$string['auditlogs'] = 'Audit Logs';

// Settings
$string['settings'] = 'Audit Settings';
$string['enableaudit'] = 'Enable audit logging';
$string['enableaudit_desc'] = 'Enable or disable audit logging for all persistent classes using the audit trait. Only logs changes when data actually changes.';
$string['auditretention'] = 'Audit log retention (days)';
$string['auditretention_desc'] = 'Number of days to keep audit logs. Set to 0 to keep logs indefinitely.';

// Audit actions
$string['action_create'] = 'Create';
$string['action_update'] = 'Update';
$string['action_delete'] = 'Delete';

// Table fields
$string['id'] = 'ID';
$string['persistent'] = 'Persistent Class';
$string['recordid'] = 'Record ID';
$string['action'] = 'Action';
$string['newdata'] = 'New Data';
$string['olddata'] = 'Old Data';
$string['userid'] = 'User ID';
$string['timecreated'] = 'Time Created';
$string['ipaddress'] = 'IP Address';

// Tasks
$string['cleanupauditlogs'] = 'Clean up old audit logs';

// Privacy
$string['privacy:metadata:local_audit_log'] = 'Audit log table that stores changes made to persistent objects.';
$string['privacy:metadata:local_audit_log:persistent'] = 'The name of the persistent class that was modified.';
$string['privacy:metadata:local_audit_log:recordid'] = 'The ID of the record that was modified.';
$string['privacy:metadata:local_audit_log:action'] = 'The action that was performed (create, update, delete).';
$string['privacy:metadata:local_audit_log:newdata'] = 'The new data after the change.';
$string['privacy:metadata:local_audit_log:olddata'] = 'The old data before the change.';
$string['privacy:metadata:local_audit_log:userid'] = 'The ID of the user who made the change.';
$string['privacy:metadata:local_audit_log:timecreated'] = 'The time when the change was made.';
$string['privacy:metadata:local_audit_log:ipaddress'] = 'The IP address of the user who made the change.';
