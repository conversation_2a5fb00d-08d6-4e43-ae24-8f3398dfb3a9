<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings de idioma para o plugin local_audit.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'Auditoria';
$string['audit'] = 'Auditoria';
$string['auditlog'] = 'Log de Auditoria';
$string['auditlogs'] = 'Logs de Auditoria';

// Configurações
$string['settings'] = 'Configurações de Auditoria';
$string['enableaudit'] = 'Habilitar log de auditoria';
$string['enableaudit_desc'] = 'Habilitar ou desabilitar o log de auditoria para todas as classes persistent que usam a trait de auditoria.';
$string['auditretention'] = 'Retenção do log de auditoria (dias)';
$string['auditretention_desc'] = 'Número de dias para manter os logs de auditoria. Defina como 0 para manter os logs indefinidamente.';

// Ações de auditoria
$string['action_create'] = 'Criar';
$string['action_update'] = 'Atualizar';
$string['action_delete'] = 'Excluir';

// Campos da tabela
$string['id'] = 'ID';
$string['persistent'] = 'Classe Persistent';
$string['recordid'] = 'ID do Registro';
$string['action'] = 'Ação';
$string['newdata'] = 'Novos Dados';
$string['olddata'] = 'Dados Antigos';
$string['userid'] = 'ID do Usuário';
$string['timecreated'] = 'Data de Criação';
$string['ipaddress'] = 'Endereço IP';

// Tarefas
$string['cleanupauditlogs'] = 'Limpar logs de auditoria antigos';

// Privacidade
$string['privacy:metadata:local_audit_log'] = 'Tabela de log de auditoria que armazena alterações feitas em objetos persistent.';
$string['privacy:metadata:local_audit_log:persistent'] = 'O nome da classe persistent que foi modificada.';
$string['privacy:metadata:local_audit_log:recordid'] = 'O ID do registro que foi modificado.';
$string['privacy:metadata:local_audit_log:action'] = 'A ação que foi executada (criar, atualizar, excluir).';
$string['privacy:metadata:local_audit_log:newdata'] = 'Os novos dados após a alteração.';
$string['privacy:metadata:local_audit_log:olddata'] = 'Os dados antigos antes da alteração.';
$string['privacy:metadata:local_audit_log:userid'] = 'O ID do usuário que fez a alteração.';
$string['privacy:metadata:local_audit_log:timecreated'] = 'O horário em que a alteração foi feita.';
$string['privacy:metadata:local_audit_log:ipaddress'] = 'O endereço IP do usuário que fez a alteração.';
