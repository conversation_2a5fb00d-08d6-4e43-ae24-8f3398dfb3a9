<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON>odle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Scheduled task for cleaning up old audit logs.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_audit\task;

use local_audit\persistent\audit_log;

defined('MOODLE_INTERNAL') || die();

/**
 * Scheduled task for cleaning up old audit logs.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class cleanup_audit_logs extends \core\task\scheduled_task {

    /**
     * Get a descriptive name for this task (shown to admins).
     *
     * @return string
     */
    public function get_name() {
        return get_string('cleanupauditlogs', 'local_audit');
    }

    /**
     * Execute the task.
     */
    public function execute() {
        $deleted = audit_log::cleanup_old_logs();
        
        if ($deleted > 0) {
            mtrace("Cleaned up {$deleted} old audit log entries.");
        } else {
            mtrace("No old audit log entries to clean up.");
        }
    }
}
