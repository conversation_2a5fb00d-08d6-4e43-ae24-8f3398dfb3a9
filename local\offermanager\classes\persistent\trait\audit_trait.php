<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait audit_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait audit_trait
{

    /**
     * Gets the user who created the offer class.
     *
     * @return stdClass The user record of the creator.
     */
    public function get_creator()
    {
        global $DB;
        return $DB->get_record('user', ['id' => $this->get('usercreated')]);
    }

    /**
     * Gets the full name of the user who created the offer class.
     *
     * @return string The full name of the creator.
     */

    public function get_creator_name()
    {
        $user = $this->get_creator();
        return fullname($user);
    }

    /**
     * Gets the user who last modified the offer class.
     *
     * @return stdClass The user record of the modifier.
     */
    public function get_modifier()
    {
        global $DB;
        return $DB->get_record('user', ['id' => $this->get('usermodified')]);
    }

    /**
     * Gets the full name of the user who last modified the offer class.
     *
     * @return string The full name of the modifier.
     */
    public function get_modifier_name()
    {
        $user = $this->get_modifier();
        return fullname($user);
    }
}
