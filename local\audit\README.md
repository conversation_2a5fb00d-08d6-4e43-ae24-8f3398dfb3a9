# Local Audit Plugin

Plugin local para auditoria de alterações em classes persistent do Moodle.

## Descrição

O plugin `local_audit` é responsável por salvar logs de alterações realizadas em classes persistent. Ele fornece uma classe persistent para armazenar os logs e uma trait que pode ser usada por outros plugins para registrar automaticamente as alterações.

## Funcionalidades

- **Logging automático**: Registra automaticamente operações de criação, edição e remoção em classes persistent que usam a trait de auditoria
- **Detecção inteligente de mudanças**: Só salva logs quando há alterações reais nos dados, evitando logs desnecessários
- **Armazenamento detalhado**: Salva dados antigos e novos, usuário responsável, timestamp e endereço IP
- **Configuração flexível**: Permite habilitar/desabilitar o logging e configurar período de retenção
- **Campos ignorados**: Ignora automaticamente campos como `timemodified` e `usermodified` na comparação
- **Limpeza automática**: Tarefa agendada para remover logs antigos baseado no período de retenção configurado

## Estrutura do Banco de Dados

A tabela `local_audit_log` contém os seguintes campos:

- `id`: ID da linha (chave primária)
- `persistent`: Classe persistent que está sendo alterada
- `recordid`: ID do registro alterado
- `action`: Ação realizada (create, update, delete)
- `newdata`: Novos dados (formato JSON)
- `olddata`: Dados antigos (formato JSON)
- `userid`: Usuário que fez a alteração
- `timecreated`: Timestamp da criação
- `ipaddress`: IP do usuário

## Como Usar

### 1. Usando a Trait em Classes Persistent

Para habilitar auditoria automática em uma classe persistent, adicione a trait:

```php
<?php
use local_audit\traits\audit_trait;

class minha_classe extends \core\persistent {
    use audit_trait;

    const TABLE = 'minha_tabela';

    // Opcionalmente, defina campos adicionais a serem ignorados na comparação
    protected function define_audit_ignored_fields() {
        return ['campo_calculado', 'timestamp_interno'];
    }

    // ... resto da implementação
}
```

**Detecção Inteligente de Mudanças:**
- A trait automaticamente compara os dados antes e depois da alteração
- Só registra logs quando há mudanças reais nos dados
- Ignora campos como `timemodified` e `usermodified` por padrão
- Permite definir campos adicionais a serem ignorados

### 2. Logging Manual

Para registrar logs manualmente (para classes que não são persistent):

```php
<?php
use local_audit\persistent\audit_log;

// Registrar uma criação
audit_log::log_action('minha_classe', $recordid, audit_log::ACTION_CREATE, $newdata);

// Registrar uma atualização
audit_log::log_action('minha_classe', $recordid, audit_log::ACTION_UPDATE, $newdata, $olddata);

// Registrar uma remoção
audit_log::log_action('minha_classe', $recordid, audit_log::ACTION_DELETE, null, $olddata);
```

### 3. Consultando Logs de Auditoria

```php
<?php
use local_audit\persistent\audit_log;

// Obter todos os logs de um registro específico
$logs = audit_log::get_logs_for_record('minha_classe', $recordid);

// Obter logs de um usuário específico
$logs = audit_log::get_logs_for_user($userid);

// Usando métodos da trait (em classes que usam audit_trait)
$objeto = new minha_classe($id);
$logs = $objeto->get_audit_logs();
$creation_log = $objeto->get_creation_audit_log();
$update_logs = $objeto->get_update_audit_logs();
```

## Configurações

O plugin oferece as seguintes configurações no painel administrativo:

- **Habilitar logging de auditoria**: Liga/desliga o sistema de auditoria
- **Período de retenção**: Número de dias para manter os logs (0 = indefinido)

## Tarefas Agendadas

O plugin inclui uma tarefa agendada que executa diariamente às 2:00 AM para limpar logs antigos baseado no período de retenção configurado.

## Instalação

1. Copie o plugin para o diretório `local/audit/`
2. Execute a atualização do banco de dados através do painel administrativo
3. Configure as opções desejadas em "Administração do site > Plugins > Plugins locais > Local Audit"

## Requisitos

- Moodle 4.1 ou superior
- PHP 7.4 ou superior

## Licença

Este plugin é licenciado sob a GNU GPL v3 ou posterior.

## Suporte

Para suporte e questões, entre em contato com a equipe REVVO.
