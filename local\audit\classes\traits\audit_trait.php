<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON>od<PERSON>.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Audit trait for persistent classes.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_audit\traits;

use local_audit\persistent\audit_log;

defined('MOODLE_INTERNAL') || die();

/**
 * Audit trait for persistent classes.
 *
 * This trait can be used by any persistent class to automatically log
 * create, update, and delete operations.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait audit_trait
{

    /** @var array Original data before changes (for update operations) */
    private $audit_original_data = null;

    /** @var array Fields to ignore when comparing data changes */
    private $audit_ignored_fields = ['timemodified', 'usermodified'];

    /**
     * Hook to execute after a create operation.
     * Logs the creation of the record.
     */
    protected function after_create()
    {
        parent::after_create();
        $this->log_audit_action(audit_log::ACTION_CREATE, $this->to_record());
    }

    /**
     * Hook to execute before an update operation.
     * Stores the original data for comparison.
     */
    protected function before_update()
    {
        parent::before_update();
        // Store original data before update.
        $this->audit_original_data = $this->get_original_data();

        // Allow classes to define additional ignored fields.
        if (method_exists($this, 'define_audit_ignored_fields')) {
            $additional_fields = $this->define_audit_ignored_fields();
            if (is_array($additional_fields)) {
                $this->set_audit_ignored_fields($additional_fields);
            }
        }
    }

    /**
     * Hook to execute after an update operation.
     * Logs the update of the record with old and new data.
     *
     * @param bool $result Whether the update was successful
     */
    protected function after_update($result)
    {
        parent::after_update($result);

        if ($result && $this->has_data_changed()) {
            $this->log_audit_action(
                audit_log::ACTION_UPDATE,
                $this->to_record(),
                $this->audit_original_data
            );
        }

        // Clear stored original data.
        $this->audit_original_data = null;
    }

    /**
     * Hook to execute before a delete operation.
     * Stores the current data for logging.
     */
    protected function before_delete()
    {
        parent::before_delete();
        // Store current data before deletion.
        $this->audit_original_data = $this->to_record();
    }

    /**
     * Hook to execute after a delete operation.
     * Logs the deletion of the record.
     *
     * @param bool $result Whether the deletion was successful
     */
    protected function after_delete($result)
    {
        parent::after_delete($result);

        if ($result) {
            $this->log_audit_action(
                audit_log::ACTION_DELETE,
                null,
                $this->audit_original_data
            );
        }

        // Clear stored original data.
        $this->audit_original_data = null;
    }

    /**
     * Log an audit action.
     *
     * @param string $action The action performed
     * @param object|array|null $newdata New data after the change
     * @param object|array|null $olddata Old data before the change
     */
    private function log_audit_action($action, $newdata = null, $olddata = null)
    {
        // Get the persistent class name.
        $persistent = get_class($this);

        // Get the record ID.
        $recordid = $this->get('id');

        // Skip logging if no ID (shouldn't happen in normal operations).
        if (!$recordid) {
            return;
        }

        // Log the action.
        audit_log::log_action($persistent, $recordid, $action, $newdata, $olddata);
    }

    /**
     * Get the original data from the database.
     *
     * @return object|null The original record data
     */
    private function get_original_data()
    {
        global $DB;

        $recordid = $this->get('id');
        if (!$recordid) {
            return null;
        }

        return $DB->get_record($this::TABLE, ['id' => $recordid]);
    }

    /**
     * Check if data has actually changed by comparing current data with original data.
     *
     * @return bool True if data has changed, false otherwise
     */
    private function has_data_changed()
    {
        if (!$this->audit_original_data) {
            return false; // No original data to compare.
        }

        $current_data = $this->to_record();
        $original_data = $this->audit_original_data;

        // Convert objects to arrays for easier comparison.
        $current_array = (array) $current_data;
        $original_array = (array) $original_data;

        // Remove ignored fields from comparison.
        foreach ($this->audit_ignored_fields as $field) {
            unset($current_array[$field]);
            unset($original_array[$field]);
        }

        // Normalize data types by converting all values to strings.
        $current_array = array_map('strval', $current_array);
        $original_array = array_map('strval', $original_array);

        // Sort arrays by key to ensure consistent comparison regardless of order.
        ksort($current_array);
        // ksort($original_array);

        // Compare the arrays.
        return $current_array !== $original_array;
    }

    /**
     * Set fields to ignore when comparing data changes.
     *
     * @param array $fields Array of field names to ignore
     */
    protected function set_audit_ignored_fields(array $fields)
    {
        $this->audit_ignored_fields = array_merge(['timemodified', 'usermodified'], $fields);
    }

    /**
     * Get the list of fields ignored during audit comparison.
     *
     * @return array Array of ignored field names
     */
    protected function get_audit_ignored_fields()
    {
        return $this->audit_ignored_fields;
    }

    /**
     * Get audit logs for this record.
     *
     * @param string|null $action Filter by action (optional)
     * @return audit_log[] Array of audit log entries
     */
    public function get_audit_logs($action = null)
    {
        $persistent = get_class($this);
        $recordid = $this->get('id');

        if (!$recordid) {
            return [];
        }

        return audit_log::get_logs_for_record($persistent, $recordid, $action);
    }

    /**
     * Check if audit logging is enabled for this class.
     *
     * @return bool True if audit logging is enabled
     */
    protected function is_audit_enabled()
    {
        return get_config('local_audit', 'enableaudit');
    }

    /**
     * Get the last audit log entry for this record.
     *
     * @return audit_log|null The last audit log entry or null if none found
     */
    public function get_last_audit_log()
    {
        $logs = $this->get_audit_logs();
        return !empty($logs) ? reset($logs) : null;
    }

    /**
     * Get audit logs for a specific action on this record.
     *
     * @param string $action The action to filter by
     * @return audit_log[] Array of audit log entries
     */
    public function get_audit_logs_by_action($action)
    {
        return $this->get_audit_logs($action);
    }

    /**
     * Get the creation audit log for this record.
     *
     * @return audit_log|null The creation audit log or null if not found
     */
    public function get_creation_audit_log()
    {
        $logs = $this->get_audit_logs_by_action(audit_log::ACTION_CREATE);
        return !empty($logs) ? end($logs) : null; // Get the first (oldest) create log.
    }

    /**
     * Get all update audit logs for this record.
     *
     * @return audit_log[] Array of update audit log entries
     */
    public function get_update_audit_logs()
    {
        return $this->get_audit_logs_by_action(audit_log::ACTION_UPDATE);
    }

    /**
     * Get the deletion audit log for this record.
     *
     * @return audit_log|null The deletion audit log or null if not found
     */
    public function get_deletion_audit_log()
    {
        $logs = $this->get_audit_logs_by_action(audit_log::ACTION_DELETE);
        return !empty($logs) ? reset($logs) : null; // Get the most recent delete log.
    }

    /**
     * Gets the user who created.
     *
     * @return stdClass The user record of the creator.
     */
    public function get_creator()
    {
        global $DB;
        return $DB->get_record('user', ['id' => $this->get('usercreated')]);
    }

    /**
     * Gets the full name of the user who created.
     *
     * @return string The full name of the creator.
     */

    public function get_creator_name()
    {
        $user = $this->get_creator();
        return fullname($user);
    }

    /**
     * Gets the user who last modified.
     *
     * @return stdClass The user record of the modifier.
     */
    public function get_modifier()
    {
        global $DB;
        return $DB->get_record('user', ['id' => $this->get('usermodified')]);
    }

    /**
     * Gets the full name of the user who last modified.
     *
     * @return string The full name of the modifier.
     */
    public function get_modifier_name()
    {
        $user = $this->get_modifier();
        return fullname($user);
    }
}
