<template>
  <div id="offer-manager-view" class="offer-manager">
    <PageHeader title="Gerenciamento de ofertas">
      <template #actions>
        <div class="new-offer-container">
          <CustomButton
            variant="primary"
            icon="fa-solid fa-plus"
            label="Adicionar"
            @click="createNewOffer"
          />
        </div>
      </template>
    </PageHeader>

    <!-- Filtros -->
    <FilterSection title="FILTRO">
      <FilterRow :inline="true">
        <FilterGroup label="Oferta">
          <CustomInput
            v-model="inputFilters.search"
            placeholder="Buscar..."
            :width="339"
            :has-search-icon="true"
            @input="debouncedSearch"
          />
        </FilterGroup>

        <FilterGroup label="Tipo" v-if="typeOptionsEnabled">
          <CustomSelect
            v-model="inputFilters.type"
            :options="typeSelectOptions"
            :width="144"
            @update:modelValue="handleTypeChange"
          />
        </FilterGroup>

        <FilterGroup :is-checkbox="true">
          <CustomCheckbox
            v-model="inputFilters.hideInactive"
            id="hideInactive"
            label="Não exibir inativas"
            @update:modelValue="handleHideInactiveChange"
          />
        </FilterGroup>

        <FilterActions>
          <CustomButton
            variant="secondary"
            label="Limpar"
            @click="clearFilters"
          />
        </FilterActions>
      </FilterRow>
    </FilterSection>

    <!-- Mensagem de Erro -->
    <div class="alert alert-danger" v-if="error">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <!-- Tabela -->
    <div class="table-container">
      <CustomTable
        :headers="tableHeaders"
        :items="offers"
        :sort-by="sortBy"
        :sort-desc="sortDesc"
        @sort="handleTableSort"
      >
        <template #item-description="{ item }">
          <span :title="item.description">
            {{
              item.description.length > 50
                ? item.description.slice(0, 50) + "..."
                : item.description
            }}
          </span>
        </template>
        <template #item-type="{ item }">
          {{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }}
        </template>
        <template #item-status="{ item }">
          <span v-if="item.status === 1">
            <svg
              class="mr-1"
              width="22"
              height="23"
              viewBox="0 0 22 23"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="1"
                y="1.39999"
                width="20"
                height="20"
                rx="10"
                fill="white"
              />
              <rect
                x="1"
                y="1.39999"
                width="20"
                height="20"
                rx="10"
                stroke="var(--success)"
                stroke-width="2"
              />
              <path
                d="M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z"
                fill="var(--success)"
              />
            </svg>
            Ativa
          </span>
          <span v-else>
            <svg
              class="mr-1"
              width="22"
              height="23"
              viewBox="0 0 22 23"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_572_6021)">
                <rect
                  x="1"
                  y="1.39999"
                  width="20"
                  height="20"
                  rx="10"
                  fill="white"
                />
                <path
                  d="M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z"
                  fill="var(--danger)"
                />
              </g>
              <rect
                x="1"
                y="1.39999"
                width="20"
                height="20"
                rx="10"
                stroke="var(--danger)"
                stroke-width="2"
              />
              <defs>
                <clipPath id="clip0_572_6021">
                  <rect
                    x="1"
                    y="1.39999"
                    width="20"
                    height="20"
                    rx="10"
                    fill="white"
                  />
                </clipPath>
              </defs>
            </svg>
            Inativo
          </span>
        </template>
        <template #item-actions="{ item }">
          <div class="action-buttons">
            <button
              class="btn-action btn-edit"
              @click="navigateToShowOffer(item)"
              title="Visualizar"
            >
              <img src="@/assets/icons/file-search-fill.svg" />
            </button>

            <button
              class="btn-action btn-edit"
              @click="navigateToEditOffer(item)"
              title="Editar"
            >
              <i class="fas fa-pencil-alt"></i>
            </button>
            <button
              class="btn-action"
              :class="item.status === 1 ? 'btn-deactivate' : 'btn-activate'"
              @click="toggleOfferStatus(item)"
              :disabled="item.status === 0 && !item.can_activate"
              :title="getStatusButtonTitle(item)"
            >
              <template v-if="item.status === 1">
                <i class="fas fa-eye text-white"></i>
              </template>
              <template v-else>
                <i class="fas fa-eye-slash"></i>
              </template>
            </button>
            <button
              class="btn-action btn-delete"
              @click="deleteOffer(item)"
              :disabled="!item.can_delete"
              :title="
                item.can_delete
                  ? 'Excluir'
                  : 'Não é possível excluir esta oferta'
              "
            >
              <i class="fa fa-trash fa-fw"></i>
            </button>
          </div>
        </template>
      </CustomTable>
    </div>

    <!-- Paginação -->
    <Pagination
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="totalOffers"
      :loading="loading"
    />

    <!-- Modal de Confirmação de Exclusão -->
    <ConfirmationModal
      :show="showDeleteModal"
      size="md"
      title="A exclusão desta instância de oferta é uma ação irreversível."
      message="Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir Oferta"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteModal = false"
      @confirm="confirmDeleteOffer"
    />

    <!-- Modal de Confirmação de Alteração de Status -->
    <ConfirmationModal
      :show="showStatusModal"
      size="md"
      :title="
        selectedOffer?.status === 1
          ? 'Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedOffer?.status === 1
          ? ''
          : 'Tem certeza que deseja ativar esta oferta?'
      "
      :list-title="
        selectedOffer?.status === 1
          ? 'Comportamento para os cursos, turmas e matrículas:'
          : ''
      "
      :list-items="
        selectedOffer?.status === 1
          ? [
              'Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.',
              'Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.',
              'Novos alunos não poderão ser inscritos através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedOffer?.status === 1 ? 'Inativar oferta' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedOffer?.status === 1 ? 'warning' : 'question'"
      @close="showStatusModal = false"
      @confirm="confirmToggleStatus"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import { debounce } from "lodash";

// Importação dos services
import {
  fetchOffers,
  deleteOffer,
  toggleOfferStatus,
  getTypeOptions,
} from "@/services/offer";

// Importação dos componentes
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import CustomButton from "@/components/CustomButton.vue";
import FilterSection from "@/components/FilterSection.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterActions from "@/components/FilterActions.vue";
import LFLoading from "@/components/LFLoading.vue";
import Toast from "@/components/Toast.vue";

import Pagination from "@/components/Pagination.vue";
import PageHeader from "@/components/PageHeader.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";

export default {
  name: "OfferList",

  components: {
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomCheckbox,
    CustomButton,
    FilterSection,
    FilterRow,
    FilterGroup,
    FilterActions,
    Pagination,
    PageHeader,
    ConfirmationModal,
    LFLoading,
    Toast,
  },

  data() {
    return {
      // Filtros de entrada (não aplicados automaticamente)
      inputFilters: {
        search: "",
        type: "",
        hideInactive: false,
      },

      // Filtros aplicados (usados para filtragem real)
      appliedFilters: {
        search: "",
        type: "",
        hideInactive: false,
      },

      // Opções do select de tipo
      typeOptions: [],
      typeOptionsEnabled: false,

      // Configuração da tabela
      tableHeaders: [
        { text: "OFERTA", value: "name", sortable: true },
        { text: "STATUS", value: "status", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],

      // Estados de carregamento e erro
      offers: [],
      totalOffers: 0,
      loading: false,
      error: null,

      // Toast
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null, // Adicionar referência para o timeout

      // Paginação
      currentPage: 1,
      perPage: 10,

      // Ordenação
      sortBy: "name",
      sortDesc: false,

      // Modal de exclusão
      showDeleteModal: false,
      offerToDelete: null,

      // Modal de alteração de status
      showStatusModal: false,
      selectedOffer: null,
    };
  },

  computed: {
    typeSelectOptions() {
      return [{ value: "", label: "Todos" }, ...this.typeOptions];
    },

    hasActiveFilters() {
      return this.appliedFilters.search || this.appliedFilters.hideInactive;
    },
  },

  watch: {
    perPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentPage = 1;
        this.loadOffers();
      }
    },
    currentPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loadOffers();
      }
    },
  },

  async created() {
    // Criar a versão debounced da função de busca
    this.debouncedSearch = debounce(this.handleSearchInput, 300);

    // Inicializar
    this.loadTypeOptions();
    this.loadOffers();
  },

  methods: {
    getTypeLabel(typeValue) {
      if (!typeValue) return "";

      const option = this.typeOptions.find(
        (opt) => opt.value === typeValue || opt.label === typeValue
      );

      if (option) return option.label;
      return typeValue;
    },

    async loadTypeOptions() {
      const response = await getTypeOptions();

      if (response.types) {
        this.typeOptionsEnabled = response.enabled;

        if (response.default) {
          this.inputFilters.type = response.default;
        }

        this.typeOptions = response.types.map((type) => {
          return {
            value: type,
            label: type,
          };
        });
      }
    },

    async loadOffers() {
      try {
        this.loading = true;
        this.error = null;

        const params = {
          search: this.appliedFilters.search || "",
          type: this.appliedFilters.type || null,
          onlyActive: this.appliedFilters.hideInactive === true,
          page: this.currentPage,
          perPage: this.perPage,
          sortBy: this.sortBy,
          sortDesc: this.sortDesc,
        };

        const response = await fetchOffers(params);

        this.offers = response.offers || [];
        this.totalOffers = response.total_items || 0;
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async handlePageChange(page) {
      if (page !== this.currentPage) {
        this.currentPage = page;
        await this.loadOffers();
      }
    },

    async handlePerPageChange(newPerPage) {
      if (newPerPage !== this.perPage) {
        this.perPage = newPerPage;
        this.currentPage = 1;
        await this.loadOffers();
      }
    },

    async clearFilters() {
      // Preservar o tipo selecionado
      const currentType = this.inputFilters.type;

      this.inputFilters = {
        search: "",
        type: "",
        hideInactive: false,
      };

      this.appliedFilters = {
        search: "",
        type: "",
        hideInactive: false,
      };

      this.currentPage = 1;
      await this.loadOffers();
    },

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
      this.loadOffers();
    },

    createNewOffer() {
      this.$router.push({ name: "offer.create" });
    },

    navigateToEditOffer(offer) {
      this.$router.push({
        name: "offer.edit",
        params: { id: offer.id.toString() },
      });
    },

    navigateToShowOffer(offer) {
      this.$router.push({
        name: "offer.show",
        params: { id: offer.id.toString() },
      });
    },

    deleteOffer(offer) {
      if (!offer.can_delete) {
        return;
      }

      this.offerToDelete = offer;
      this.showDeleteModal = true;
    },

    async confirmDeleteOffer() {
      if (this.offerToDelete) {
        try {
          this.loading = true;
          await deleteOffer(this.offerToDelete.id);
          await this.loadOffers();

          // Exibir mensagem de sucesso
          this.showSuccessMessage(
            `Oferta "${this.offerToDelete.name}" excluída com sucesso`
          );

          this.offerToDelete = null;
          this.showDeleteModal = false;
        } catch (error) {
          this.error = error.message;
          // Exibir mensagem de erro
          this.showErrorMessage(
            `Erro ao excluir oferta "${this.offerToDelete.name}"`
          );
        } finally {
          this.loading = false;
        }
      }
    },

    toggleOfferStatus(offer) {
      if (offer.status === 0 && !offer.can_activate) {
        return;
      }

      this.selectedOffer = offer;
      this.showStatusModal = true;
    },

    async confirmToggleStatus() {
      if (this.selectedOffer) {
        try {
          this.loading = true;
          await toggleOfferStatus(
            this.selectedOffer.id,
            this.selectedOffer.status === 1
          );
          await this.loadOffers();

          // Exibir mensagem de sucesso
          this.showSuccessMessage(
            this.selectedOffer.status === 1
              ? `Oferta "${this.selectedOffer.name}" inativada com sucesso`
              : `Oferta "${this.selectedOffer.name}" inativada com sucesso`
          );

          this.selectedOffer = null;
          this.showStatusModal = false;
        } catch (error) {
          this.error = error.message;
          // Exibir mensagem de erro
          this.showErrorMessage(
            this.selectedOffer.status === 1
              ? `Erro ao inativar oferta "${this.selectedOffer.name}"`
              : `Erro ao ativar oferta "${this.selectedOffer.name}"`
          );
        } finally {
          this.loading = false;
        }
      }
    },

    getStatusButtonTitle(item) {
      if (item.status === 1) {
        return "Desativar";
      } else {
        return item.can_activate
          ? "Ativar"
          : "Não é possível ativar esta oferta";
      }
    },

    // Método para lidar com a mudança de tipo
    async handleTypeChange(value) {
      // Atualiza o filtro aplicado diretamente
      this.appliedFilters.type = value;

      // Reseta para a primeira página
      this.currentPage = 1;

      // Carrega as ofertas com o novo filtro
      await this.loadOffers();
    },

    // Método para lidar com a mudança do checkbox de inativas
    async handleHideInactiveChange(value) {
      // Garantir que value seja um booleano primitivo
      const boolValue = value === true;

      // Sincronizar os valores entre inputFilters e appliedFilters
      this.inputFilters.hideInactive = boolValue;
      this.appliedFilters.hideInactive = boolValue;

      // Reseta para a primeira página
      this.currentPage = 1;

      // Carrega as ofertas com o novo filtro
      await this.loadOffers();
    },

    // Método para lidar com a entrada de texto na pesquisa
    async handleSearchInput() {
      if (
        this.inputFilters.search.length >= 3 ||
        this.inputFilters.search === ""
      ) {
        // Atualiza o filtro aplicado diretamente
        this.appliedFilters.search = this.inputFilters.search;

        // Reseta para a primeira página
        this.currentPage = 1;

        // Carrega as ofertas com o novo filtro
        await this.loadOffers();
      }
    },

    /**
     * Exibe uma mensagem de sucesso usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      // Isso ajuda a reiniciar a animação corretamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000); // Usar a duração definida no componente Toast se necessário
      });
    },

    /**
     * Exibe uma mensagem de erro usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-activate {
  color: #6c757d;

  &:hover {
    background-color: rgba(108, 117, 125, 0.1);
  }

  i {
    opacity: 0.7;
  }
}

.btn-deactivate {
  color: #fff;
}

.table-container {
  position: relative;
  margin-bottom: 1rem;
  min-height: 400px;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &:not(:empty) {
      opacity: 1;
      visibility: visible;
    }

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2rem;
      padding: 3rem;
      border-radius: 12px;
      background-color: rgba(33, 37, 41, 0.98);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      min-width: 240px;
    }

    .loading-text {
      color: #fff;
      font-size: 1.125rem;
      font-weight: 500;
      letter-spacing: 0.01em;
      text-align: center;
      margin: 0;
      padding: 0;
    }

    .spinner-border {
      width: 3.5rem;
      height: 3.5rem;
      border: 0.3rem solid rgba(255, 255, 255, 0.2);
      border-top-color: #fff;
      border-radius: 50%;
      animation: spin 0.8s ease-in-out infinite;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &.alert-danger {
    background-color: #2c0b0e;
    border: 1px solid #842029;
    color: #ea868f;
  }

  i {
    font-size: 1.25rem;
  }
}
</style>

<style lang="scss">
.table-container {
  table {
    tr {
      th {
        &[data-value="actions"] {
          text-align: center !important;
        }
      }
    }
  }
}
</style>
