<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Library functions for local_audit plugin.
 *
 * @package    local_audit
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Check if audit logging is enabled.
 *
 * @return bool True if audit logging is enabled
 */
function local_audit_is_enabled() {
    return get_config('local_audit', 'enableaudit');
}

/**
 * Get the audit log retention period in days.
 *
 * @return int Number of days to retain audit logs (0 = indefinite)
 */
function local_audit_get_retention_days() {
    return (int) get_config('local_audit', 'auditretention');
}

/**
 * Log an audit action manually (for non-persistent classes).
 *
 * @param string $persistent The persistent class name or table name
 * @param int $recordid The record ID
 * @param string $action The action performed
 * @param array|object|null $newdata New data
 * @param array|object|null $olddata Old data
 * @param int|null $userid User ID (defaults to current user)
 * @param string|null $ipaddress IP address (defaults to current user's IP)
 * @return \local_audit\persistent\audit_log|null The created audit log entry or null if disabled
 */
function local_audit_log_action($persistent, $recordid, $action, $newdata = null, $olddata = null, $userid = null, $ipaddress = null) {
    return \local_audit\persistent\audit_log::log_action($persistent, $recordid, $action, $newdata, $olddata, $userid, $ipaddress);
}

/**
 * Get audit logs for a specific record.
 *
 * @param string $persistent The persistent class name or table name
 * @param int $recordid The record ID
 * @param string|null $action Filter by action (optional)
 * @return \local_audit\persistent\audit_log[] Array of audit log entries
 */
function local_audit_get_logs_for_record($persistent, $recordid, $action = null) {
    return \local_audit\persistent\audit_log::get_logs_for_record($persistent, $recordid, $action);
}

/**
 * Get audit logs for a specific user.
 *
 * @param int $userid The user ID
 * @param int $limit Limit number of results (optional)
 * @return \local_audit\persistent\audit_log[] Array of audit log entries
 */
function local_audit_get_logs_for_user($userid, $limit = 0) {
    return \local_audit\persistent\audit_log::get_logs_for_user($userid, $limit);
}

/**
 * Clean up old audit logs.
 *
 * @return int Number of records deleted
 */
function local_audit_cleanup_old_logs() {
    return \local_audit\persistent\audit_log::cleanup_old_logs();
}
