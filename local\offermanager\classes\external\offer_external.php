<?php

// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\external;

use core_date;
use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_value;
use core_external\external_single_structure;
use core_external\external_multiple_structure;
use DateTime;
use local_offermanager\enrol_setup;
use local_offermanager\persistent\offer_model;
use local_offermanager\settings_utils;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_external
 *
 * @package    local_offermanager
 * @copyright  2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_external extends external_api
{
    /**
     * Define os parâmetros de entrada da função fetch_offers.
     *
     * @return external_function_parameters
     */
    public static function fetch_parameters()
    {
        return new external_function_parameters([
            'search_string' => new external_value(PARAM_TEXT, 'Texto para pesquisa', VALUE_DEFAULT, ''),
            'type' => new external_value(PARAM_TEXT, 'Tipo da oferta', VALUE_DEFAULT, null),
            'only_active' => new external_value(PARAM_BOOL, 'Se deve buscar apenas ativos', VALUE_DEFAULT, false),
            'page' => new external_value(PARAM_INT, 'Número da página', VALUE_DEFAULT, 1),
            'per_page' => new external_value(PARAM_INT, 'Número de itens por página', VALUE_DEFAULT, 25),
            'sort_by' => new external_value(PARAM_TEXT, 'Campo para ordenação', VALUE_DEFAULT, 'name'),
            'sort_direction' => new external_value(PARAM_TEXT, 'Ordem da ordenação', VALUE_DEFAULT, 'ASC')
        ]);
    }

    /**
     * Executa a busca das ofertas com base nos parâmetros recebidos.
     *
     * @param string $search_string
     * @param string|null $type
     * @param bool $only_active
     * @param int $page
     * @param int $per_page
     * @param string $sort_by
     * @param string $sort_direction
     * @return array
     */
    public static function fetch($search_string, $type, $only_active, $page, $per_page, $sort_by, $sort_direction)
    {
        self::validate_parameters(self::fetch_parameters(), compact('search_string', 'type', 'only_active', 'page', 'per_page', 'sort_by', 'sort_direction'));

        $offers = offer_model::fetch($search_string, $type, $only_active, $page - 1, $per_page, $sort_by, $sort_direction);

        $total = offer_model::count_offers($search_string, $type, $only_active);

        $total_pages = ceil($total / $per_page);

        $offers = array_map(function ($offer) {
            return [
                'id' => $offer->get('id'),
                'name' => $offer->get('name'),
                'description' => $offer->get('description'),
                'type' => $offer->get('type'),
                'status' => $offer->get('status'),
                'can_activate' => $offer->can_activate(),
                'can_delete' => $offer->can_delete(),
                'usercreated' => $offer->get('usercreated'),
            ];
        }, $offers);

        return [
            'page' => $page + 1,
            'per_page' => $per_page,
            'total_pages' => $total_pages,
            'total_items' => $total,
            'offers' => $offers
        ];
    }

    /**
     * Define a estrutura de retorno da função fetch_offers.
     *
     * @return external_multiple_structure
     */
    public static function fetch_returns()
    {
        return
            new external_single_structure([
                'page' => new external_value(PARAM_INT, 'Pagina atual'),
                'per_page' => new external_value(PARAM_INT, 'Quantidade por página'),
                'total_pages' => new external_value(PARAM_INT, 'Total de páginas'),
                'total_items' => new external_value(PARAM_INT, 'Total de itens'),
                'offers' => new external_multiple_structure(
                    new external_single_structure([
                        'id' => new external_value(PARAM_INT, 'ID da oferta'),
                        'name' => new external_value(PARAM_TEXT, 'Nome da oferta'),
                        'description' => new external_value(PARAM_TEXT, 'Descrição da oferta'),
                        'type' => new external_value(PARAM_TEXT, 'Tipo da oferta'),
                        'status' => new external_value(PARAM_INT, 'Status da oferta'),
                        'can_activate' => new external_value(PARAM_BOOL, 'Status da oferta'),
                        'can_delete' => new external_value(PARAM_BOOL, 'Status da oferta'),
                        'usercreated' => new external_value(PARAM_INT, 'ID do usuário que criou'),
                    ])
                )
            ]);
    }

    /**
     * Define os parâmetros para obter uma oferta pelo ID.
     *
     * @return external_function_parameters
     */
    public static function get_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID da oferta')
        ]);
    }

    /**
     * Retorna os dados de uma oferta pelo ID.
     *
     * @param int $id
     * @return array
     * @throws moodle_exception
     */
    public static function get($id)
    {
        self::validate_parameters(self::get_parameters(), compact('id'));

        $offer = new offer_model($id);

        if (!$offer->get('id')) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $datetime = new DateTime('now', core_date::get_user_timezone_object());

        $datetime->setTimestamp($offer->get('timecreated'));
        $createddate = $datetime->format('d/m/Y - H:i');

        $datetime->setTimestamp($offer->get('timemodified'));
        $modifieddate = $datetime->format('d/m/Y - H:i');

        return [
            'id' => $offer->get('id'),
            'name' => $offer->get('name'),
            'description' => $offer->get('description'),
            'type' => $offer->get('type'),
            'status' => $offer->get('status'),
            'created_by' => $offer->get('usercreated'),
            'audiences' => $offer->get_audience_ids(),
            'creatorname' => $offer->get_creator_name(),
            'modifiername' => $offer->get_modifier_name(),
            'createddate' => $createddate,
            'modifieddate' => $modifieddate
        ];
    }

    /**
     * Define a estrutura de retorno da função get_offer_by_id.
     *
     * @return external_single_structure
     */
    public static function get_returns()
    {
        return new external_single_structure([
            'id' => new external_value(PARAM_INT, 'ID da oferta'),
            'name' => new external_value(PARAM_TEXT, 'Nome da oferta'),
            'description' => new external_value(PARAM_TEXT, 'Descrição da oferta'),
            'type' => new external_value(PARAM_TEXT, 'Tipo da oferta'),
            'status' => new external_value(PARAM_INT, 'Status da oferta'),
            'created_by' => new external_value(PARAM_INT, 'ID do criador da oferta'),
            'audiences' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do público-alvo'),
                'Lista de IDs dos públicos-alvo'
            ),
            'creatorname' => new external_value(PARAM_TEXT, 'Nome do usuário que criou a oferta'),
            'modifiername' => new external_value(PARAM_TEXT, 'Nome do usuário que atualizou a oferta'),
            'createddate' => new external_value(PARAM_TEXT, 'Data de criação da oferta'),
            'modifieddate' => new external_value(PARAM_TEXT, 'Data de atualização da oferta')
        ]);
    }

    /**
     * Define os parâmetros de entrada da função.
     *
     * @return external_function_parameters
     */
    public static function save_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID da oferta (0 para criar, outro número para atualizar)', VALUE_DEFAULT, 0),
            'name' => new external_value(PARAM_TEXT, 'Nome da oferta'),
            'description' => new external_value(PARAM_TEXT, 'Descrição da oferta', VALUE_DEFAULT, ''),
            'type' => new external_value(PARAM_TEXT, 'Tipo da oferta', VALUE_DEFAULT, ''),
            'audienceids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do público-alvo'),
                'Lista de IDs dos públicos-alvo',
                VALUE_DEFAULT,
                []
            ),
            'status' => new external_value(PARAM_INT, 'Status da oferta', VALUE_DEFAULT, 0),
        ]);
    }

    /**
     * Cria ou atualiza uma oferta.
     *
     * @param int $id
     * @param string $name
     * @param string $description
     * @param string|null $type
     * @param int[] $audienceids
     * @return array
     * @throws moodle_exception
     */
    public static function save($id, $name, $description, $type, $audienceids, $status = 0)
    {
        global $USER;

        self::validate_parameters(
            self::save_parameters(),
            compact('id', 'name', 'description', 'type', 'audienceids')
        );

        if ($id) {
            $offer = new offer_model($id);

            if (!$offer->get('id')) {
                throw new moodle_exception('error:offer_not_found', 'local_offermanager');
            }
        } else {
            $hasoffer = offer_model::get_record(['name' => $name]);

            if ($hasoffer) {
                throw new moodle_exception('error:offer_exists', 'local_offermanager', '', $name);
            }

            $offer = new offer_model(0);
        }

        $offer->set('name', $name);
        $offer->set('description', $description);
        $offer->set('type', $type);
        $offer->set('status', $status);

        $offer->save();

        $message = get_string($id ? 'offer_updated' : 'offer_created', 'local_offermanager');

        $audience_updated = $offer->update_audiences($audienceids);

        return [
            'id' => $offer->get('id'),
            'message' => $message,
            'audiences_updated' => $audience_updated
        ];
    }

    /**
     * Define a estrutura de saída da função.
     *
     * @return external_single_structure
     */
    public static function save_returns()
    {
        return new external_single_structure([
            'id' => new external_value(PARAM_INT, 'ID da oferta criada ou atualizada'),
            'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
            'audiences_updated' => new external_value(PARAM_BOOL, 'Bool para confirmar alterações nos público-alvo'),
        ]);
    }

    /**
     * Define os parâmetros para excluir uma oferta pelo ID.
     *
     * @return external_function_parameters
     */
    public static function delete_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID da oferta a ser excluída')
        ]);
    }

    /**
     * Exclui uma oferta pelo ID.
     *
     * @param int $id
     * @return array
     * @throws moodle_exception
     */
    public static function delete($id)
    {
        self::validate_parameters(self::delete_parameters(), compact('id'));

        $offer = new offer_model($id);
        if (!$offer->get('id')) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        if (!$offer->can_delete()) {
            throw new moodle_exception('error:cannot_delete_offer', 'local_offermanager');
        }

        $offer->delete();

        return [
            'id' => $id,
            'message' => get_string('offer_deleted', 'local_offermanager'),
        ];
    }

    /**
     * Define a estrutura de retorno da função delete_offer.
     *
     * @return external_single_structure
     */
    public static function delete_returns()
    {
        return new external_single_structure([
            'id' => new external_value(PARAM_INT, 'ID da oferta excluída'),
            'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
        ]);
    }

    /**
     * Define os parâmetros da função get_types.
     *
     * @return external_function_parameters
     */
    public static function get_types_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'search_string' => new external_value(
                PARAM_TEXT,
                'String de busca para filtrar os tipos de ofertas.',
                VALUE_DEFAULT,
                ''
            ),
            'only_active' => new external_value(
                PARAM_BOOL,
                'Booleano para buscar apenas os tipos de ofertas em ofertas ativas.',
                VALUE_DEFAULT,
                false
            ),
        ]);
    }

    /**
     * Retorna os tipos de ofertas com base em uma string de busca.
     *
     * @param string $search_string String de busca (opcional).
     * @param bool $only_active Busca entre ofertas ativas (opcional);
     * @return array Lista de tipos de ofertas.
     */
    public static function get_types(string $search_string = '', bool $only_active = false): array
    {
        $params = self::validate_parameters(
            self::get_types_parameters(),
            [
                'search_string' => $search_string,
                'only_active' => $only_active
            ]
        );

        return offer_model::fetch_types($params['search_string'], $params['only_active']);
    }

    /**
     * Define a estrutura de retorno da função get_types.
     *
     * @return external_multiple_structure
     */
    public static function get_types_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_value(PARAM_TEXT, 'Tipo de oferta')
        );
    }

    /**
     * Define os parâmetros de entrada da função (neste caso, nenhum).
     *
     * @return external_function_parameters
     */
    public static function get_type_options_parameters(): external_function_parameters
    {
        return new external_function_parameters([]);
    }

    /**
     * Retorna os tipos de oferta e o valor padrão.
     *
     * @return array
     */
    public static function get_type_options(): array
    {
        return [
            'enabled' => !!get_config('local_offermanager', 'enabletypeoptions'),
            'types' => settings_utils::get_type_options(),
            'default' => settings_utils::get_type_default(),
        ];
    }

    /**
     * Define a estrutura de retorno da função.
     *
     * @return external_single_structure
     */
    public static function get_type_options_returns(): external_single_structure
    {
        return new external_single_structure([
            'enabled' => new external_value(PARAM_BOOL, 'Opções de tipos habilitado'),
            'types' => new external_multiple_structure(
                new external_value(PARAM_TEXT, 'Tipo de oferta')
            ),
            'default' => new external_value(PARAM_TEXT, 'Valor padrão'),
        ]);
    }

    /**
     * Define os parâmetros para alterar o status de uma oferta.
     *
     * @return external_function_parameters
     */
    public static function set_status_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID da oferta a ser alterada'),
            'status' => new external_value(PARAM_BOOL, 'Status a ser alterado. 0 para desabilitar e 1 para habilitar')
        ]);
    }

    /**
     * Alterar o status de uma oferta.
     *
     * @param int $id
     * @param bool $status
     * @return array
     * @throws moodle_exception
     */
    public static function set_status($id, $status)
    {
        self::validate_parameters(
            self::set_status_parameters(),
            compact('id', 'status')
        );

        $offer = offer_model::get_record(
            [
                'id' => $id
            ]
        );

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $current_status = (bool) $offer->get('status');

        if ($status == $current_status) {
            $message_key = $status
                ? 'error:offer_already_active'
                : 'error:offer_already_inactive';

            throw new moodle_exception($message_key, 'local_offermanager');
        }

        if ($status && !$offer->can_activate()) {
            throw new moodle_exception('error:cannot_activate_offer', 'local_offermanager');
        }

        $status
            ? $offer->activate()
            : $offer->inactivate();

        $message_key = $status
            ? 'event:offeractivated'
            : 'event:offerinactivated';

        return [
            'status' => $offer->get('status'),
            'message' => get_string($message_key, 'local_offermanager'),
        ];
    }

    /**
     * Define a estrutura de retorno da função set_status.
     *
     * @return external_single_structure
     */
    public static function set_status_returns()
    {
        return new external_single_structure([
            'status' => new external_value(PARAM_BOOL, 'O status atual'),
            'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
        ]);
    }

    /**
     * Retorna os métodos de inscrição disponíveis que dependem do local_offermanager
     */
    public static function get_class_methods_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'enabled' => new external_value(PARAM_BOOL, 'Retornar apenas plugins habilitados', VALUE_DEFAULT, true)
        ]);
    }

    /**
     * Executa a consulta dos métodos de inscrição.
     *
     * @param bool $enabled Indica se deve retornar apenas plugins habilitados (padrão: true).
     * @return array Retorna um array com os plugins de inscrição disponíveis.
     *               Cada elemento contém:
     *               - pluginname: Nome curto do plugin (sem o prefixo 'enrol_').
     *               - name: Nome legível do plugin.
     */
    public static function get_class_methods($enabled = true): array
    {
        $params = self::validate_parameters(
            self::get_class_methods_parameters(),
            ['enabled' => $enabled]
        );

        $plugins = enrol_setup::get_dependent_enrol_plugins($params['enabled']);
        $result = [];

        foreach ($plugins as $pluginname) {
            $shortname = str_replace('enrol_', '', $pluginname);

            if ($shortname) {
                $result[] = [
                    'enrol' => $shortname,
                    'name' => get_string('pluginname', 'enrol_' . $shortname)
                ];
            }
        }

        return $result;
    }

    /**
     * Define a estrutura de retorno
     */
    public static function get_class_methods_returns()
    {
        return new external_multiple_structure(
            new external_single_structure([
                'enrol' => new external_value(PARAM_PLUGIN, 'Nome técnico do plugin'),
                'name' => new external_value(PARAM_TEXT, 'Nome legível do método de inscrição')
            ])
        );
    }
}
