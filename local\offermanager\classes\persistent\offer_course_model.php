<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use local_offermanager\persistent\trait\course_class_trait;
use local_offermanager\persistent\trait\course_course_trait;
use local_offermanager\persistent\trait\course_enrolment_trait;
use local_offermanager\event\offer_course_created;
use local_offermanager\event\offer_course_deleted;
use local_offermanager\event\offer_course_activated;
use local_offermanager\event\offer_course_inactivated;
use local_offermanager\constants;
use local_offermanager\enrol_setup;
use moodle_exception;
use dml_exception;
use local_audit\traits\audit_trait;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_course_model
 *
 * @package    local_offermanager
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_course_model extends persistent
{
    use audit_trait;
    use course_class_trait;
    use course_course_trait;
    use course_enrolment_trait;

    const TABLE = 'local_offermanager_course';

    protected static function define_properties()
    {
        return [
            'offerid' => [
                'type' => PARAM_INT,
                'description' => 'ID da oferta',
            ],
            'courseid' => [
                'type' => PARAM_INT,
                'description' => 'ID do curso',
            ],
            'status' => [
                'type' => PARAM_INT,
                'description' => 'Status da oferta',
                'choice' => [constants::OFFER_STATUS_INACTIVE, constants::OFFER_STATUS_ACTIVE],
                'null' => NULL_NOT_ALLOWED,
                'default' => constants::OFFER_STATUS_ACTIVE
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id do creador',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;

                    return $USER->id;
                }
            ]
        ];
    }

    /**
     * Valida o campo offerid.
     *
     * @return bool
     */
    protected function validate_offerid($value): bool
    {
        if (!offer_model::record_exists($value)) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Valida o campo courseid.
     *
     * @param int $value ID do curso.
     * @return bool
     */
    protected function validate_courseid($value): bool
    {
        try {
            $course = get_course($value);
            return !!$course;
        } catch (dml_exception $e) {
            throw new moodle_exception('error:course_not_found', 'local_offermanager');
            return false;
        }
    }

    /**
     * Valida a combinação única de offerid e courseid.
     *
     * @return bool
     */
    protected function validate_unique_combination(): bool
    {
        $params = [
            'offerid' => $this->get('offerid'),
            'courseid' => $this->get('courseid'),
        ];

        $query = 'offerid = :offerid AND courseid = :courseid';

        if ($this->get('id')) {

            $params['id'] = $this->get('id');
            $query .= ' AND id <> :id';
        }

        return !self::record_exists_select($query, $params);
    }

    /**
     * Retorna a oferta associada a este registro.
     *
     * @return offer_model
     */
    public function get_offer()
    {
        return new offer_model($this->get('offerid'));
    }

    /**
     * Ativa a relação entre oferta e curso se as condições forem atendidas.
     *
     * @return bool
     * @throws moodle_exception
     */
    public function activate(): bool
    {
        if ($this->get('status') === constants::OFFER_STATUS_ACTIVE) {
            throw new moodle_exception('error:offer_course_already_active', 'local_offermanager');
        }

        $this->enable_enrol_instances();

        $this->set('status', constants::OFFER_STATUS_ACTIVE);
        $this->save();

        return $this->get('status') == constants::OFFER_STATUS_ACTIVE;
    }

    /**
     * Desativa a relação entre oferta e curso.
     *
     * @return bool
     * @throws moodle_exception
     */
    public function inactivate(): bool
    {
        if ($this->get('status') === constants::OFFER_STATUS_INACTIVE) {
            throw new moodle_exception('error:offer_course_already_inactive', 'local_offermanager');
        }

        $this->set('status', constants::OFFER_STATUS_INACTIVE);

        $this->save();

        return $this->get('status') == constants::OFFER_STATUS_INACTIVE;
    }

    public function can_delete()
    {
        return !$this->has_enrol_instances() || !$this->has_user_enrolments();
    }

    public function can_activate()
    {
        return true;
    }

    /**
     * Verifica se a instância está ativa.
     *
     * @return bool
     */
    public function is_active()
    {
        $offer = $this->get_offer();
        return $this->get('status') == constants::OFFER_STATUS_ACTIVE && $offer->is_active();
    }

    protected function before_validate()
    {
        if (!$this->validate_unique_combination()) {
            throw new moodle_exception('error:duplicate_offer_course', 'local_offermanager');
        }
    }

    protected function after_create()
    {
        $event = offer_course_created::instance($this);
        $event->trigger();
    }

    protected function before_update()
    {
        global $DB;

        $table_status = $DB->get_field(
            self::TABLE,
            'status',
            [
                'id' => $this->get('id')
            ]
        );

        $instance_status = $this->get('status');

        if ($table_status != $instance_status) {

            $event =  $instance_status == constants::OFFER_STATUS_ACTIVE
                ? offer_course_activated::instance($this)
                : offer_course_inactivated::instance($this);

            $event->trigger();
        }
    }

    protected function before_delete()
    {
        if (!$this->can_delete()) {
            throw new moodle_exception('error:cannot_delete_offer_course', 'local_offermanager');
        }

        $classes = $this->get_classes();

        if ($classes) {
            foreach ($classes as $class) {
                $class->delete_instance();
            }
        }

        $event = offer_course_deleted::instance($this);
        $event->trigger();
    }
}
